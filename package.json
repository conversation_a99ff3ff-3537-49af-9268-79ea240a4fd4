{"name": "cms", "version": "1.0.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "wang <bj<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:dev": "vue-cli-service build --mode dev", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:hainan": "vue-cli-service build --mode hainan", "build:test": "vue-cli-service build --mode test", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"axios": "0.19.0", "clipboard": "2.0.4", "dropzone": "5.5.1", "echarts": "^4.6.0", "element-ui": "2.11.1", "file-saver": "2.0.2", "fuse.js": "3.4.5", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "medium-editor": "^5.23.3", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-to-regexp": "3.0.0", "print-js": "^1.0.61", "qrcode": "^1.5.1", "querystring": "^0.2.0", "screenfull": "4.2.1", "showdown": "1.9.0", "tim-js-sdk": "^2.9.3", "tim-upload-plugin": "^1.0.1", "vant": "^2.5.3", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-echarts": "^5.0.0-beta.0", "vue-pdf": "^4.0.8", "vue-router": "3.0.7", "vue-splitpane": "1.0.4", "vue2-medium-editor": "^1.1.6", "vuedraggable": "^2.23.2", "vuex": "3.1.1", "webpack": "^4.41.6"}, "devDependencies": {"@babel/core": "7.5.5", "@babel/register": "7.5.5", "@vue/cli-plugin-babel": "3.9.2", "@vue/cli-plugin-eslint": "3.9.2", "@vue/cli-plugin-unit-jest": "3.9.0", "@vue/cli-service": "3.9.3", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-eslint": "8.0.1", "babel-jest": "24.8.0", "babel-plugin-dynamic-import-node": "^2.3.0", "chalk": "2.4.2", "chokidar": "3.0.2", "connect": "3.7.0", "eslint": "6.1.0", "eslint-plugin-vue": "5.2.3", "html-webpack-plugin": "3.2.0", "husky": "3.0.2", "less-loader": "^6.2.0", "lint-staged": "9.2.1", "minimist": "^1.2.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.12.0", "paho-mqtt": "^1.1.0", "plop": "2.4.0", "prettier": "^1.18.2", "runjs": "^4.4.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "^2.1.4", "script-loader": "0.7.2", "serve-static": "^1.14.1", "svg-sprite-loader": "^4.1.6", "svgo": "1.3.0", "trtc-js-sdk": "^4.8.5", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}