!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Chatroom=t():e.Chatroom=t()}(window,function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=421)}([function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=n(81),a=n(75);n(105);var c,u,l=n(15),m=l.getGlobal(),p=/\s+/;l.deduplicate=function(e){var t=[];return e.forEach(function(e){-1===t.indexOf(e)&&t.push(e)}),t},l.capFirstLetter=function(e){return e?(e=""+e).slice(0,1).toUpperCase()+e.slice(1):""},l.guid=(c=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return c()+c()+c()+c()+c()+c()+c()+c()}),l.extend=function(e,t,n){for(var r in t)void 0!==e[r]&&!0!==n||(e[r]=t[r])},l.filterObj=function(e,t){var n={};return l.isString(t)&&(t=t.split(p)),t.forEach(function(t){e.hasOwnProperty(t)&&(n[t]=e[t])}),n},l.copy=function(e,t){return t=t||{},e?(Object.keys(e).forEach(function(n){l.exist(e[n])&&(t[n]=e[n])}),t):t},l.copyWithNull=function(e,t){return t=t||{},e?(Object.keys(e).forEach(function(n){(l.exist(e[n])||l.isnull(e[n]))&&(t[n]=e[n])}),t):t},l.findObjIndexInArray=function(e,t){e=e||[];var n=t.keyPath||"id",r=-1;return e.some(function(e,o){if(a(e,n)===t.value)return r=o,!0}),r},l.findObjInArray=function(e,t){var n=l.findObjIndexInArray(e,t);return-1===n?null:e[n]},l.mergeObjArray=function(){var e=[],t=[].slice.call(arguments,0,-1),n=arguments[arguments.length-1];l.isArray(n)&&(t.push(n),n={});var r,o=n.keyPath=n.keyPath||"id";for(n.sortPath=n.sortPath||o;!e.length&&t.length;)e=(e=t.shift()||[]).slice(0);return t.forEach(function(t){t&&t.forEach(function(t){-1!==(r=l.findObjIndexInArray(e,{keyPath:o,value:a(t,o)}))?e[r]=l.merge({},e[r],t):e.push(t)})}),n.notSort||(e=l.sortObjArray(e,n)),e},l.cutObjArray=function(e){var t=e.slice(0),n=arguments.length,r=[].slice.call(arguments,1,n-1),o=arguments[n-1];l.isObject(o)||(r.push(o),o={});var i,s=o.keyPath=o.keyPath||"id";return r.forEach(function(e){l.isArray(e)||(e=[e]),e.forEach(function(e){e&&(o.value=a(e,s),-1!==(i=l.findObjIndexInArray(t,o))&&t.splice(i,1))})}),t},l.sortObjArray=function(e,t){var n=(t=t||{}).sortPath||"id";s.insensitive=!!t.insensitive;var r,o,i,c=!!t.desc;return i=l.isFunction(t.compare)?t.compare:function(e,t){return r=a(e,n),o=a(t,n),c?s(o,r):s(r,o)},e.sort(i)},l.emptyFunc=function(){},l.isEmptyFunc=function(e){return e===l.emptyFunc},l.notEmptyFunc=function(e){return e!==l.emptyFunc},l.splice=function(e,t,n){return[].splice.call(e,t,n)},l.reshape2d=function(e,t){if(Array.isArray(e)){l.verifyParamType("type",t,"number","util::reshape2d");var n=e.length;if(n<=t)return[e];for(var r=Math.ceil(n/t),o=[],i=0;i<r;i++)o.push(e.slice(i*t,(i+1)*t));return o}return e},l.flatten2d=function(e){if(Array.isArray(e)){var t=[];return e.forEach(function(e){t=t.concat(e)}),t}return e},l.dropArrayDuplicates=function(e){if(Array.isArray(e)){for(var t={},n=[];e.length>0;){t[e.shift()]=!0}for(var r in t)!0===t[r]&&n.push(r);return n}return e},l.onError=function(e){throw new function(e){"object"===(void 0===e?"undefined":(0,i.default)(e))?(this.callFunc=e.callFunc||null,this.message=e.message||"UNKNOW ERROR"):this.message=e,this.time=new Date,this.timetag=+this.time}(e)},l.verifyParamPresent=function(e,t,n,r){n=n||"";var o=!1;switch(l.typeOf(t)){case"undefined":case"null":o=!0;break;case"string":""===t&&(o=!0);break;case"StrStrMap":case"object":Object.keys(t).length||(o=!0);break;case"array":t.length?t.some(function(e){if(l.notexist(e))return o=!0,!0}):o=!0}o&&l.onParamAbsent(n+e,r)},l.onParamAbsent=function(e,t){l.onParamError("缺少参数 "+e+", 请确保参数不是 空字符串、空对象、空数组、null或undefined, 或数组的内容不是 null/undefined",t)},l.verifyParamAbsent=function(e,t,n,r){n=n||"",void 0!==t&&l.onParamPresent(n+e,r)},l.onParamPresent=function(e,t){l.onParamError("多余的参数 "+e,t)},l.verifyParamType=function(e,t,n,r){var o=l.typeOf(t).toLowerCase();l.isArray(n)||(n=[n]);var i=!0;switch(-1===(n=n.map(function(e){return e.toLowerCase()})).indexOf(o)&&(i=!1),o){case"number":isNaN(t)&&(i=!1);break;case"string":"numeric or numeric string"===n.join("")&&(i=!!/^[0-9]+$/.test(t))}i||l.onParamInvalidType(e,n,"",r)},l.onParamInvalidType=function(e,t,n,r){n=n||"",t=l.isArray(t)?(t=t.map(function(e){return'"'+e+'"'})).join(", "):'"'+t+'"',l.onParamError('参数"'+n+e+'"类型错误, 合法的类型包括: ['+t+"]",r)},l.verifyParamValid=function(e,t,n,r){l.isArray(n)||(n=[n]),-1===n.indexOf(t)&&l.onParamInvalidValue(e,n,r)},l.onParamInvalidValue=function(e,t,n){l.isArray(t)||(t=[t]),t=t.map(function(e){return'"'+e+'"'}),l.isArray(t)&&(t=t.join(", ")),l.onParamError("参数 "+e+"值错误, 合法的值包括: ["+JSON.stringify(t)+"]",n)},l.verifyParamMin=function(e,t,n,r){t<n&&l.onParamError("参数"+e+"的值不能小于"+n,r)},l.verifyParamMax=function(e,t,n,r){t>n&&l.onParamError("参数"+e+"的值不能大于"+n,r)},l.verifyArrayMax=function(e,t,n,r){t.length>n&&l.onParamError("参数"+e+"的长度不能大于"+n,r)},l.verifyEmail=(u=/^\S+@\S+$/,function(e,t,n){u.test(t)||l.onParamError("参数"+e+"邮箱格式错误, 合法格式必须包含@符号, @符号前后至少要各有一个字符",n)}),l.verifyTel=function(){var e=/^[+\-()\d]+$/;return function(t,n,r){e.test(n)||l.onParamError("参数"+t+"电话号码格式错误, 合法字符包括+、-、英文括号和数字",r)}}(),l.verifyBirth=function(){var e=/^(\d{4})-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/;return function(t,n,r){e.test(n)||l.onParamError("参数"+t+'生日格式错误, 合法为"yyyy-MM-dd"',r)}}(),l.onParamError=function(e,t){l.onError({message:e,callFunc:t})},l.verifyOptions=function(e,t,n,r,o){if(e=e||{},t&&(l.isString(t)&&(t=t.split(p)),l.isArray(t))){"boolean"!=typeof n&&(o=n||null,n=!0,r="");var i=n?l.verifyParamPresent:l.verifyParamAbsent;t.forEach(function(t){i.call(l,t,e[t],r,o)})}return e},l.verifyParamAtLeastPresentOne=function(e,t,n){t&&(l.isString(t)&&(t=t.split(p)),l.isArray(t)&&(t.some(function(t){return l.exist(e[t])})||l.onParamError("以下参数["+t.join(", ")+"]至少需要传入一个",n)))},l.verifyParamPresentJustOne=function(e,t,n){t&&(l.isString(t)&&(t=t.split(p)),l.isArray(t)&&1!==t.reduce(function(t,n){return l.exist(e[n])&&t++,t},0)&&l.onParamError("以下参数["+t.join(", ")+"]必须且只能传入一个",n))},l.verifyBooleanWithDefault=function(e,t,n,r,o){l.undef(n)&&(n=!0),p.test(t)&&(t=t.split(p)),l.isArray(t)?t.forEach(function(t){l.verifyBooleanWithDefault(e,t,n,r,o)}):void 0===e[t]?e[t]=n:l.isBoolean(e[t])||l.onParamInvalidType(t,"boolean",r,o)},l.verifyFileInput=function(e,t){return l.verifyParamPresent("fileInput",e,"",t),l.isString(e)&&((e="undefined"==typeof document?void 0:document.getElementById(e))||l.onParamError("找不到要上传的文件对应的input, 请检查fileInput id "+e,t)),e.tagName&&"input"===e.tagName.toLowerCase()&&"file"===e.type.toLowerCase()||l.onParamError("请提供正确的 fileInput, 必须为 file 类型的 input 节点 tagname:"+e.tagName+", filetype:"+e.type,t),e},l.verifyFileType=function(e,t){l.verifyParamValid("type",e,l.validFileTypes,t)},l.verifyCallback=function(e,t,n){p.test(t)&&(t=t.split(p)),l.isArray(t)?t.forEach(function(t){l.verifyCallback(e,t,n)}):e[t]?l.isFunction(e[t])||l.onParamInvalidType(t,"function","",n):e[t]=l.emptyFunc},l.verifyFileUploadCallback=function(e,t){l.verifyCallback(e,"uploadprogress uploaddone uploaderror uploadcancel",t)},l.validFileTypes=["image","audio","video","file"],l.validFileExts={image:["bmp","gif","jpg","jpeg","jng","png","webp"],audio:["mp3","wav","aac","wma","wmv","amr","mp2","flac","vorbis","ac3"],video:["mp4","rm","rmvb","wmv","avi","mpg","mpeg","mov"]},l.filterFiles=function(e,t){var n,r,o="file"===(t=t.toLowerCase()),i=[];return[].forEach.call(e,function(e){if(o)i.push(e);else if(n=e.name.slice(e.name.lastIndexOf(".")+1),(r=e.type.split("/"))[0]&&r[1]){(r[0].toLowerCase()===t||-1!==l.validFileExts[t].indexOf(n))&&i.push(e)}}),i};var d,f,g=l.supportFormData=l.notundef(m.FormData);l.getFileName=function(e){return e=l.verifyFileInput(e),g?e.files[0].name:e.value.slice(e.value.lastIndexOf("\\")+1)},l.getFileInfo=(d={ppt:1,pptx:2,pdf:3},function(e){var t={};if(!(e=l.verifyFileInput(e)).files)return t;var n=e.files[0];return g&&(t.name=n.name,t.size=n.size,t.type=n.name.match(/\.(\w+)$/),t.type=t.type&&t.type[1].toLowerCase(),t.transcodeType=d[t.type]||0),t}),l.sizeText=(f=["B","KB","MB","GB","TB","PB","EB","ZB","BB"],function(e){var t,n=0;do{t=(e=Math.floor(100*e)/100)+f[n],e/=1024,n++}while(e>1);return t}),l.promises2cmds=function(e){return e.map(function(e){return e.cmd})},l.objs2accounts=function(e){return e.map(function(e){return e.account})},l.teams2ids=function(e){return e.map(function(e){return e.teamId})},l.objs2ids=function(e){return e.map(function(e){return e.id})},l.getMaxUpdateTime=function(e){var t=e.map(function(e){return+e.updateTime});return Math.max.apply(Math,t)},l.genCheckUniqueFunc=function(e,t){return e=e||"id",t=t||1e3,function(t){this.uniqueSet=this.uniqueSet||{},this.uniqueSet[e]=this.uniqueSet[e]||{};var n=this.uniqueSet[e],r=t[e];return!n[r]&&(n[r]=!0,!0)}},l.fillPropertyWithDefault=function(e,t,n){return!!l.undef(e[t])&&(e[t]=n,!0)},e.exports=l},,,,,function(e,t,n){"use strict";var r={info:{hash:"6a79ac165c83986f338713446967414af1f3be49",shortHash:"6a79ac165",version:"8.0.1",sdkVersion:"140",nrtcVersion:"5.1.0",nrtcSdkVersion:"1",protocolVersion:1},agentVersion:"3.0.1",lbsUrl:"https://lbs.netease.im/lbs/webconf.jsp",roomserver:"roomserver.netease.im",connectTimeout:8e3,xhrTimeout:8e3,socketTimeout:8e3,reconnectionDelay:1600,reconnectionDelayMax:8e3,reconnectionJitter:.01,reconnectiontimer:null,heartbeatInterval:6e4,cmdTimeout:8e3,defaultReportUrl:"https://dr.netease.im/1.gif",isWeixinApp:!1,isNodejs:!1,isRN:!1,ipVersion:0,PUSHTOKEN:"",PUSHCONFIG:{},CLIENTTYPE:16,PushPermissionAsked:!1,iosPushConfig:null,androidPushConfig:null,netDetectAddr:"https://roomserver-dev.netease.im/v1/sdk/detect/local",optionDefaultLinkUrl:"",defaultLinkUrl:"weblink.netease.im",ipv6DefaultLinkUrl:"weblink.netease.im",optionIpv6DefaultLinkUrl:"",wxDefaultLinkUrl:"wlnimsc0.netease.im",getDefaultLinkUrl:function(e){var t,n;1===r.ipVersion?(t=r.optionIpv6DefaultLinkUrl,n=r.ipv6DefaultLinkUrl):(t=r.optionDefaultLinkUrl,n=r.defaultLinkUrl);var o=t||(r.isWeixinApp?r.wxDefaultLinkUrl:n);if(!o)return!1;var i=e?"https":"http",s=e?"443":"80",a=o;return-1===o.indexOf("http")&&(a=i+"://"+a),-1===o.indexOf(":")&&(a=a+":"+s),a}};r.weixinNetcall=r.nrtcNetcall={checkSumUrl:"https://nrtc.netease.im/demo/getChecksum.action",getChannelInfoUrl:"https://nrtc.netease.im/nrtc/getChannelInfos.action"},r.ipProbeAddr={ipv4:"https://detect4.netease.im/test/",ipv6:"https://detect6.netease.im/test/"},r.nrtcWebRTC2={checkSumUrl:"",getChannelInfoUrl:""},r.formatSocketUrl=function(e){var t=e.url,n=e.secure?"https":"http";return-1===t.indexOf("http")?n+"://"+t:t},r.uploadUrl="https://nos.netease.com",r.chunkUploadUrl="https://wanproxy-web.127.net",r.commonMaxSize=104857600,r.chunkSize=4194304,r.chunkMaxSize=4194304e4,r.replaceUrl="https://{bucket}-nosdn.netease.im/{object}",r.downloadHost="nos.netease.com",r.downloadUrl="https://{bucket}-nosdn.netease.im/{object}",r.httpsEnabled=!1,r.threshold=0,r.lbsUrls=["http://wanproxy.127.net/lbs","http://wanproxy-bj.127.net/lbs","http://wanproxy-hz.127.net/lbs","http://wanproxy-oversea.127.net/lbs"],r.genUploadUrl=function(e){return r.uploadUrl+"/"+e},r.genChunkUploadUrl=function(e){return r.chunkUploadUrl?r.chunkUploadUrl+"/"+e.bucket+"/"+e.objectName:""},r.genDownloadUrl=function(e,t){var n=e.bucket,o=(e.tag,e.expireSec),i=+new Date,s=o?"&survivalTime="+o:"",a=r.replaceUrl+"?createTime="+i+s;return(a=r.genNosProtocolUrl(a)).replace("{bucket}",n).replace("{object}",t)},r.genFileUrl=function(e){var t=e.bucket,n=e.objectName;return r.genNosProtocolUrl(r.replaceUrl).replace("{bucket}",t).replace("{object}",n)},r.genNosProtocolUrl=function(e){return/^http/.test(e)?r.httpsEnabled&&0!==e.indexOf("https://")&&(e=e.replace("http","https")):e=r.httpsEnabled?"https://"+e:"http://"+e,e},e.exports=r},function(e,t){var n=e.exports={version:"2.5.5"};"number"==typeof __e&&(__e=n)},function(e,t,n){var r=n(48)("wks"),o=n(33),i=n(8).Symbol,s="function"==typeof i;(e.exports=function(e){return r[e]||(r[e]=s&&i[e]||(s?i:o)("Symbol."+e))}).store=r},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},,function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty,o="~";function i(){}function s(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(o=!1)),a.prototype.eventNames=function(){var e,t,n=[];if(0===this._eventsCount)return n;for(t in e=this._events)r.call(e,t)&&n.push(o?t.slice(1):t);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},a.prototype.listeners=function(e,t){var n=o?o+e:e,r=this._events[n];if(t)return!!r;if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,a=new Array(s);i<s;i++)a[i]=r[i].fn;return a},a.prototype.emit=function(e,t,n,r,i,s){var a=o?o+e:e;if(!this._events[a])return!1;var c,u,l=this._events[a],m=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),m){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,r),!0;case 5:return l.fn.call(l.context,t,n,r,i),!0;case 6:return l.fn.call(l.context,t,n,r,i,s),!0}for(u=1,c=new Array(m-1);u<m;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var p,d=l.length;for(u=0;u<d;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),m){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,n);break;case 4:l[u].fn.call(l[u].context,t,n,r);break;default:if(!c)for(p=1,c=new Array(m-1);p<m;p++)c[p-1]=arguments[p];l[u].fn.apply(l[u].context,c)}}return!0},a.prototype.on=function(e,t,n){var r=new s(t,n||this),i=o?o+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],r]:this._events[i].push(r):(this._events[i]=r,this._eventsCount++),this},a.prototype.once=function(e,t,n){var r=new s(t,n||this,!0),i=o?o+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],r]:this._events[i].push(r):(this._events[i]=r,this._eventsCount++),this},a.prototype.removeListener=function(e,t,n,r){var s=o?o+e:e;if(!this._events[s])return this;if(!t)return 0==--this._eventsCount?this._events=new i:delete this._events[s],this;var a=this._events[s];if(a.fn)a.fn!==t||r&&!a.once||n&&a.context!==n||(0==--this._eventsCount?this._events=new i:delete this._events[s]);else{for(var c=0,u=[],l=a.length;c<l;c++)(a[c].fn!==t||r&&!a[c].once||n&&a[c].context!==n)&&u.push(a[c]);u.length?this._events[s]=1===u.length?u[0]:u:0==--this._eventsCount?this._events=new i:delete this._events[s]}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=o?o+e:e,this._events[t]&&(0==--this._eventsCount?this._events=new i:delete this._events[t])):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prototype.setMaxListeners=function(){return this},a.prefixed=o,a.EventEmitter=a,e.exports=a},function(e,t,n){"use strict";t.__esModule=!0;var r=s(n(124)),o=s(n(114)),i="function"==typeof o.default&&"symbol"==typeof r.default?function(e){return typeof e}:function(e){return e&&"function"==typeof o.default&&e.constructor===o.default&&e!==o.default.prototype?"symbol":typeof e};function s(e){return e&&e.__esModule?e:{default:e}}t.default="function"==typeof o.default&&"symbol"===i(r.default)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof o.default&&e.constructor===o.default&&e!==o.default.prototype?"symbol":void 0===e?"undefined":i(e)}},function(e,t,n){var r=n(18);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t,n){var r=n(12),o=n(67),i=n(50),s=Object.defineProperty;t.f=n(16)?Object.defineProperty:function(e,t,n){if(r(e),t=i(t,!0),r(n),o)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var r=n(8),o=n(6),i=n(29),s=n(20),a=n(17),c=function(e,t,n){var u,l,m,p=e&c.F,d=e&c.G,f=e&c.S,g=e&c.P,y=e&c.B,h=e&c.W,v=d?o:o[t]||(o[t]={}),b=v.prototype,T=d?r:f?r[t]:(r[t]||{}).prototype;for(u in d&&(n=t),n)(l=!p&&T&&void 0!==T[u])&&a(v,u)||(m=l?T[u]:n[u],v[u]=d&&"function"!=typeof T[u]?n[u]:y&&l?i(m,r):h&&T[u]==m?function(e){var t=function(t,n,r){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,r)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(m):g&&"function"==typeof m?i(Function.call,m):m,g&&((v.virtual||(v.virtual={}))[u]=m,e&c.R&&b&&!b[u]&&s(b,u,m)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.url2origin=t.uniqueID=t.off=t.removeEventListener=t.on=t.addEventListener=t.format=t.regWhiteSpace=t.regBlank=t.emptyFunc=t.f=t.emptyObj=t.o=void 0;var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};t.getGlobal=s,t.detectCSSFeature=function(e){var t=!1,n="Webkit Moz ms O".split(" ");if("undefined"==typeof document)return void console.log("error:fn:detectCSSFeature document is undefined");var r=document.createElement("div"),o=null;e=e.toLowerCase(),void 0!==r.style[e]&&(t=!0);if(!1===t){o=e.charAt(0).toUpperCase()+e.substr(1);for(var i=0;i<n.length;i++)if(void 0!==r.style[n[i]+o]){t=!0;break}}return t},t.fix=a,t.getYearStr=c,t.getMonthStr=u,t.getDayStr=l,t.getHourStr=m,t.getMinuteStr=p,t.getSecondStr=d,t.getMillisecondStr=f,t.dateFromDateTimeLocal=function(e){return e=""+e,new Date(e.replace(/-/g,"/").replace("T"," "))},t.getClass=h,t.typeOf=v,t.isString=b,t.isNumber=T,t.isInt=function(e){return T(e)&&e%1==0},t.isBoolean=function(e){return"boolean"===v(e)},t.isArray=S,t.isFunction=k,t.isDate=M,t.isRegExp=function(e){return"regexp"===v(e)},t.isError=function(e){return"error"===v(e)},t.isnull=_,t.notnull=C,t.undef=x,t.notundef=P,t.exist=w,t.notexist=O,t.isObject=I,t.isEmpty=function(e){return O(e)||(b(e)||S(e))&&0===e.length},t.containsNode=function(e,t){if(e===t)return!0;for(;t.parentNode;){if(t.parentNode===e)return!0;t=t.parentNode}return!1},t.calcHeight=function(e){var t=e.parentNode||("undefined"==typeof document?null:document.body);if(!t)return 0;(e=e.cloneNode(!0)).style.display="block",e.style.opacity=0,e.style.height="auto",t.appendChild(e);var n=e.offsetHeight;return t.removeChild(e),n},t.remove=function(e){e.parentNode&&e.parentNode.removeChild(e)},t.dataset=function(e,t,n){if(!w(n))return e.getAttribute("data-"+t);e.setAttribute("data-"+t,n)},t.target=function(e){return e.target||e.srcElement},t.createIframe=function(e){if("undefined"==typeof document)return;var t;if((e=e||{}).name)try{(t=document.createElement('<iframe name="'+e.name+'"></iframe>')).frameBorder=0}catch(n){(t=document.createElement("iframe")).name=e.name}else t=document.createElement("iframe");e.visible||(t.style.display="none");k(e.onload)&&A(t,"load",function n(r){if(!t.src)return;e.multi||j(t,"load",n);e.onload(r)});(e.parent||document.body).appendChild(t);var n=e.src||"about:blank";return setTimeout(function(){t.src=n},0),t},t.html2node=function(e){if("undefined"==typeof document)return;var t=document.createElement("div");t.innerHTML=e;var n=[],r=void 0,o=void 0;if(t.children)for(r=0,o=t.children.length;r<o;r++)n.push(t.children[r]);else for(r=0,o=t.childNodes.length;r<o;r++){var i=t.childNodes[r];1===i.nodeType&&n.push(i)}return n.length>1?t:n[0]},t.scrollTop=function(e){"undefined"!=typeof document&&w(e)&&(document.documentElement.scrollTop=document.body.scrollTop=e);return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},t.forOwn=F,t.mixin=R,t.isJSON=L,t.parseJSON=function e(t){try{L(t)&&(t=JSON.parse(t)),I(t)&&F(t,function(n,r){switch(v(r)){case"string":case"object":t[n]=e(r)}})}catch(e){console.log("error:",e)}return t},t.simpleClone=function(e){var t=[],n=JSON.stringify(e,function(e,n){if("object"===(void 0===n?"undefined":(0,i.default)(n))&&null!==n){if(-1!==t.indexOf(n))return;t.push(n)}return n});return JSON.parse(n)},t.merge=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(function(t){R(e,t)}),e},t.fillUndef=function(e,t){return F(t,function(t,n){x(e[t])&&(e[t]=n)}),e},t.checkWithDefault=function(e,t,n){var r=e[t]||e[t.toLowerCase()];O(r)&&(r=n,e[t]=r);return r},t.fetch=function(e,t){return F(e,function(n,r){w(t[n])&&(e[n]=t[n])}),e},t.string2object=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",",n={};return e.split(t).forEach(function(e){var t=e.split("="),r=t.shift();r&&(n[decodeURIComponent(r)]=decodeURIComponent(t.join("=")))}),n},t.object2string=D,t.genUrlSep=function(e){return e.indexOf("?")<0?"?":"&"},t.object2query=function(e){return D(e,"&",!0)},t.isFileInput=q,t.getKeys=function(e,t){var n=Object.keys(e);t&&n.sort(function(t,n){var r=q(e[t]),o=q(e[n]);return r===o?0:r?1:-1});return n};t.o={},t.emptyObj={},t.f=function(){},t.emptyFunc=function(){},t.regBlank=/\s+/gi,t.regWhiteSpace=/\s+/gi;function s(){return"undefined"!=typeof window?window:void 0!==e?e:{}}function a(e,t){t=t||2;for(var n=""+e;n.length<t;)n="0"+n;return n}function c(e){return""+e.getFullYear()}function u(e){return a(e.getMonth()+1)}function l(e){return a(e.getDate())}function m(e){return a(e.getHours())}function p(e){return a(e.getMinutes())}function d(e){return a(e.getSeconds())}function f(e){return a(e.getMilliseconds(),3)}var g,y;t.format=(g=/yyyy|MM|dd|hh|mm|ss|SSS/g,y={yyyy:c,MM:u,dd:l,hh:m,mm:p,ss:d,SSS:f},function(e,t){return e=new Date(e),isNaN(+e)?"invalid date":(t=t||"yyyy-MM-dd").replace(g,function(t){return y[t](e)})});function h(e){return Object.prototype.toString.call(e).slice(8,-1)}function v(e){return h(e).toLowerCase()}function b(e){return"string"===v(e)}function T(e){return"number"===v(e)}function S(e){return"array"===v(e)}function k(e){return"function"===v(e)}function M(e){return"date"===v(e)}function _(e){return null===e}function C(e){return null!==e}function x(e){return void 0===e}function P(e){return void 0!==e}function w(e){return P(e)&&C(e)}function O(e){return x(e)||_(e)}function I(e){return w(e)&&"object"===v(e)}var E=t.addEventListener=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on"+t,n)},A=t.on=E,N=t.removeEventListener=function(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent&&e.detachEvent("on"+t,n)},j=t.off=N;function F(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},n=arguments[2];for(var r in e)e.hasOwnProperty(r)&&t.call(n,r,e[r])}function R(e,t){F(t,function(t,n){e[t]=n})}var U;t.uniqueID=(U=0,function(){return""+U++});function L(e){return b(e)&&0===e.indexOf("{")&&e.lastIndexOf("}")===e.length-1}function D(e,t,n){if(!e)return"";var r=[];return F(e,function(e,t){k(t)||(M(t)?t=t.getTime():S(t)?t=t.join(","):I(t)&&(t=JSON.stringify(t)),n&&(t=encodeURIComponent(t)),r.push(encodeURIComponent(e)+"="+t))}),r.join(t||",")}t.url2origin=function(){var e=/^([\w]+?:\/\/.*?(?=\/|$))/i;return function(t){return e.test(t||"")?RegExp.$1.toLowerCase():""}}();function q(e){var t=s();return e.tagName&&"INPUT"===e.tagName.toUpperCase()||t.Blob&&e instanceof t.Blob}}).call(this,n(35))},function(e,t,n){e.exports=!n(24)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){"use strict";(function(t){var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=function(){var e="object"===(void 0===t?"undefined":(0,i.default)(t))?t:window,n=Math.pow(2,53)-1,r=/\bOpera/,o=Object.prototype,s=o.hasOwnProperty,a=o.toString;function c(e){return(e=String(e)).charAt(0).toUpperCase()+e.slice(1)}function u(e){return e=f(e),/^(?:webOS|i(?:OS|P))/.test(e)?e:c(e)}function l(e,t){for(var n in e)s.call(e,n)&&t(e[n],n,e)}function m(e){return null==e?c(e):a.call(e).slice(8,-1)}function p(e){return String(e).replace(/([ -])(?!$)/g,"$1?")}function d(e,t){var r=null;return function(e,t){var r=-1,o=e?e.length:0;if("number"==typeof o&&o>-1&&o<=n)for(;++r<o;)t(e[r],r,e);else l(e,t)}(e,function(n,o){r=t(r,n,o,e)}),r}function f(e){return String(e).replace(/^ +| +$/g,"")}return function t(n){var o=e,s=n&&"object"===(void 0===n?"undefined":(0,i.default)(n))&&"String"!=m(n);s&&(o=n,n=null);var c=o.navigator||{},g=c.userAgent||"";n||(n=g);var y,h,v,b,T,S=s?!!c.likeChrome:/\bChrome\b/.test(n)&&!/internal|\n/i.test(a.toString()),k=s?"Object":"ScriptBridgingProxyObject",M=s?"Object":"Environment",_=s&&o.java?"JavaPackage":m(o.java),C=s?"Object":"RuntimeObject",x=/\bJava/.test(_)&&o.java,P=x&&m(o.environment)==M,w=x?"a":"α",O=x?"b":"β",I=o.document||{},E=o.operamini||o.opera,A=r.test(A=s&&E?E["[[Class]]"]:m(E))?A:E=null,N=n,j=[],F=null,R=n==g,U=R&&E&&"function"==typeof E.version&&E.version(),L=d([{label:"EdgeHTML",pattern:"Edge"},"Trident",{label:"WebKit",pattern:"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"],function(e,t){return e||RegExp("\\b"+(t.pattern||p(t))+"\\b","i").exec(n)&&(t.label||t)}),D=function(e){return d(e,function(e,t){return e||RegExp("\\b"+(t.pattern||p(t))+"\\b","i").exec(n)&&(t.label||t)})}(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{label:"Microsoft Edge",pattern:"Edge"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{label:"Samsung Internet",pattern:"SamsungBrowser"},"SeaMonkey",{label:"Silk",pattern:"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{label:"SRWare Iron",pattern:"Iron"},"Sunrise","Swiftfox","Waterfox","WebPositive","Opera Mini",{label:"Opera Mini",pattern:"OPiOS"},"Opera",{label:"Opera",pattern:"OPR"},"Chrome",{label:"Chrome",pattern:"(?:HeadlessChrome)"},{label:"Chrome Mobile",pattern:"(?:CriOS|CrMo)"},{label:"Firefox",pattern:"(?:Firefox|Minefield)"},{label:"Firefox for iOS",pattern:"FxiOS"},{label:"IE",pattern:"IEMobile"},{label:"IE",pattern:"MSIE"},"Safari"]),q=z([{label:"BlackBerry",pattern:"BB10"},"BlackBerry",{label:"Galaxy S",pattern:"GT-I9000"},{label:"Galaxy S2",pattern:"GT-I9100"},{label:"Galaxy S3",pattern:"GT-I9300"},{label:"Galaxy S4",pattern:"GT-I9500"},{label:"Galaxy S5",pattern:"SM-G900"},{label:"Galaxy S6",pattern:"SM-G920"},{label:"Galaxy S6 Edge",pattern:"SM-G925"},{label:"Galaxy S7",pattern:"SM-G930"},{label:"Galaxy S7 Edge",pattern:"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{label:"Kindle Fire",pattern:"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{label:"Wii U",pattern:"WiiU"},"Wii","Xbox One",{label:"Xbox 360",pattern:"Xbox"},"Xoom"]),B=function(e){return d(e,function(e,t,r){return e||(t[q]||t[/^[a-z]+(?: +[a-z]+\b)*/i.exec(q)]||RegExp("\\b"+p(r)+"(?:\\b|\\w*\\d)","i").exec(n))&&r})}({Apple:{iPad:1,iPhone:1,iPod:1},Archos:{},Amazon:{Kindle:1,"Kindle Fire":1},Asus:{Transformer:1},"Barnes & Noble":{Nook:1},BlackBerry:{PlayBook:1},Google:{"Google TV":1,Nexus:1},HP:{TouchPad:1},HTC:{},LG:{},Microsoft:{Xbox:1,"Xbox One":1},Motorola:{Xoom:1},Nintendo:{"Wii U":1,Wii:1},Nokia:{Lumia:1},Samsung:{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},Sony:{PlayStation:1,"PlayStation Vita":1}}),H=function(e){return d(e,function(e,t){var r=t.pattern||p(t);return!e&&(e=RegExp("\\b"+r+"(?:/[\\d.]+|[ \\w.]*)","i").exec(n))&&(e=function(e,t,n){var r={"10.0":"10",6.4:"10 Technical Preview",6.3:"8.1",6.2:"8",6.1:"Server 2008 R2 / 7","6.0":"Server 2008 / Vista",5.2:"Server 2003 / XP 64-bit",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"};return t&&n&&/^Win/i.test(e)&&!/^Windows Phone /i.test(e)&&(r=r[/[\d.]+$/.exec(e)])&&(e="Windows "+r),e=String(e),t&&n&&(e=e.replace(RegExp(t,"i"),n)),e=u(e.replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])}(e,r,t.label||t)),e})}(["Windows Phone","Android","CentOS",{label:"Chrome OS",pattern:"CrOS"},"Debian","Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "]);function z(e){return d(e,function(e,t){var r=t.pattern||p(t);return!e&&(e=RegExp("\\b"+r+" *\\d+[.\\w_]*","i").exec(n)||RegExp("\\b"+r+" *\\w+-[\\w]*","i").exec(n)||RegExp("\\b"+r+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(n))&&((e=String(t.label&&!RegExp(r,"i").test(t.label)?t.label:e).split("/"))[1]&&!/[\d.]+/.test(e[0])&&(e[0]+=" "+e[1]),t=t.label||t,e=u(e[0].replace(RegExp(r,"i"),t).replace(RegExp("; *(?:"+t+"[_-])?","i")," ").replace(RegExp("("+t+")[-_.]?(\\w)","i"),"$1 $2"))),e})}if(L&&(L=[L]),B&&!q&&(q=z([B])),(y=/\bGoogle TV\b/.exec(q))&&(q=y[0]),/\bSimulator\b/i.test(n)&&(q=(q?q+" ":"")+"Simulator"),"Opera Mini"==D&&/\bOPiOS\b/.test(n)&&j.push("running in Turbo/Uncompressed mode"),"IE"==D&&/\blike iPhone OS\b/.test(n)?(B=(y=t(n.replace(/like iPhone OS/,""))).manufacturer,q=y.product):/^iP/.test(q)?(D||(D="Safari"),H="iOS"+((y=/ OS ([\d_]+)/i.exec(n))?" "+y[1].replace(/_/g,"."):"")):"Konqueror"!=D||/buntu/i.test(H)?B&&"Google"!=B&&(/Chrome/.test(D)&&!/\bMobile Safari\b/i.test(n)||/\bVita\b/.test(q))||/\bAndroid\b/.test(H)&&/^Chrome/.test(D)&&/\bVersion\//i.test(n)?(D="Android Browser",H=/\bAndroid\b/.test(H)?H:"Android"):"Silk"==D?(/\bMobi/i.test(n)||(H="Android",j.unshift("desktop mode")),/Accelerated *= *true/i.test(n)&&j.unshift("accelerated")):"PaleMoon"==D&&(y=/\bFirefox\/([\d.]+)\b/.exec(n))?j.push("identifying as Firefox "+y[1]):"Firefox"==D&&(y=/\b(Mobile|Tablet|TV)\b/i.exec(n))?(H||(H="Firefox OS"),q||(q=y[1])):!D||(y=!/\bMinefield\b/i.test(n)&&/\b(?:Firefox|Safari)\b/.exec(D))?(D&&!q&&/[\/,]|^[^(]+?\)/.test(n.slice(n.indexOf(y+"/")+8))&&(D=null),(y=q||B||H)&&(q||B||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(H))&&(D=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(H)?H:y)+" Browser")):"Electron"==D&&(y=(/\bChrome\/([\d.]+)\b/.exec(n)||0)[1])&&j.push("Chromium "+y):H="Kubuntu",U||(U=d(["(?:Cloud9|CriOS|CrMo|Edge|FxiOS|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$))","Version","HeadlessChrome",p(D),"(?:Firefox|Minefield|NetFront)"],function(e,t){return e||(RegExp(t+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(n)||0)[1]||null})),(y=("iCab"==L&&parseFloat(U)>3?"WebKit":/\bOpera\b/.test(D)&&(/\bOPR\b/.test(n)?"Blink":"Presto"))||/\b(?:Midori|Nook|Safari)\b/i.test(n)&&!/^(?:Trident|EdgeHTML)$/.test(L)&&"WebKit"||!L&&/\bMSIE\b/i.test(n)&&("Mac OS"==H?"Tasman":"Trident")||"WebKit"==L&&/\bPlayStation\b(?! Vita\b)/i.test(D)&&"NetFront")&&(L=[y]),"IE"==D&&(y=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(n)||0)[1])?(D+=" Mobile",H="Windows Phone "+(/\+$/.test(y)?y:y+".x"),j.unshift("desktop mode")):/\bWPDesktop\b/i.test(n)?(D="IE Mobile",H="Windows Phone 8.x",j.unshift("desktop mode"),U||(U=(/\brv:([\d.]+)/.exec(n)||0)[1])):"IE"!=D&&"Trident"==L&&(y=/\brv:([\d.]+)/.exec(n))&&(D&&j.push("identifying as "+D+(U?" "+U:"")),D="IE",U=y[1]),R){if(b="global",T=null!=(v=o)?(0,i.default)(v[b]):"number",/^(?:boolean|number|string|undefined)$/.test(T)||"object"==T&&!v[b])m(y=o.runtime)==k?(D="Adobe AIR",H=y.flash.system.Capabilities.os):m(y=o.phantom)==C?(D="PhantomJS",U=(y=y.version||null)&&y.major+"."+y.minor+"."+y.patch):"number"==typeof I.documentMode&&(y=/\bTrident\/(\d+)/i.exec(n))?(U=[U,I.documentMode],(y=+y[1]+4)!=U[1]&&(j.push("IE "+U[1]+" mode"),L&&(L[1]=""),U[1]=y),U="IE"==D?String(U[1].toFixed(1)):U[0]):"number"==typeof I.documentMode&&/^(?:Chrome|Firefox)\b/.test(D)&&(j.push("masking as "+D+" "+U),D="IE",U="11.0",L=["Trident"],H="Windows");else if(x&&(N=(y=x.lang.System).getProperty("os.arch"),H=H||y.getProperty("os.name")+" "+y.getProperty("os.version")),P){try{U=o.require("ringo/engine").version.join("."),D="RingoJS"}catch(e){(y=o.system)&&y.global.system==o.system&&(D="Narwhal",H||(H=y[0].os||null))}D||(D="Rhino")}else"object"===(0,i.default)(o.process)&&!o.process.browser&&(y=o.process)&&("object"===(0,i.default)(y.versions)&&("string"==typeof y.versions.electron?(j.push("Node "+y.versions.node),D="Electron",U=y.versions.electron):"string"==typeof y.versions.nw&&(j.push("Chromium "+U,"Node "+y.versions.node),D="NW.js",U=y.versions.nw)),D||(D="Node.js",N=y.arch,H=y.platform,U=(U=/[\d.]+/.exec(y.version))?U[0]:null));H=H&&u(H)}if(U&&(y=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(U)||/(?:alpha|beta)(?: ?\d)?/i.exec(n+";"+(R&&c.appMinorVersion))||/\bMinefield\b/i.test(n)&&"a")&&(F=/b/i.test(y)?"beta":"alpha",U=U.replace(RegExp(y+"\\+?$"),"")+("beta"==F?O:w)+(/\d+\+?/.exec(y)||"")),"Fennec"==D||"Firefox"==D&&/\b(?:Android|Firefox OS)\b/.test(H))D="Firefox Mobile";else if("Maxthon"==D&&U)U=U.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(q))"Xbox 360"==q&&(H=null),"Xbox 360"==q&&/\bIEMobile\b/.test(n)&&j.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(D)&&(!D||q||/Browser|Mobi/.test(D))||"Windows CE"!=H&&!/Mobi/i.test(n))if("IE"==D&&R)try{null===o.external&&j.unshift("platform preview")}catch(e){j.unshift("embedded")}else(/\bBlackBerry\b/.test(q)||/\bBB10\b/.test(n))&&(y=(RegExp(q.replace(/ +/g," *")+"/([.\\d]+)","i").exec(n)||0)[1]||U)?(H=((y=[y,/BB10/.test(n)])[1]?(q=null,B="BlackBerry"):"Device Software")+" "+y[0],U=null):this!=l&&"Wii"!=q&&(R&&E||/Opera/.test(D)&&/\b(?:MSIE|Firefox)\b/i.test(n)||"Firefox"==D&&/\bOS X (?:\d+\.){2,}/.test(H)||"IE"==D&&(H&&!/^Win/.test(H)&&U>5.5||/\bWindows XP\b/.test(H)&&U>8||8==U&&!/\bTrident\b/.test(n)))&&!r.test(y=t.call(l,n.replace(r,"")+";"))&&y.name&&(y="ing as "+y.name+((y=y.version)?" "+y:""),r.test(D)?(/\bIE\b/.test(y)&&"Mac OS"==H&&(H=null),y="identify"+y):(y="mask"+y,D=A?u(A.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(y)&&(H=null),R||(U=null)),L=["Presto"],j.push(y));else D+=" Mobile";(y=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(n)||0)[1])&&(y=[parseFloat(y.replace(/\.(\d)$/,".0$1")),y],"Safari"==D&&"+"==y[1].slice(-1)?(D="WebKit Nightly",F="alpha",U=y[1].slice(0,-1)):U!=y[1]&&U!=(y[2]=(/\bSafari\/([\d.]+\+?)/i.exec(n)||0)[1])||(U=null),y[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(n)||0)[1],537.36==y[0]&&537.36==y[2]&&parseFloat(y[1])>=28&&"WebKit"==L&&(L=["Blink"]),R&&(S||y[1])?(L&&(L[1]="like Chrome"),y=y[1]||((y=y[0])<530?1:y<532?2:y<532.05?3:y<533?4:y<534.03?5:y<534.07?6:y<534.1?7:y<534.13?8:y<534.16?9:y<534.24?10:y<534.3?11:y<535.01?12:y<535.02?"13+":y<535.07?15:y<535.11?16:y<535.19?17:y<536.05?18:y<536.1?19:y<537.01?20:y<537.11?"21+":y<537.13?23:y<537.18?24:y<537.24?25:y<537.36?26:"Blink"!=L?"27":"28")):(L&&(L[1]="like Safari"),y=(y=y[0])<400?1:y<500?2:y<526?3:y<533?4:y<534?"4+":y<535?5:y<537?6:y<538?7:y<601?8:"8"),L&&(L[1]+=" "+(y+="number"==typeof y?".x":/[.+]/.test(y)?"":"+")),"Safari"==D&&(!U||parseInt(U)>45)&&(U=y)),"Opera"==D&&(y=/\bzbov|zvav$/.exec(H))?(D+=" ",j.unshift("desktop mode"),"zvav"==y?(D+="Mini",U=null):D+="Mobile",H=H.replace(RegExp(" *"+y+"$"),"")):"Safari"==D&&/\bChrome\b/.exec(L&&L[1])&&(j.unshift("desktop mode"),D="Chrome Mobile",U=null,/\bOS X\b/.test(H)?(B="Apple",H="iOS 4.3+"):H=null),U&&0==U.indexOf(y=/[\d.]+$/.exec(H))&&n.indexOf("/"+y+"-")>-1&&(H=f(H.replace(y,""))),L&&!/\b(?:Avant|Nook)\b/.test(D)&&(/Browser|Lunascape|Maxthon/.test(D)||"Safari"!=D&&/^iOS/.test(H)&&/\bSafari\b/.test(L[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|Web)/.test(D)&&L[1])&&(y=L[L.length-1])&&j.push(y),j.length&&(j=["("+j.join("; ")+")"]),B&&q&&q.indexOf(B)<0&&j.push("on "+B),q&&j.push((/^on /.test(j[j.length-1])?"":"on ")+q),H&&(y=/ ([\d.+]+)$/.exec(H),h=y&&"/"==H.charAt(H.length-y[0].length-1),H={architecture:32,family:y&&!h?H.replace(y[0],""):H,version:y?y[1]:null,toString:function(){var e=this.version;return this.family+(e&&!h?" "+e:"")+(64==this.architecture?" 64-bit":"")}}),(y=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(N))&&!/\bi686\b/i.test(N)?(H&&(H.architecture=64,H.family=H.family.replace(RegExp(" *"+y),"")),D&&(/\bWOW64\b/i.test(n)||R&&/\w(?:86|32)$/.test(c.cpuClass||c.platform)&&!/\bWin64; x64\b/i.test(n))&&j.unshift("32-bit")):H&&/^OS X/.test(H.family)&&"Chrome"==D&&parseFloat(U)>=39&&(H.architecture=64),n||(n=null);var W={};return W.description=n,W.layout=L&&L[0],W.manufacturer=B,W.name=D,W.prerelease=F,W.product=q,W.ua=n,W.version=D&&U,W.os=H||{architecture:null,family:null,version:null,toString:function(){return"null"}},W.parse=t,W.toString=function(){return this.description||""},W.version&&j.unshift(U),W.name&&j.unshift(D),H&&D&&(H!=String(H).split(" ")[0]||H!=D.split(" ")[0]&&!q)&&j.push(q?"("+H+")":"on "+H),j.length&&(W.description=j.join(" ")),W}()}();e.exports=s}).call(this,n(35))},function(e,t,n){var r=n(13),o=n(28);e.exports=n(16)?function(e,t,n){return r.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var r=n(64),o=n(51);e.exports=function(e){return r(o(e))}},,,function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports={}},,function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};function s(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.message=e||n.message||"","object"===(void 0===t?"undefined":(0,i.default)(t))?(this.event=t,this.code="Other_Error"):void 0!==t&&(this.code=t),this.timetag=+new Date,void 0!==n&&(this.event=n),this.event&&(this.callFunc=this.event.callFunc||null,delete this.event.callFunc)}s.prototype=Object.create(Error.prototype),s.prototype.name="NIMError";var a={201:"客户端版本不对, 需升级sdk",302:"用户名或密码错误, 请检查appKey和token是否有效, account和token是否匹配",403:"非法操作或没有权限",404:"对象(用户/群/聊天室)不存在",405:"参数长度过长",408:"客户端请求超时",414:"参数错误",415:"服务不可用/没有聊天室服务器可分配",416:"频率控制",417:"重复操作",422:"帐号被禁用",500:"服务器内部错误",501:"数据库操作失败",503:"服务器繁忙",508:"删除有效期过了",509:"已失效",7101:"被拉黑",801:"群人数达到上限",802:"没有权限",803:"群不存在或未发生变化",804:"用户不在群里面",805:"群类型不匹配",806:"创建群数量达到限制",807:"群成员状态不对",809:"已经在群里",811:"强推列表中帐号数量超限",812:"群被禁言",813:"因群数量限制，部分拉人成功",814:"禁止使用群组消息已读服务",815:"群管理员人数上限",997:"协议已失效",998:"解包错误",999:"打包错误",9102:"通道失效",9103:"已经在其他端接听/拒绝过这通电话",11001:"对方离线, 通话不可送达",13002:"聊天室状态异常",13003:"在黑名单中",13004:"在禁言名单中",13006:"聊天室处于整体禁言状态,只有管理员能发言",Connect_Failed:"无法建立连接, 请确保能 ping/telnet 到云信服务器; 如果是IE8/9, 请确保项目部署在 HTTPS 环境下",Error_Internet_Disconnected:"网断了",Error_Connection_is_not_Established:"连接未建立",Error_Connection_Socket_State_not_Match:"socket状态不对",Error_Timeout:"超时",Param_Error:"参数错误",No_File_Selected:"请选择文件",Wrong_File_Type:"文件类型错误",File_Too_Large:"文件过大",Cross_Origin_Iframe:"不能获取跨域Iframe的内容",Not_Support:"不支持",NO_DB:"无数据库",DB:"数据库错误",Still_In_Team:"还在群里",Session_Exist:"会话已存在",Session_Not_Exist:"会话不存在",Error_Unknown:"未知错误",Operation_Canceled:"操作取消"};[200,406,808,810].forEach(function(e){a[e]=null}),s.genError=function(e){var t=a[e];return void 0===t&&(t="操作失败"),null===t?null:new s(t,e)},s.multiInstance=function(e){return new s("不允许初始化多个实例","Not_Allow_Multi_Instance",e)},s.newNetworkError=function(e){var t="Error_Internet_Disconnected";return new s(a[t],t,e)},s.newConnectError=function(e){var t="Connect_Failed";return new s(a[t]||null,t,e)},s.newConnectionError=function(e){var t="Error_Connection_is_not_Established";return new s(a[t],t,e)},s.newSocketStateError=function(e){var t="Error_Connection_Socket_State_not_Match";return new s(a[t],t,e)},s.newTimeoutError=function(e){var t="Error_Timeout";return new s(a[t],t,e)},s.newFrequencyControlError=function(e){var t=new s(a[416],416,e);return t.from="local",t},s.newParamError=function(e,t){return new s(e||a.Param_Error,"Param_Error",t)},s.newNoFileError=function(e,t){var n="No_File_Selected";return new s(e||a[n],n,t)},s.newWrongFileTypeError=function(e,t){var n="Wrong_File_Type";return new s(e||a[n],n,t)},s.newFileTooLargeError=function(e,t){var n="File_Too_Large";return new s(e||a[n],n,t)},s.newCORSIframeError=function(e){var t="Cross_Origin_Iframe";return new s(a[t],t,e)},s.newSupportError=function(e,t,n){return new s("不支持"+e,"Not_Support_"+t,n)},s.newSupportDBError=function(e){return s.newSupportError("数据库","DB",e)},s.noDBError=function(e){return new s(a.NO_DB,"NO_DB",e)},s.newDBError=function(e){return new s(a.DB,"DB",e)},s.newUnknownError=function(e){var t="Error_Unknown";return new s(a[t],t,e)},s.stillInTeamError=function(e){var t="Still_In_Team";return new s(a[t],t,e)},s.sessionExist=function(e){var t="Session_Exist";return new s(a[t],t,e)},s.sessionNotExist=function(e){var t="Session_Not_Exist";return new s(a[t],t,e)},s.cancel=function(e){var t="Operation_Canceled";return new s(a[t],t,e)},s.customError=function(e,t){e=e||"Other_Error";var n="";return(t=t||{}).message||(n=a[e]||e),"object"!==(void 0===e?"undefined":(0,i.default)(e))?new s(n,e,t):new s(n,"Other_Error",void 0===t?e:t)},e.exports=s},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,n){var r=n(42);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,o){return e.call(t,n,r,o)}}return function(){return e.apply(t,arguments)}}},,function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t){var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+r).toString(36))}},function(e,t,n){var r=n(65),o=n(47);e.exports=Object.keys||function(e){return r(e,o)}},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";var r=n(122)(!0);n(68)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){var r=n(13).f,o=n(17),i=n(7)("toStringTag");e.exports=function(e,t,n){e&&!o(e=n?e:e.prototype,i)&&r(e,i,{configurable:!0,value:t})}},function(e,t){e.exports=!0},function(e,t,n){"use strict";var r=n(0),o={init:function(){o.deviceId=r.guid()}};o.init(),o.clientTypeMap={1:"Android",2:"iOS",4:"PC",8:"WindowsPhone",16:"Web",32:"Server",64:"Mac"},o.db={open:function(){}},o.rnfs=null,e.exports=o},function(e,t,n){n(117);for(var r=n(8),o=n(20),i=n(25),s=n(7)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<a.length;c++){var u=a[c],l=r[u],m=l&&l.prototype;m&&!m[s]&&o(m,s,u),i[u]=i.Array}},function(e,t,n){var r=n(51);e.exports=function(e){return Object(r(e))}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},,function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){var r=n(8),o=n(6),i=n(38),s=n(46),a=n(13).f;e.exports=function(e){var t=o.Symbol||(o.Symbol=i?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t,n){t.f=n(7)},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){var r=n(8),o=r["__core-js_shared__"]||(r["__core-js_shared__"]={});e.exports=function(e){return o[e]||(o[e]={})}},function(e,t,n){var r=n(48)("keys"),o=n(33);e.exports=function(e){return r[e]||(r[e]=o(e))}},function(e,t,n){var r=n(18);e.exports=function(e,t){if(!r(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!r(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!r(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!r(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=n(10),a=n(0),c=a.notundef,u=n(331),l=n(5);function m(){}var p={};m.getInstance=function(e){e=f(e),a.verifyOptions(e,"account","api::Base.getInstance");var t=this.genInstanceName(e),n=p[t];return n?m.updateInstance(n,e):n=p[t]=new this(e),n},m.updateInstance=function(e,t){e.setOptions(t),e.connect()};var d=m.fn=m.prototype=Object.create(new s),f=function(e){return e.nosSurvivalTime?(a.verifyParamType("nosSurvivalTime",e.nosSurvivalTime,"number","api::Base.getInstance"),a.verifyParamMin("nosSurvivalTime",e.nosSurvivalTime,86400,"api::Base.getInstance")):e.nosSurvivalTime=1/0,e};d.updatePrivateConf=function(e){if(e&&"object"===(0,i.default)(e.privateConf)){var t=e.privateConf;"string"==typeof t.lbs_web&&(e.lbsUrl=t.lbs_web),"boolean"==typeof t.link_ssl_web&&(e.secure=t.link_ssl_web),"boolean"==typeof t.https_enabled&&(e.httpsEnabled=t.https_enabled),e.uploadUrl=t.nos_uploader_web?t.nos_uploader_web:null,e.chunkUploadUrl=t.nos_uploader_web?t.nos_uploader_web:null,e.replaceUrl=t.nos_downloader?t.nos_downloader:null,e.downloadUrl=t.nos_accelerate?t.nos_accelerate:null,e.downloadHost=t.nos_accelerate_host?t.nos_accelerate_host:null,e.ntServerAddress=t.nt_server||null,e.kibanaServer=t.kibana_server,e.statisticServer=t.statistic_server,e.reportGlobalServer=t.report_global_server,e.ipVersion=t.ip_protocol_version,e.defaultLink=t.link_web||e.defaultLink,e.ipv6DefaultLink=t.link_ipv6_web||e.ipv6DefaultLink,"string"==typeof t.nos_lbs&&(e.lbsUrls=[t.nos_lbs])}return null===e.ntServerAddress||""===e.ntServerAddress?l.ntServerAddress=null:l.ntServerAddress=e.ntServerAddress||l.defaultReportUrl,l.uploadUrl=e.uploadUrl||l.uploadUrl,l.chunkUploadUrl=e.chunkUploadUrl||l.chunkUploadUrl,l.downloadUrl=e.downloadUrl||l.downloadUrl,l.downloadHost=e.downloadHost||l.downloadHost,l.replaceUrl=e.replaceUrl||l.replaceUrl,l.httpsEnabled=e.httpsEnabled||l.httpsEnabled,e.probe_ipv4_url&&(l.ipProbeAddr.ipv4=e.probe_ipv4_url),e.probe_ipv6_url&&(l.ipProbeAddr.ipv6=e.probe_ipv6_url),e},d.init=function(e){a.verifyOptions(e,"account","api::Base.init"),e=this.updatePrivateConf(e),a.verifyBooleanWithDefault(e,"exifOrientation",!0,"","api::Base.init");var t=this.account=e.account=e.account+"",n=e.constructor.genInstanceName(e),r=p[n];if(e._disableSingleton&&(r=null),r)return m.updateInstance(r,e),r;this.name=n,p[n]=this,this.logger=e.logger=new u({debug:e.debug,logFunc:e.logFunc,prefix:this.subType}),e.api=this;var o=this.protocol=new e.Protocol(e);return o.name="Protocol-"+n,o.account=t,o.api=this,o.message=this.message=new e.Message({account:t}),this.options=a.copy(e),this},d.destroy=function(e){var t,n,r=this;e=e||{};var o=this.name;o&&(this.protocol&&(t=this.protocol.reconnectTimer,n=this.protocol.onlineListener),this.protocol&&this.protocol.resetPush&&this.protocol.resetPush(),this.disconnect({done:function(i){r.logger.warn("ApiBase::destroy: instance destroyed ..."),Object.getOwnPropertyNames(r).forEach(function(e){delete r[e]}),p&&(p[o]=null,clearTimeout(t),n&&"undefined"!=typeof window&&a.isFunction(window.removeEventListener)&&window.removeEventListener("online",n)),e.done instanceof Function&&e.done(i)}}))},d.setOptions=function(e){this.protocol.setOptions(e)},d.processCallback=function(e,t){g(e,t)},d.processCallbackPromise=function(e,t){return new Promise(function(n,r){g(e,t,!0,n,r)})};var g=function(e,t,n,r,o){var i="api::processCallback";n&&(i="api::processCallbackPromise"),a.verifyCallback(e,"done",i),e.callback=function(s,u,l){var m=e.callback.options;if(u=u||m,t&&(u=m),a.isFunction(e.cbaop)){var p=e.cbaop(s,u);c(p)&&(u=p)}var d=e.done;a.isObject(u)&&(delete u.done,delete u.cb,delete u.callback),n?s?o({message:"生成接口回调错误",callFunc:i,event:s}):r(u):d(s,u,l)},e.callback.options=a.copy(e)};d.processPs=function(e){a.notexist(e.ps)&&(e.ps=""),a.verifyArrayMax("ps",e.ps,5e3)},d.processCustom=function(e){a.notexist(e.custom)&&(e.custom="")},d.sendCmd=function(){this.protocol.sendCmd.apply(this.protocol,arguments)},d.sendCmdWithResp=function(e,t,n){this.sendCmd(e,t,function(e,t,r){a.isFunction(n)&&(e?n(e,t):n(null,r))})},d.cbAndSendCmd=function(e,t){var n=this.processCallbackPromise(t);return this.sendCmd(e,t),n},d.sendCmdUsePromise=function(e,t){var n=this;return new Promise(function(r,o){n.sendCmd(e,t,function(e,t,n){if(e)o(e);else{var i=a.merge({},t,n);r(i)}})})},m.use=function(e,t){e&&e.install&&a.isFunction(e.install)&&e.install(this,t)},m.rmAllInstances=function(){for(var e in p)p[e].destroy();p={}},d.logout=function(e){e=e||{},this.protocol.shouldReconnect=!1,this.protocol.doLogout=!0,this.processCallback(e),this.sendCmd("logout",e,e.callback)},e.exports=m,n(330),n(329),n(326),n(325),n(324),n(323),n(322)},function(e,t,n){"use strict";var r=n(58),o=n(167),i=n(166),s=n(165);r.json=o,r.upload=i,r.chunkUpload=s,e.exports=r},,function(e,t,n){var r=n(18),o=n(8).document,i=r(o)&&r(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},function(e,t,n){"use strict";t.__esModule=!0;var r,o=n(103),i=(r=o)&&r.__esModule?r:{default:r};t.default=i.default||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}},function(e,t,n){"use strict";var r=n(15),o=n(171),i=n(169),s=n(168),a={},c=r.f;function u(e){var t=e.upload="multipart/form-data"===(e.headers||r.o)["Content-Type"],n=!1;try{n=(location.protocol+"//"+location.host).toLowerCase()!==r.url2origin(e.url)}catch(e){}return e.cors=n,t||n||e.mode?function(e){var t=e.mode,n=o,a=r.getGlobal();return!a.FormData&&a.document&&(t="iframe"),"iframe"===t&&(n=e.upload?i:s),new n(e)}(e):new o(e)}function l(e,t,n){var r=a[e];if(r){"onload"===t&&r.result&&(n=function(e,t){t={data:t};var n=e.result.headers;return n&&(t.headers=e.req.header(n)),t}(r,n)),function(e){var t=a[e];t&&(t.req.destroy(),delete a[e])}(e);var o={type:t,result:n};c(o),o.stopped||r[t](o.result)}}function m(e,t){var n=r.genUrlSep(e);return t=t||"",r.isObject(t)&&(t=r.object2query(t)),t&&(e+=n+t),e}function p(e,t){t=t||{};var n=r.uniqueID(),o={result:t.result,onload:t.onload||r.f,onerror:t.onerror||r.f};a[n]=o,t.onload=function(e,t){l(e,"onload",t)}.bind(null,n),t.onerror=function(e,t){l(e,"onerror",t)}.bind(null,n),t.query&&(e=m(e,t.query));var i=t.method||"";return i&&!/get/i.test(i)||!t.data||(e=m(e,t.data),t.data=null),t.url=e,o.req=u(t),n}p.filter=function(e){r.isFunction(e)&&(c=e)},p.abort=function(e){var t=a[e];t&&t.req&&t.req.abort()},e.exports=p},function(e,t,n){var r=n(12),o=n(120),i=n(47),s=n(49)("IE_PROTO"),a=function(){},c=function(){var e,t=n(56)("iframe"),r=i.length;for(t.style.display="none",n(77).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;r--;)delete c.prototype[i[r]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(a.prototype=r(e),n=new a,a.prototype=null,n[s]=e):n=c(),void 0===t?n:o(n,t)}},function(e,t,n){var r=n(52),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},function(e,t,n){"use strict";var r=n(0),o=n(5),i=n(183),s=n(27);function a(e){r.undef(e.secure)&&(e.secure=!0),this.options=r.copy(e),this.keepNosSafeUrl=this.options.keepNosSafeUrl||!1;var t=e.defaultLink||e.defaultLinkUrl;r.notundef(t)&&r.isString(t)&&(o.optionDefaultLinkUrl=t.trim()),r.notundef(e.ipv6DefaultLink)&&r.isString(e.ipv6DefaultLink)&&(o.optionIpv6DefaultLinkUrl=e.ipv6DefaultLink),Array.isArray(e.lbsUrls)&&(o.lbsUrls=e.lbsUrls),this.init(),this.connect()}var c=a.fn=a.prototype;c.init=function(){this.logger=this.options.logger,this.getNosOriginUrlReqNum=0,this.checkNosReqNum=0,this.cmdTaskArray=[],this.timerMap={},this.cmdCallbackMap={},this.cmdContentMap={},this.initConnect(),this.reset()},c.reset=function(){this.resetConnect()},c.setOptions=function(e){var t=this.options,n=Object.keys(t),o=n.indexOf("account");-1!==o&&n.splice(o,1),e=r.filterObj(e,n),this.options=r.merge(t,e),this.reset()},c.sendCmd=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments[2];this.heartbeat(),"heartbeat"!==e&&this.logger.warn("protocol::sendCmd: "+e,t);var r,o=e,i=(e=this.parser.createCmd(e,t)).SER;t=t||{},this.cmdContentMap[i]=t,t.single&&(delete t.single,1===(r=Object.keys(t)).length&&(this.cmdContentMap[i]=t[r[0]])),t.NOTSTORE&&((r=t.NOTSTORE.split(" ")).forEach(function(e){delete t[e]}),delete t.NOTSTORE),(n=n||t.callback)&&(this.cmdCallbackMap[i]=n),this.cmdTaskArray.push({cmdName:o,cmd:JSON.stringify(e)}),this.startCmdTaskTimer()},c.startCmdTaskTimer=function(){var e=this;e.cmdTaskTimer||(e.cmdTaskTimer=setTimeout(function(){var t=e.cmdTaskArray.shift();e.cmdTaskTimer=null,t&&e.executeCmdTask(t),e.cmdTaskArray.length&&e.startCmdTaskTimer()},0))},c.executeCmdTask=function(e){var t=e.cmdName,n=e.cmd,r=(n=JSON.parse(n)).SER;this.isFrequencyControlled(t)?(this.logger.warn("protocol::executeCmdTask: "+t+" hit freq control"),this.markCallbackInvalid(r,s.newFrequencyControlError({callFunc:"protocol::executeCmdTask",message:t+" hit freq control"}))):this.isConnected()?("heartbeat"!==t&&this.logger.log("protocol::sendCmd: "+t+" "+JSON.stringify(n)),this.doSendCmd(n)):(this.logger.warn("protocol::executeCmdTask: "+t+" not connected"),this.markCallbackInvalid(r,s.newSocketStateError({callFunc:"protocol::executeCmdTask",message:t+" not connected"})))},c.isFrequencyControlled=function(e){var t=this.frequencyControlMap&&this.frequencyControlMap[e];if(t){if(Date.now()<t.from+t.duration)return!0;delete this.frequencyControlMap[e]}},c.doSendCmd=function(e){var t=this,n=e.SER;function r(){t.markCallbackInvalid(n,s.newSocketStateError({callFunc:"protocol::doSendCmd",message:"ser "+n+" socketSendJson Error"})),t.onDisconnect(!0,"protocol::doSendCmd:socketSendJson")}t.timerMap[n]=setTimeout(function(){t.markCallbackInvalid(n,s.newTimeoutError({callFunc:"protocol::doSendCmd",message:"ser "+n+" Timeout Error"}))},o.cmdTimeout);try{t.socket&&t.socket.send?t.socket.send(JSON.stringify(e)):r()}catch(e){r()}},c.getObjWithSer=function(e){var t=this.cmdContentMap[e];return t&&!t.isImSyncDataObj&&delete this.cmdContentMap[e],t&&r.copy(t)},c.getCallbackWithSer=function(e){var t=this.cmdCallbackMap[e];return t&&!t.isImSyncDataCb&&delete this.cmdCallbackMap[e],t},c.getTimerWithSer=function(e){var t=this.timerMap[e];return delete this.timerMap[e],t},c.clearTimerWithSer=function(e){var t=this.getTimerWithSer(e);t&&clearTimeout(t)},c.markCallbackInvalid=function(e,t){this.getObjWithSer(e),this.clearTimerWithSer(e);var n=this.getCallbackWithSer(e);if(n){var r=n.options;setTimeout(function(){n(t||s.newUnknownError({ser:e}),r)},0)}},c.markAllCallbackInvalid=function(e){var t=this;Object.keys(this.cmdCallbackMap).forEach(function(n){t.markCallbackInvalid(n,e)})},c.getPacketObj=function(e){var t=null;if(e&&e.raw){var n=e.raw.ser;r.notundef(n)&&(t=this.getObjWithSer(n))}return t},c.callPacketAckCallback=function(e){var t=this;if(e&&e.raw){var n=e.raw.ser;if(r.notundef(n)){t.clearTimerWithSer(n);var o=t.getCallbackWithSer(n);o&&(o.originUrl&&e.obj&&e.obj.file&&(e.obj.file._url_safe=e.obj.file.url,e.obj.file.url=o.originUrl,"audio"===e.obj.type&&(e.obj.file.mp3Url=o.originUrl+(~o.originUrl.indexOf("?")?"&":"?")+"audioTrans&type=mp3")),e.promise?e.promise.then(function(){o(e.error,e.obj)},function(r){r.callFunc="protocol::callPacketAckCallback",r.message="Resp Promoise Error: cmd: "+e.cmd+", ser: "+n;var i=s.customError("CALLBACK_ACK_ERR",r);t.logger.error("protocol::callPacketAckCallback: promise error "+JSON.stringify(r)),o(i,e.obj,e.content)}):o(e.error,e.obj,e.content))}}},c.onMessage=function(e){var t=this;t.heartbeat(),t.parser.parseResponse(e).then(function(e){if(e.notFound&&t.logger.warn("protocol::onMessage: packet not found "+JSON.stringify(e)),e.error){e.error.message=e.cmd+" error: "+e.error.message,t.logger.error("protocol::onMessage: packet error "+JSON.stringify(e.error));var n=e.raw||{};408!==n.code&&415!==n.code&&500!==n.code||i.saveErrEvent({code:n.code,module:e.cmd,accid:t.account})}else e.content||"heartbeat"===e.cmd||t.logger.warn("protocol::onMessage: packet.content undefined "+JSON.stringify(e));e.frequencyControlDuration&&(t.logger.error("protocol::onMessage: server freq control "+JSON.stringify(e.cmd)),t.frequencyControlMap=t.frequencyControlMap||{},t.frequencyControlMap[e.cmd]={from:+new Date,duration:e.frequencyControlDuration}),e.obj=t.getPacketObj(e),"heartbeat"!==e.cmd&&"getClientAntispam"!==e.cmd&&t.logger.log("protocol::recvCmd: "+e.cmd+" "+e.rawStr);var o="process"+r.capFirstLetter(e.service);if(t[o])if("heartbeat"!==e.cmd&&t.logger.warn("protocol::recvCmd: "+e.cmd+" "+o,e.content),"syncDone"===e.cmd){if(t.cmdCallbackMap[e.raw.ser]&&t.cmdCallbackMap[e.raw.ser].isImSyncDataCb){t.cmdCallbackMap[e.raw.ser].isImSyncDataCb=!1;var s=function(e,t){this.checkNosReqNum++,this.getNosOriginUrlReqNum<=0||this.checkNosReqNum>=20?this[e](t):setTimeout(s,300)}.bind(t,o,e);setTimeout(function(){s.call(t,o,e)},10)}}else t[o](e);else t.logger.warn("protocol::onMessage: "+o+" not found");t.callPacketAckCallback(e)})},c.onMiscError=function(e,t,n){t&&this.notifyError(e,t,n)},c.onCustomError=function(e,t){var n=s.customError(e,t),r=t.message||"未知错误";this.onMiscError(r,n)},c.notifyError=function(e,t,n){this.isConnected()&&(this.logger.error((e||"")+" "+this.name,t,n),this.options.onerror(t,n))},c.emitAPI=function(e){var t=e.type,n=e.obj;this.api.emit(t,n)},e.exports=a,n(321),n(319),n(318),n(317),n(316)},function(e,t,n){var r=n(32),o=n(28),i=n(21),s=n(50),a=n(17),c=n(67),u=Object.getOwnPropertyDescriptor;t.f=n(16)?u:function(e,t){if(e=i(e),t=s(t,!0),c)try{return u(e,t)}catch(e){}if(a(e,t))return o(!r.f.call(e,t),e[t])}},function(e,t,n){var r=n(65),o=n(47).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},function(e,t,n){var r=n(31);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t,n){var r=n(17),o=n(21),i=n(119)(!1),s=n(49)("IE_PROTO");e.exports=function(e,t){var n,a=o(e),c=0,u=[];for(n in a)n!=s&&r(a,n)&&u.push(n);for(;t.length>c;)r(a,n=t[c++])&&(~i(u,n)||u.push(n));return u}},function(e,t,n){e.exports=n(20)},function(e,t,n){e.exports=!n(16)&&!n(24)(function(){return 7!=Object.defineProperty(n(56)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){"use strict";var r=n(38),o=n(14),i=n(66),s=n(20),a=n(25),c=n(121),u=n(37),l=n(82),m=n(7)("iterator"),p=!([].keys&&"next"in[].keys()),d=function(){return this};e.exports=function(e,t,n,f,g,y,h){c(n,t,f);var v,b,T,S=function(e){if(!p&&e in C)return C[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},k=t+" Iterator",M="values"==g,_=!1,C=e.prototype,x=C[m]||C["@@iterator"]||g&&C[g],P=x||S(g),w=g?M?S("entries"):P:void 0,O="Array"==t&&C.entries||x;if(O&&(T=l(O.call(new e)))!==Object.prototype&&T.next&&(u(T,k,!0),r||"function"==typeof T[m]||s(T,m,d)),M&&x&&"values"!==x.name&&(_=!0,P=function(){return x.call(this)}),r&&!h||!p&&!_&&C[m]||s(C,m,P),a[t]=P,a[k]=d,g)if(v={values:M?P:S("values"),keys:y?P:S("keys"),entries:w},h)for(b in v)b in C||i(C,b,v[b]);else o(o.P+o.F*(p||_),t,v);return v}},,,,function(e,t,n){"use strict";var r=n(15),o=r.f,i=n(170);function s(e){e.onload&&this.once("load",e.onload),e.onerror&&this.once("error",e.onerror),e.onbeforesend&&this.once("beforesend",e.onbeforesend),e.onaftersend&&this.once("aftersend",e.onaftersend);var t=(e=this.options=r.fetch({method:"GET",url:"",sync:!1,data:null,headers:{},cookie:!1,timeout:6e4,type:"text",form:null,input:null,putFileAtEnd:!1,proxyUrl:""},e)).headers;r.notexist(t["Content-Type"])&&(t["Content-Type"]="application/x-www-form-urlencoded"),this.send()}var a=s.prototype=Object.create(i.prototype);a.send=function(){var e=this,t=e.options;setTimeout(function(){try{try{e.emit("beforesend",t)}catch(e){console.log("error:","ignore error ajax beforesend,",e)}e.doSend()}catch(t){console.log("error:","ignore error server error,",t),e.onError("serverError","请求失败:"+t.message)}},0)},a.doSend=o,a.afterSend=function(){var e=this;setTimeout(function(){e.emit("aftersend",e.options)},0)},a.onLoad=function(e){var t=this.options,n=e.status,r=e.result;if(0===(""+n).indexOf("2")){if("json"===t.type)try{r=JSON.parse(r)}catch(e){return console.log("error:","ignore error parse json,",e),void this.onError("parseError",r)}this.emit("load",r)}else this.onError("serverError","服务器返回异常状态",{status:n,result:r})},a.onError=function(e,t,n){var o=r.isObject(n)?n:{};o.code=e||"error",o.message=t||"发生错误",this.emit("error",o)},a.onTimeout=function(){this.onError("timeout","请求超时")},a.abort=function(){this.onError("abort","客户端中止")},a.header=function(e){var t=this;if(!r.isArray(e))return t.getResponseHeader(e||"");var n={};return e.forEach(function(e){n[e]=t.header(e)}),n},a.getResponseHeader=o,a.destroy=o,e.exports=s},,function(e,t,n){"use strict";var r=n(5),o={genUrlSep:function(e){return-1===(e=""+e).indexOf("?")?"?imageView&":"&"},urlQuery2Object:function(e){if("[object String]"!==Object.prototype.toString.call(e)||""===e)return{};var t=e.indexOf("?");if(-1!==t){var n=e.slice(t+1).split("&"),r={};return n.forEach(function(e){if(~e.indexOf("=")){var t=e.split("=");r[t[0]]=decodeURIComponent(t[1])}else r[e]=""}),r}},url2object:function(e){"[object String]"!==Object.prototype.toString.call(e)&&(e="");var t=(e=e||"").indexOf("https")>=0?"https://":"http://",n=e.replace(t,"");n.indexOf("?")>=0&&(n=n.substring(0,n.indexOf("?")));var r=n.split("/");n=r[0];var o="";if(r.length>0&&(o=r.slice(1).join("/")),-1===e.indexOf("?"))return{protocol:t,hostname:n,path:o,query:{}};var i=e.substr(e.indexOf("?")+1).split("&"),s={};return i.forEach(function(e){if(e.indexOf("=")>0){var t=e.split("=");s[t[0]]=decodeURIComponent(t[1])}else s[e]=""}),{protocol:t,hostname:n,path:o,query:s}},object2url:function(e){var t=e.protocol,n=e.hostname,r=e.path,o=e.query;t=t||"http://",n=n||"",r&&(n=n+"/"+r),o=o||{};var i=[];for(var s in o)"imageView"!==s&&i.push(s+"="+encodeURIComponent(o[s]));return i.length>0?""+t+n+"?imageView&"+i.join("&"):""+t+n},genPrivateUrl:function(e){var t=o.url2object(e),n=t.hostname,i=t.path,s=r.downloadHost,a=r.downloadUrl;if(n===s){var c=i.indexOf("/");if(-1!==c){var u=i.substring(0,c),l=i.substring(c+1);return a.replace("{bucket}",u).replace("{object}",l)}}else if(n&&"[object String]"==Object.prototype.toString.call(n)&&~n.indexOf(s)){var m=t.path,p=m.indexOf(".");if(-1!==p){var d=m.substring(0,p),f=m;return a.replace("{bucket}",d).replace("{object}",f)}}return e}};e.exports=o},function(e,t){e.exports=function(e,t){var n=t.split(".");for(;n.length;){var r=n.shift(),o=!1;if("?"==r[r.length-1]&&(r=r.slice(0,-1),o=!0),!(e=e[r])&&o)return e}return e}},function(e,t){},function(e,t,n){var r=n(8).document;e.exports=r&&r.documentElement},,function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r},s=n(74);var a=n(0),c=a.notundef,u=a.exist,l=n(130),m=n(181),p=m.typeMap;function d(e){e.resend?(a.verifyOptions(e,"idClient","msg::Message"),this.idClient=e.idClient):this.idClient=a.guid(),this.type=p[e.type],this.resend=e.resend?1:0,c(e.subType)&&(a.isInt(+e.subType)&&+e.subType>0?this.subType=+e.subType:a.onParamError("subType只能是大于0的整数","msg::Message")),c(e.custom)&&("object"===(0,i.default)(e.custom)?this.custom=JSON.stringify(e.custom):this.custom=""+e.custom),c(e.text)&&(this.body=""+e.text),c(e.body)&&(this.body=""+e.body),c(e.yidunEnable)&&(this.yidunEnable=e.yidunEnable?1:0),c(e.antiSpamUsingYidun)&&(this.antiSpamUsingYidun=e.antiSpamUsingYidun?1:0),c(e.antiSpamContent)&&("object"===(0,i.default)(e.antiSpamContent)?this.antiSpamContent=JSON.stringify(e.antiSpamContent):this.antiSpamContent=""+e.antiSpamContent),c(e.antiSpamBusinessId)&&("object"===(0,i.default)(e.antiSpamBusinessId)?this.antiSpamBusinessId=JSON.stringify(e.antiSpamBusinessId):this.antiSpamBusinessId=""+e.antiSpamBusinessId),c(e.yidunAntiCheating)&&(this.yidunAntiCheating=e.yidunAntiCheating+""),c(e.skipHistory)&&(this.skipHistory=e.skipHistory?1:0),c(e.highPriority)&&(this.highPriority=e.highPriority?1:0),c(e.clientAntiSpam)&&(this.clientAntiSpam=e.clientAntiSpam?1:0),c(e.env)&&(this.env=e.env)}d.validTypes=m.validTypes,a.merge(d.prototype,m.prototype),d.getType=m.getType,d.reverse=function(e){var t=a.filterObj(e,"chatroomId idClient from fromNick fromAvatar _fromAvatar_safe fromCustom userUpdateTime custom status");return c(t.fromAvatar)&&(t.fromAvatar=(0,s.genPrivateUrl)(t.fromAvatar)),t=a.merge(t,{fromClientType:l.reverseType(e.fromClientType),time:+e.time,type:d.getType(e),text:u(e.body)?e.body:e.text||"",resend:1==+e.resend}),c(t.userUpdateTime)&&(t.userUpdateTime=+t.userUpdateTime),c(e.callbackExt)&&(t.callbackExt=e.callbackExt),c(e.subType)&&(t.subType=+e.subType),t.status=t.status||"success",t},d.setExtra=function(e,t){m.setFlow(e,t)},e.exports=d},,function(e,t){e.exports=function e(t,n){"use strict";var r,o,i=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,s=/(^[ ]*|[ ]*$)/g,a=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,c=/^0x[0-9a-f]+$/i,u=/^0/,l=function(t){return e.insensitive&&(""+t).toLowerCase()||""+t},m=l(t).replace(s,"")||"",p=l(n).replace(s,"")||"",d=m.replace(i,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),f=p.replace(i,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),g=parseInt(m.match(c),16)||1!==d.length&&m.match(a)&&Date.parse(m),y=parseInt(p.match(c),16)||g&&p.match(a)&&Date.parse(p)||null;if(y){if(g<y)return-1;if(g>y)return 1}for(var h=0,v=Math.max(d.length,f.length);h<v;h++){if(r=!(d[h]||"").match(u)&&parseFloat(d[h])||d[h]||0,o=!(f[h]||"").match(u)&&parseFloat(f[h])||f[h]||0,isNaN(r)!==isNaN(o))return isNaN(r)?1:-1;if(typeof r!=typeof o&&(r+="",o+=""),r<o)return-1;if(r>o)return 1}return 0}},function(e,t,n){var r=n(17),o=n(41),i=n(49)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),r(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},,,,function(e,t,n){"use strict";var r={link:{id:1,heartbeat:2,negotiateTransport:5,initTransport:6},sync:{id:5,sync:1,syncTeamMembers:2},misc:{id:6,getSimpleNosToken:1,getNosToken:2,notifyUploadLog:3,uploadSdkLogUrl:4,audioToText:5,processImage:6,getNosTokenTrans:7,notifyTransLog:8,fetchFile:9,fetchFileList:10,removeFile:11,getClientAntispam:17,fileQuickTransfer:18,getNosOriginUrl:22,getServerTime:23,getNosAccessToken:24,deleteNosAccessToken:25},avSignal:{id:15,signalingCreate:1,signalingDelay:2,signalingClose:3,signalingJoin:4,signalingLeave:5,signalingInvite:6,signalingCancel:7,signalingReject:8,signalingAccept:9,signalingControl:10,signalingNotify:11,signalingMutilClientSyncNotify:12,signalingUnreadMessageSyncNotify:13,signalingChannelsSyncNotify:14,signalingGetChannelInfo:15}},o={heartbeat:{sid:r.link.id,cid:r.link.heartbeat},negotiateTransport:{sid:r.link.id,cid:r.link.negotiateTransport,params:[{type:"int",name:"sdkVersion"},{type:"Property",name:"negotiateTransportTag"}]},initTransport:{sid:r.link.id,cid:r.link.initTransport,params:[{type:"Property",name:"initTransportTag"}]},getSimpleNosToken:{sid:r.misc.id,cid:r.misc.getSimpleNosToken,params:[{type:"int",name:"num"}]},getNosToken:{sid:r.misc.id,cid:r.misc.getNosToken,params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken"}]},uploadSdkLogUrl:{sid:r.misc.id,cid:r.misc.uploadSdkLogUrl,params:[{type:"string",name:"url"}]},audioToText:{sid:r.misc.id,cid:r.misc.audioToText,params:[{type:"Property",name:"audioToText"}]},processImage:{sid:r.misc.id,cid:r.misc.processImage,params:[{type:"String",name:"url"},{type:"PropertyArray",name:"imageOps",entity:"imageOp"}]},getClientAntispam:{sid:r.misc.id,cid:r.misc.getClientAntispam,params:[{type:"Property",name:"clientAntispam"}]},fileQuickTransfer:{sid:r.misc.id,cid:r.misc.fileQuickTransfer,params:[{type:"Property",name:"fileQuickTransfer"}]},getNosOriginUrl:{sid:r.misc.id,cid:r.misc.getNosOriginUrl,params:[{type:"Property",name:"nosFileUrlTag"}]},getServerTime:{sid:r.misc.id,cid:r.misc.getServerTime,params:[]},getNosAccessToken:{sid:r.misc.id,cid:r.misc.getNosAccessToken,params:[{type:"Property",name:"nosAccessTokenTag"}]},deleteNosAccessToken:{sid:r.misc.id,cid:r.misc.deleteNosAccessToken,params:[{type:"Property",name:"nosAccessTokenTag"}]},getNosTokenTrans:{sid:r.misc.id,cid:r.misc.getNosTokenTrans,params:[{type:"Property",name:"transToken"}]},fetchFile:{sid:r.misc.id,cid:r.misc.fetchFile,params:[{type:"String",name:"docId"}]},fetchFileList:{sid:r.misc.id,cid:r.misc.fetchFileList,params:[{type:"Property",name:"fileListParam"}]},removeFile:{sid:r.misc.id,cid:r.misc.removeFile,params:[{type:"String",name:"docId"}]},signalingCreate:{sid:r.avSignal.id,cid:r.avSignal.signalingCreate,params:[{type:"Property",name:"avSignalTag"}]},signalingDelay:{sid:r.avSignal.id,cid:r.avSignal.signalingDelay,params:[{type:"Property",name:"avSignalTag"}]},signalingClose:{sid:r.avSignal.id,cid:r.avSignal.signalingClose,params:[{type:"Property",name:"avSignalTag"}]},signalingJoin:{sid:r.avSignal.id,cid:r.avSignal.signalingJoin,params:[{type:"Property",name:"avSignalTag"}]},signalingLeave:{sid:r.avSignal.id,cid:r.avSignal.signalingLeave,params:[{type:"Property",name:"avSignalTag"}]},signalingInvite:{sid:r.avSignal.id,cid:r.avSignal.signalingInvite,params:[{type:"Property",name:"avSignalTag"}]},signalingCancel:{sid:r.avSignal.id,cid:r.avSignal.signalingCancel,params:[{type:"Property",name:"avSignalTag"}]},signalingReject:{sid:r.avSignal.id,cid:r.avSignal.signalingReject,params:[{type:"Property",name:"avSignalTag"}]},signalingAccept:{sid:r.avSignal.id,cid:r.avSignal.signalingAccept,params:[{type:"Property",name:"avSignalTag"}]},signalingControl:{sid:r.avSignal.id,cid:r.avSignal.signalingControl,params:[{type:"Property",name:"avSignalTag"}]},signalingGetChannelInfo:{sid:r.avSignal.id,cid:r.avSignal.signalingGetChannelInfo,params:[{type:"Property",name:"avSignalTag"}]}};e.exports={idMap:r,cmdConfig:o,packetConfig:{"1_2":{service:"link",cmd:"heartbeat"},"1_5":{service:"link",cmd:"negotiateTransport",response:[{type:"Property",name:"negotiateTransportTag"}]},"1_6":{service:"link",cmd:"initTransport",response:[{type:"Property",name:"initTransportTag"}]},"6_1":{service:"misc",cmd:"getSimpleNosToken",response:[{type:"PropertyArray",name:"nosTokens",entity:"nosToken"}]},"6_2":{service:"misc",cmd:"getNosToken",response:[{type:"Property",name:"nosToken"}]},"6_3":{service:"misc",cmd:"notifyUploadLog"},"6_4":{service:"misc",cmd:"uploadSdkLogUrl"},"6_5":{service:"misc",cmd:"audioToText",response:[{type:"String",name:"text"}]},"6_6":{service:"misc",cmd:"processImage",response:[{type:"String",name:"url"}]},"6_7":{service:"misc",cmd:"getNosTokenTrans",response:[{type:"Property",name:"nosToken"},{type:"String",name:"docId"}]},"6_8":{service:"misc",cmd:"notifyTransLog",response:[{type:"Property",name:"transInfo"}]},"6_9":{service:"misc",cmd:"fetchFile",response:[{type:"Property",name:"info",entity:"transInfo"}]},"6_10":{service:"misc",cmd:"fetchFileList",response:[{type:"PropertyArray",name:"list",entity:"transInfo"},{type:"Number",name:"totalCount"}]},"6_11":{service:"misc",cmd:"removeFile",response:[{type:"String",name:"res"}]},"6_17":{service:"misc",cmd:"getClientAntispam",response:[{type:"Property",name:"clientAntispam"}]},"6_18":{service:"misc",cmd:"fileQuickTransfer",response:[{type:"Property",name:"fileQuickTransfer"}]},"6_22":{service:"misc",cmd:"getNosOriginUrl",response:[{type:"Property",name:"nosFileUrlTag"}]},"6_23":{service:"misc",cmd:"getServerTime",response:[{type:"Number",name:"time"}]},"6_24":{service:"misc",cmd:"getNosAccessToken",response:[{type:"Property",name:"nosAccessTokenTag"}]},"6_25":{service:"misc",cmd:"deleteNosAccessToken"},"15_1":{service:"avSignal",cmd:"signalingCreate",response:[{type:"Property",name:"avSignalTag"}]},"15_2":{service:"avSignal",cmd:"signalingDelay",response:[{type:"Property",name:"avSignalTag"}]},"15_3":{service:"avSignal",cmd:"signalingClose",response:[{type:"Property",name:"avSignalTag"}]},"15_4":{service:"avSignal",cmd:"signalingJoin",response:[{type:"Property",name:"avSignalTag"}]},"15_5":{service:"avSignal",cmd:"signalingLeave",response:[]},"15_6":{service:"avSignal",cmd:"signalingInvite",response:[]},"15_7":{service:"avSignal",cmd:"signalingCancel",response:[]},"15_8":{service:"avSignal",cmd:"signalingReject",response:[]},"15_9":{service:"avSignal",cmd:"signalingAccept",response:[]},"15_10":{service:"avSignal",cmd:"signalingControl",response:[]},"15_11":{service:"avSignal",cmd:"signalingNotify",response:[{type:"Property",name:"avSignalTag"}]},"15_12":{service:"avSignal",cmd:"signalingMutilClientSyncNotify",response:[{type:"Property",name:"avSignalTag"}]},"15_13":{service:"avSignal",cmd:"signalingUnreadMessageSyncNotify",response:[{type:"PropertyArray",name:"avSignalTag"}]},"15_14":{service:"avSignal",cmd:"signalingChannelsSyncNotify",response:[{type:"PropertyArray",name:"avSignalTag"}]},"15_15":{service:"avSignal",cmd:"signalingGetChannelInfo",response:[{type:"Property",name:"avSignalTag"}]}}}},,,,,,,,,function(e,t,n){"use strict";var r,o=n(74),i=n(0),s=n(27),a=n(204),c=n(315),u=n(314),l=n(313),m=n(312),p=n(311);function d(e){this.mixin(e)}d.prototype=Object.create(function(){}.prototype,{protocol:{value:null,writable:!0,enumerable:!0,configurable:!0}}),d.prototype.setProtocol=function(e){this.protocol=e},d.prototype.mixin=function(e){var t=this;this.configMap=this.configMap||{},["idMap","cmdConfig","packetConfig"].forEach(function(n){t.configMap[n]=i.merge({},t.configMap[n],e.configMap&&e.configMap[n])}),["serializeMap","unserializeMap"].forEach(function(n){t[n]=i.merge({},t[n],e[n])})},d.prototype.createCmd=(r=1,function(e,t){var n=this,o=this.configMap.cmdConfig[e],s="heartbeat"===e?0:r++;return s>32767&&(s=1,r=2),e={SID:o.sid,CID:o.cid,SER:s},o.params&&(e.Q=[],o.params.forEach(function(r){var o=r.type,s=r.name,a=r.entity,c=t[s];if(!i.undef(c)){switch(o){case"PropertyArray":o="ArrayMable",c=c.map(function(e){return{t:"Property",v:n.serialize(e,a)}});break;case"Property":c=n.serialize(c,s);break;case"bool":c=c?"true":"false"}e.Q.push({t:o,v:c})}})),e}),d.prototype.parseResponse=function(e){var t=this;return new Promise(function(n,r){var o=JSON.parse(e),a={raw:o,rawStr:e,error:s.genError(o.code)},c=t.configMap.packetConfig[o.sid+"_"+o.cid];if(!c)return a.notFound={sid:o.sid,cid:o.cid},void n(a);var u=o.r,l="notify"===c.service&&!c.cmd;if(a.isNotify=l,l){var m=o.r[1].headerPacket;if(c=t.configMap.packetConfig[m.sid+"_"+m.cid],u=o.r[1].body,!c)return a.notFound={sid:m.sid,cid:m.cid},void n(a)}if(a.service=c.service,a.cmd=c.cmd,a.error){var p=o.sid+"_"+o.cid;if(l&&(p=m.sid+"_"+m.cid),a.error.cmd=a.cmd,a.error.callFunc="protocol::parseResponse: "+p,416===a.error.code){var d=u[0];d&&(a.frequencyControlDuration=1e3*d)}}var f=!1;a.error&&c.trivialErrorCodes&&(f=-1!==c.trivialErrorCodes.indexOf(a.error.code));var g=[];if((!a.error||f)&&c.response){a.content={};var y=function(e,t,n,r){if(e&&"msg"===r||"sysMsg"===r){var o=n.content[r];i.isObject(o)&&!o.idServer&&(o.idServer=""+t.r[0])}};c.response.forEach(function(e,n){var r=u[n];if(!i.undef(r)){var s=e.type,c=e.name,m=e.entity||c;switch(s){case"Property":g.push(t.unserialize(r,m).then(function(e,t,n,r,o){n.content[r]=o,y(e,t,n,r)}.bind(this,l,o,a,c)));break;case"PropertyArray":a.content[c]=[],r.forEach(function(e,n){g.push(t.unserialize(e,m).then(function(e,t,r){e.content[t][n]=r}.bind(this,a,c)))});break;case"KVArray":a.content[c]=r,y(l,o,a,c);break;case"long":case"Long":case"byte":case"Byte":case"Number":a.content[c]=+r;break;default:a.content[c]=r,y(l,o,a,c)}}})}Promise.all(g).then(function(){n(a)})})},d.prototype.serialize=function(e,t){var n=this.serializeMap[t],r={};for(var o in n)e.hasOwnProperty(o)&&(r[n[o]]=e[o]);return r},d.prototype.matchNosSafeUrl=function(e){if(!i.isString(e)||!~e.indexOf("_im_url=1"))return!1;var t=(0,o.urlQuery2Object)(e);return!(!t||!t._im_url||1!=t._im_url)},d.prototype.getOneNosOriginUrl=function(e,t,n){var r=this;return new Promise(function(o,i){r.protocol.getNosOriginUrlReqNum++,r.protocol.sendCmd("getNosOriginUrl",{nosFileUrlTag:{safeUrl:e}},function(e,i,s){r.protocol.getNosOriginUrlReqNum--,e?console.warn("error: get nos originUrl failed",e):(t["_"+n+"_safe"]=t[n],t[n]=s.nosFileUrlTag&&s.nosFileUrlTag.originUrl),o()})})},d.prototype.checkObjSafeUrl=function(e,t,n){var r=this;for(var o in e)if(e.hasOwnProperty(o)){var s=e[o];if(i.isString(s)){if(this.matchNosSafeUrl(s)){var a=this.getOneNosOriginUrl(s,e,o);t.push(a),n.push(a)}}else i.isObject(s)?this.checkObjSafeUrl(s,t,n):i.isArray(s)&&s.forEach(function(e){i.isObject(e)&&r.checkObjSafeUrl(e,t,n)})}};var f=["url","avatar","fromAvatar","chatroomAvatar"];d.prototype.unserialize=function(e,t){var n=this;return new Promise(function(r,o){var i=n.unserializeMap[t],s={},a=[];if(e)for(var c in i){var u=[];if(e.hasOwnProperty(c)&&(s[i[c]]=e[c],!n.protocol.keepNosSafeUrl))if("attach"===i[c]&&e[c]&&e[c].indexOf&&~e[c].indexOf("_im_url=1"))try{var l=JSON.parse(e[c]);n.checkObjSafeUrl(l,u,a),Promise.all(u).then(function(e,t){e.attach=JSON.stringify(t)}.bind(n,s,l))}catch(e){console.warn(e)}else~f.indexOf(i[c])&&e[c]&&n.matchNosSafeUrl(e[c])&&a.push(n.getOneNosOriginUrl(e[c],s,i[c]))}Promise.all(a).then(function(e){r(s)})})},d.prototype.syncUnserialize=function(e,t){var n=this.unserializeMap[t],r={};if(e)for(var o in n)e.hasOwnProperty(o)&&(r[n[o]]=e[o]);return r};var g=new d({configMap:a,serializeMap:c,unserializeMap:u}),y=new d({configMap:l,serializeMap:m,unserializeMap:p});e.exports={IM:g,Chatroom:y}},function(e,t,n){"use strict";var r=n(15),o=r.getGlobal(),i={},s=o.name||"_parent",a=[],c=[];i.addMsgListener=function(e){a.push(e)};var u,l,m,p,d=(u=/^([\w]+?:\/\/.*?(?=\/|$))/i,function(e){return e=e||"",u.test(e)?RegExp.$1:"*"}),f=function(){var e=unescape(o.name||"").trim();if(e&&0===e.indexOf("MSG|")){o.name="";var t=r.string2object(e.replace("MSG|",""),"|"),n=(t.origin||"").toLowerCase();n&&"*"!==n&&0!==location.href.toLowerCase().indexOf(n)||function(e){for(var t=0,n=a.length;t<n;t++)try{a[t].call(null,e)}catch(e){}}({data:JSON.parse(t.data||"null"),source:o.frames[t.self]||t.self,origin:d(t.ref||("undefined"==typeof document?"":document.referrer))})}},g=(m=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return!0;return!1},function(){if(c.length){l=[];for(var e,t=c.length-1;t>=0;t--)e=c[t],m(l,e.w)||(l.push(e.w),c.splice(t,1),e.w.name=e.d);l=null}}),y=i.startTimer=(p=!1,function(){p||(p=!0,o.postMessage||(setInterval(g,100),setInterval(f,20)))});i.postMessage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r.fillUndef(t,{origin:"*",source:s}),o.postMessage){var n=t.data;o.FormData||(n=JSON.stringify(n)),e.postMessage(n,t.origin)}else{if(y(),r.isObject(t)){var i={};i.origin=t.origin||"",i.ref=location.href,i.self=t.source,i.data=JSON.stringify(t.data),t="MSG|"+r.object2string(i,"|",!0)}c.unshift({w:e,d:escape(t)})}},e.exports=i},,,,function(e,t,n){"use strict";var r=n(34),o=n(44),i=n(32),s=n(41),a=n(64),c=Object.assign;e.exports=!c||n(24)(function(){var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach(function(e){t[e]=e}),7!=c({},e)[n]||Object.keys(c({},t)).join("")!=r})?function(e,t){for(var n=s(e),c=arguments.length,u=1,l=o.f,m=i.f;c>u;)for(var p,d=a(arguments[u++]),f=l?r(d).concat(l(d)):r(d),g=f.length,y=0;g>y;)m.call(d,p=f[y++])&&(n[p]=d[p]);return n}:c},function(e,t,n){var r=n(14);r(r.S+r.F,"Object",{assign:n(100)})},function(e,t,n){n(101),e.exports=n(6).Object.assign},function(e,t,n){e.exports={default:n(102),__esModule:!0}},,function(e,t,n){"use strict";var r=n(5);"undefined"!=typeof window&&(window.console||r.isWeixinApp||(window.console={log:function(){},info:function(){},warn:function(){},error:function(){}}))},function(e,t,n){n(45)("observable")},function(e,t,n){n(45)("asyncIterator")},function(e,t,n){var r=n(21),o=n(63).f,i={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"[object Window]"==i.call(e)?function(e){try{return o(e)}catch(e){return s.slice()}}(e):o(r(e))}},function(e,t,n){var r=n(31);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){var r=n(34),o=n(44),i=n(32);e.exports=function(e){var t=r(e),n=o.f;if(n)for(var s,a=n(e),c=i.f,u=0;a.length>u;)c.call(e,s=a[u++])&&t.push(s);return t}},function(e,t,n){var r=n(33)("meta"),o=n(18),i=n(17),s=n(13).f,a=0,c=Object.isExtensible||function(){return!0},u=!n(24)(function(){return c(Object.preventExtensions({}))}),l=function(e){s(e,r,{value:{i:"O"+ ++a,w:{}}})},m=e.exports={KEY:r,NEED:!1,fastKey:function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,r)){if(!c(e))return"F";if(!t)return"E";l(e)}return e[r].i},getWeak:function(e,t){if(!i(e,r)){if(!c(e))return!0;if(!t)return!1;l(e)}return e[r].w},onFreeze:function(e){return u&&m.NEED&&c(e)&&!i(e,r)&&l(e),e}}},function(e,t,n){"use strict";var r=n(8),o=n(17),i=n(16),s=n(14),a=n(66),c=n(111).KEY,u=n(24),l=n(48),m=n(37),p=n(33),d=n(7),f=n(46),g=n(45),y=n(110),h=n(109),v=n(12),b=n(18),T=n(21),S=n(50),k=n(28),M=n(59),_=n(108),C=n(62),x=n(13),P=n(34),w=C.f,O=x.f,I=_.f,E=r.Symbol,A=r.JSON,N=A&&A.stringify,j=d("_hidden"),F=d("toPrimitive"),R={}.propertyIsEnumerable,U=l("symbol-registry"),L=l("symbols"),D=l("op-symbols"),q=Object.prototype,B="function"==typeof E,H=r.QObject,z=!H||!H.prototype||!H.prototype.findChild,W=i&&u(function(){return 7!=M(O({},"a",{get:function(){return O(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=w(q,t);r&&delete q[t],O(e,t,n),r&&e!==q&&O(q,t,r)}:O,J=function(e){var t=L[e]=M(E.prototype);return t._k=e,t},X=B&&"symbol"==typeof E.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof E},$=function(e,t,n){return e===q&&$(D,t,n),v(e),t=S(t,!0),v(n),o(L,t)?(n.enumerable?(o(e,j)&&e[j][t]&&(e[j][t]=!1),n=M(n,{enumerable:k(0,!1)})):(o(e,j)||O(e,j,k(1,{})),e[j][t]=!0),W(e,t,n)):O(e,t,n)},V=function(e,t){v(e);for(var n,r=y(t=T(t)),o=0,i=r.length;i>o;)$(e,n=r[o++],t[n]);return e},G=function(e){var t=R.call(this,e=S(e,!0));return!(this===q&&o(L,e)&&!o(D,e))&&(!(t||!o(this,e)||!o(L,e)||o(this,j)&&this[j][e])||t)},K=function(e,t){if(e=T(e),t=S(t,!0),e!==q||!o(L,t)||o(D,t)){var n=w(e,t);return!n||!o(L,t)||o(e,j)&&e[j][t]||(n.enumerable=!0),n}},Q=function(e){for(var t,n=I(T(e)),r=[],i=0;n.length>i;)o(L,t=n[i++])||t==j||t==c||r.push(t);return r},Y=function(e){for(var t,n=e===q,r=I(n?D:T(e)),i=[],s=0;r.length>s;)!o(L,t=r[s++])||n&&!o(q,t)||i.push(L[t]);return i};B||(a((E=function(){if(this instanceof E)throw TypeError("Symbol is not a constructor!");var e=p(arguments.length>0?arguments[0]:void 0),t=function(n){this===q&&t.call(D,n),o(this,j)&&o(this[j],e)&&(this[j][e]=!1),W(this,e,k(1,n))};return i&&z&&W(q,e,{configurable:!0,set:t}),J(e)}).prototype,"toString",function(){return this._k}),C.f=K,x.f=$,n(63).f=_.f=Q,n(32).f=G,n(44).f=Y,i&&!n(38)&&a(q,"propertyIsEnumerable",G,!0),f.f=function(e){return J(d(e))}),s(s.G+s.W+s.F*!B,{Symbol:E});for(var Z="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ee=0;Z.length>ee;)d(Z[ee++]);for(var te=P(d.store),ne=0;te.length>ne;)g(te[ne++]);s(s.S+s.F*!B,"Symbol",{for:function(e){return o(U,e+="")?U[e]:U[e]=E(e)},keyFor:function(e){if(!X(e))throw TypeError(e+" is not a symbol!");for(var t in U)if(U[t]===e)return t},useSetter:function(){z=!0},useSimple:function(){z=!1}}),s(s.S+s.F*!B,"Object",{create:function(e,t){return void 0===t?M(e):V(M(e),t)},defineProperty:$,defineProperties:V,getOwnPropertyDescriptor:K,getOwnPropertyNames:Q,getOwnPropertySymbols:Y}),A&&s(s.S+s.F*(!B||u(function(){var e=E();return"[null]"!=N([e])||"{}"!=N({a:e})||"{}"!=N(Object(e))})),"JSON",{stringify:function(e){for(var t,n,r=[e],o=1;arguments.length>o;)r.push(arguments[o++]);if(n=t=r[1],(b(t)||void 0!==e)&&!X(e))return h(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!X(t))return t}),r[1]=t,N.apply(A,r)}}),E.prototype[F]||n(20)(E.prototype,F,E.prototype.valueOf),m(E,"Symbol"),m(Math,"Math",!0),m(r.JSON,"JSON",!0)},function(e,t,n){n(112),n(76),n(107),n(106),e.exports=n(6).Symbol},function(e,t,n){e.exports={default:n(113),__esModule:!0}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t){e.exports=function(){}},function(e,t,n){"use strict";var r=n(116),o=n(115),i=n(25),s=n(21);e.exports=n(68)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,o(1)):o(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])},"values"),i.Arguments=i.Array,r("keys"),r("values"),r("entries")},function(e,t,n){var r=n(52),o=Math.max,i=Math.min;e.exports=function(e,t){return(e=r(e))<0?o(e+t,0):i(e,t)}},function(e,t,n){var r=n(21),o=n(60),i=n(118);e.exports=function(e){return function(t,n,s){var a,c=r(t),u=o(c.length),l=i(s,u);if(e&&n!=n){for(;u>l;)if((a=c[l++])!=a)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===n)return e||l||0;return!e&&-1}}},function(e,t,n){var r=n(13),o=n(12),i=n(34);e.exports=n(16)?Object.defineProperties:function(e,t){o(e);for(var n,s=i(t),a=s.length,c=0;a>c;)r.f(e,n=s[c++],t[n]);return e}},function(e,t,n){"use strict";var r=n(59),o=n(28),i=n(37),s={};n(20)(s,n(7)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r(s,{next:o(1,n)}),i(e,t+" Iterator")}},function(e,t,n){var r=n(52),o=n(51);e.exports=function(e){return function(t,n){var i,s,a=String(o(t)),c=r(n),u=a.length;return c<0||c>=u?e?"":void 0:(i=a.charCodeAt(c))<55296||i>56319||c+1===u||(s=a.charCodeAt(c+1))<56320||s>57343?e?a.charAt(c):i:e?a.slice(c,c+2):s-56320+(i-55296<<10)+65536}}},function(e,t,n){n(36),n(40),e.exports=n(46).f("iterator")},function(e,t,n){e.exports={default:n(123),__esModule:!0}},,,function(e,t,n){"use strict";var r=n(61),o=n(0),i=o.undef,s=o.notundef,a=n(5),c=n(95),u=n(299),l=n(202);function m(e){o.verifyOptions(e,"appKey account chatroomId chatroomAddresses","protocol::ChatroomProtocol"),e.isAnonymous||o.verifyOptions(e,"token","protocol::ChatroomProtocol"),o.verifyParamType("chatroomAddresses",e.chatroomAddresses,"array","protocol::ChatroomProtocol"),o.verifyCallback(e,"onconnect onerror onwillreconnect ondisconnect onmsg onmsgs onrobots","protocol::ChatroomProtocol"),r.call(this,e)}var p=r.fn,d=m.fn=m.prototype=Object.create(p);d.init=function(){p.init.call(this),c.Chatroom.setProtocol(this),this.parser=c.Chatroom,this.sendCmd.bind(this),this.syncResult={},this.timetags={},this.msgBuffer=[]},d.reset=function(){var e=this;p.reset.call(e);var t=e.options;i(t.msgBufferInterval)&&(t.msgBufferInterval=300),o.verifyParamType("msgBufferInterval",t.msgBufferInterval,"number","protocol::ChatroomProtocol.reset"),i(t.msgBufferSize)&&(t.msgBufferSize=500),o.verifyParamType("msgBufferSize",t.msgBufferSize,"number","protocol::ChatroomProtocol.reset"),s(t.chatroomAddresses)&&(e.socketUrls=t.chatroomAddresses.map(function(t){return a.formatSocketUrl({url:t,secure:e.options.secure})}),e.socketUrlsBackup=e.socketUrls.slice(0))},d.processChatroom=function(e){switch(e.cmd){case"login":e.error||(e.obj={chatroom:u.reverse(e.content.chatroom),member:l.reverse(e.content.chatroomMember)});break;case"kicked":this.onKicked(e);break;case"logout":break;case"sendMsg":this.onSendMsg(e);break;case"msg":this.onMsg(e);break;case"getChatroomMembers":this.onChatroomMembers(e);break;case"getHistoryMsgs":this.onHistoryMsgs(e);break;case"markChatroomMember":this.onMarkChatroomMember(e);break;case"closeChatroom":break;case"getChatroom":this.onChatroom(e);break;case"updateChatroom":break;case"updateMyChatroomMemberInfo":delete e.obj.chatroomMember;break;case"getChatroomMembersInfo":this.onChatroomMembersInfo(e);break;case"kickChatroomMember":case"updateChatroomMemberTempMute":break;case"queueList":e.error||(e.obj=e.content);break;case"syncRobot":this.onSyncRobot(e)}},d.onChatroom=function(e){e.error||(e.obj.chatroom=u.reverse(e.content.chatroom))},e.exports=m,n(420),n(419),n(418),n(417)},,,function(e,t,n){"use strict";var r,o=n(39);var i=((r=o)&&r.__esModule?r:{default:r}).default.clientTypeMap;function s(){}s.reverse=function(e){var t=e;return t.type=i[t.type],t},s.reverseType=function(e){return i[e]||e},e.exports=s},,,,,,function(e,t,n){"use strict";var r=n(74),o=n(79),i=n(0),s=n(5);function a(e){switch(i.notundef(e.type)?i.verifyFileType(e.type,"msg::FileMessage"):e.type="file",i.verifyOptions(e,"file","msg::FileMessage"),i.verifyOptions(e.file,"url ext size",!0,"file.","msg::FileMessage"),e.type){case"image":c.verifyFile(e.file,"msg::FileMessage");break;case"audio":u.verifyFile(e.file,"msg::FileMessage");break;case"video":l.verifyFile(e.file,"msg::FileMessage")}o.call(this,e),this.attach=JSON.stringify(e.file)}a.prototype=Object.create(o.prototype),a.reverse=function(e){var t=o.reverse(e);return e.attach=e.attach?""+e.attach:"",t.file=e.attach?JSON.parse(e.attach):{},t.file.url=(0,r.genPrivateUrl)(t.file.url),"audio"!==t.type||t.file.mp3Url||(t.file.mp3Url=t.file.url+(~t.file.url.indexOf("?")?"&":"?")+"audioTrans&type=mp3"),s.httpsEnabled&&0!==t.file.url.indexOf("https://")&&(t.file.url=t.file.url.replace("http","https")),t},e.exports=a;var c=n(414),u=n(413),l=n(412)},function(e,t,n){"use strict";var r=n(53),o=n(127),i=n(5),s=n(299),a=n(416),c=n(0),u=c.verifyOptions,l=c.verifyParamType,m=n(95).Chatroom;function p(e){return this.subType="chatroom",this.nosScene=e.nosScene||"chatroom",this.nosSurvivalTime=e.nosSurvivalTime,e.Protocol=o,e.Message=a,e.constructor=p,e.isAnonymous&&(e.account=e.account||"nimanon_"+c.guid(),e.isAnonymous=1,c.verifyOptions(e,"chatroomNick","api::Chatroom"),e.chatroomAvatar=e.chatroomAvatar||" "),this.init(e)}p.Protocol=o,p.parser=m,p.use=r.use,p.getInstance=function(e){return e.isAnonymous&&(e.account=e.account||"nimanon_"+c.guid(),e.isAnonymous=1,c.verifyOptions(e,"chatroomNick","api::Chatroom.getInstance"),e.chatroomAvatar=e.chatroomAvatar||" "),r.getInstance.call(this,e)},p.genInstanceName=function(e){return c.verifyOptions(e,"chatroomId","api::Chatroom.genInstanceName"),"Chatroom-account-"+e.account+"-chatroomId-"+e.chatroomId};var d=p.fn=p.prototype=Object.create(r.prototype);p.info=d.info=i.info,d.getChatroom=function(e){this.processCallback(e),this.sendCmd("getChatroom",e)},d.updateChatroom=function(e){u(e,"chatroom needNotify","api::updateChatroom"),l("needNotify",e.needNotify,"boolean"),this.processCustom(e),this.processCallback(e),e.chatroom=new s(e.chatroom),this.sendCmd("updateChatroom",e)},d.closeChatroom=function(e){this.processCustom(e),this.processCallback(e),this.sendCmd("closeChatroom",e)},e.exports=p,n(406),n(405),n(404)},,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(5),o=r.chunkSize,i=(n(0),n(58)),s={mp4:"video/mp4",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",mpeg:"video/mpeg",mov:"video/quicktime",aac:"audio/x-aac",wma:"audio/x-ms-wma",wav:"audio/x-wav",mp3:"audio/mp3"};e.exports=function e(t,n,a,c){var u={file:t.data[n],fileSize:t.data[n].size,fileUoloadedSize:0,percentage:0},l=t.url;function m(e){var n=u.fileUoloadedSize+e.loaded,r=Math.floor(1e4*n/u.fileSize)/100;if(parseInt(r)>=100&&(r=100,m=function(){}),u.percentage!==r){u.percentage=r;var o={docId:t.docId,total:u.fileSize,loaded:n,percentage:r,percentageText:r+"%"};t.fileInput&&(o.fileInput=t.fileInput),t.blob&&(o.blob=t.blob),t.uploadprogress(o)}}function p(e){try{e=JSON.parse(e)}catch(e){return void a.onError(e)}if(e.errMsg||e.errCode)a.onError(e);else if(e.offset<u.fileSize)delete y.onaftersend,u.fileUoloadedSize=e.offset,a.sn=function(e,t,n,r){var s=e.offset,a=e.offset+o;return t.data=r.file.slice(s,a),t.query.offset=e.offset,t.query.complete=a>=r.fileSize,t.query.context=e.context,t.onuploading=m,t.onload=p,t.onerror=d,i(l,t)}(e,y,0,u);else{var n=function(e){a.onError(e)},s=r.genFileUrl(t.nosToken);"image"===t.type?i(s+"?imageInfo",{onload:function(r){try{r=JSON.parse(r),t.uploaddone(null,{docId:e.docId,w:r.Width,h:r.Height,orientation:r.Orientation||"",type:r.Type,size:r.Size||u.fileSize})}catch(e){n(e)}},onerror:n}):"video"===t.type||"audio"===t.type?i(s+"?vinfo",{onload:function(r){try{(r=JSON.parse(r)).GetVideoInfo&&r.GetVideoInfo.VideoInfo&&(r=r.GetVideoInfo.VideoInfo),t.uploaddone(null,{docId:e.docId,w:r.Width,h:r.Height,dur:r.Duration,orientation:r.Rotate,audioCodec:r.AudioCodec,videoCodec:r.VideoCodec,container:r.Container,size:r.Size||u.fileSize})}catch(e){n(e)}},onerror:n}):t.uploaddone(null,{docId:e.docId,size:u.fileSize})}}function d(r){var o,s,l;function m(){try{if(r.result)var e=JSON.parse(r.result);else e=r;a.onError(e)}catch(e){a.onError(e)}}0===u.fileUoloadedSize&&t.lbsUrls?t.edgeList?c<t.edgeList.length-1?e(t,n,a,c+1):m():(o=0,s=t.nosToken.bucket,l=t.lbsUrls,new Promise(function(e,t){function n(){i(l[o],{query:{version:"1.0",bucketname:s},method:"GET",onerror:r,onload:function(t){try{(t=JSON.parse(t))&&t.upload&&t.upload.length?e(t.upload):r()}catch(e){r()}}})}function r(){o<l.length-1?(o++,n()):e([])}n()})).then(function(r){r.length>0?(t.edgeList=r,t.updateNosEdgeList&&t.updateNosEdgeList(r),e(t,n,a,c+1)):m()}):m()}"number"!=typeof c&&(c=-1),t.edgeList&&t.edgeList.length&&(c=c>0?c:0,l=t.edgeList[c]),l+="/"+t.nosToken.bucket+"/"+t.nosToken.objectName;var f=t.data.file&&t.data.file.type;if(!f||f.indexOf("/")<0){var g=(t.fileInputName||"").split(".").pop();"image"===t.type?f="image/"+("jpg"===g?"jpeg":g):"audio"!==t.type&&"video"!==t.type||(f=s[g])}var y={query:{offset:0,complete:o>=u.fileSize,version:"1.0"},headers:{"Content-Type":f||"application/octet-stream","x-nos-token":t.nosToken.token},method:"POST",timeout:0,onaftersend:function(){t.beginupload(a)},onuploading:m,onload:p,onerror:d};return y.data=u.file.slice(0,o),i(l,y)}},function(e,t,n){"use strict";var r=n(58);e.exports=function(e,t){return t.method="POST",t.headers=t.headers||{},t.headers["Content-Type"]="multipart/form-data",t.timeout=0,t.type=t.type||"json",r(e,t)}},function(e,t,n){"use strict";var r,o,i=n(15),s=n(58),a=(r=/json/i,o=/post/i,function(e,t){var n=(t=t||{}).data=t.data||{},a=t.headers=t.headers||{},c=i.checkWithDefault(a,"Accept","application/json"),u=i.checkWithDefault(a,"Content-Type","application/json");return r.test(c)&&(t.type="json"),o.test(t.method)&&r.test(u)&&(t.data=JSON.stringify(n)),s(e,t)});e.exports=a},function(e,t,n){"use strict";var r=n(15),o=n(96),i=n(72),s={};function a(e){this.init(),i.call(this,e)}var c=i.prototype,u=a.prototype=Object.create(c);u.init=function(){var e="NEJ-AJAX-DATA:",t=!1;function n(t){var n=t.data;if(0===n.indexOf(e)){var r=(n=JSON.parse(n.replace(e,""))).key,o=s[r];o&&(delete s[r],n.result=decodeURIComponent(n.result||""),o.onLoad(n))}}return function(){!function(){if(!t){t=!0;var e=r.getGlobal();e.postMessage?r.on(e,"message",n):o.addMsgListener(n)}}()}}(),u.doSend=function(){var e=this.options,t=r.url2origin(e.url),n=e.proxyUrl||t+"/res/nej_proxy_frame.html",i=s[n];if(r.isArray(i))i.push(this.doSend.bind(this,e));else{if(!i)return s[n]=[this.doSend.bind(this,e)],void r.createIframe({src:n,onload:function(e){var t=s[n];s[n]=r.target(e).contentWindow,t.forEach(function(e){try{e()}catch(e){console.log("error:",e)}})}});if(!this.aborted){var a=this.key=r.uniqueID();s[a]=this;var c=r.fetch({method:"GET",url:"",data:null,headers:{},timeout:0},e);c.key=a,o.postMessage(i,{data:c}),this.afterSend()}}},u.abort=function(){this.aborted=!0,delete s[this.key],c.abort.call(this)},e.exports=a},function(e,t,n){"use strict";var r=n(15),o=n(72),i=n(96),s="NEJ-UPLOAD-RESULT:",a={};function c(e){this.init(),o.call(this,e)}var u=o.prototype,l=c.prototype=Object.create(u);l.init=function(){var e=!1;function t(e){var t=e.data;if(0===t.indexOf(s)){var n=(t=JSON.parse(t.replace(s,""))).key,r=a[n];r&&(delete a[n],t.result=decodeURIComponent(t.result||""),r.onLoad(t.result))}}return function(){!function(){if(!e){e=!0;var n=r.getGlobal();n.postMessage?r.on(n,"message",t):(i.addMsgListener(t),i.startTimer())}}()}}(),l.doSend=function(){var e=this,t=e.options,n=e.key="zoro-ajax-upload-iframe-"+r.uniqueID();a[n]=e;var o=e.form=r.html2node('<form style="display:none;"></form>');"undefined"==typeof document?console.log("error: document is undefined"):document.body.appendChild(o),o.target=n,o.method="POST",o.enctype="multipart/form-data",o.encoding="multipart/form-data";var i=t.url,s=r.genUrlSep(i);o.action=i+s+"_proxy_=form";var c=t.data,u=[],l=[];function m(){u.forEach(function(e,t){var n=l[t];n.parentNode&&(e.name=n.name,r.isFunction(e.setAttribute)&&e.setAttribute("form",n.getAttribute("form")),n.parentNode.replaceChild(e,n))})}c&&r.getKeys(c,t.putFileAtEnd).forEach(function(e){var t=c[e];if(t.tagName&&"INPUT"===t.tagName.toUpperCase()){if("file"===t.type){var n=t,i=n.cloneNode(!0);n.parentNode.insertBefore(i,n);var s=r.dataset(n,"name");s&&(n.name=s),o.appendChild(n),r.isFunction(n.setAttribute)&&(n.setAttribute("form",""),n.removeAttribute("form")),u.push(t),l.push(i)}}else{var a=r.html2node('<input type="hidden"/>');a.name=e,a.value=t,o.appendChild(a)}});var p=e.iframe=r.createIframe({name:n,onload:function(){e.aborted?m():(r.on(p,"load",e.checkResult.bind(e)),o.submit(),m(),e.afterSend())}})},l.checkResult=function(){var e,t;try{if((t=((e=this.iframe.contentWindow.document.body).innerText||e.textContent||"").trim()).indexOf(s)>=0||e.innerHTML.indexOf(s)>=0)return}catch(e){return void console.log("error:","ignore error if not same domain,",e)}this.onLoad(t)},l.onLoad=function(e){u.onLoad.call(this,{status:200,result:e}),r.remove(this.form),r.remove(this.iframe),u.destroy.call(this)},l.destroy=function(){r.remove(this.iframe),r.remove(this.form)},l.abort=function(){this.aborted=!0,delete a[this.key],u.abort.call(this)},e.exports=c},function(e,t,n){var r;
/*!
 * EventEmitter v5.2.4 - git.io/ee
 * Unlicense - http://unlicense.org/
 * Oliver Caldwell - http://oli.me.uk/
 * @preserve
 */!function(t){"use strict";function o(){}var i=o.prototype,s=t.EventEmitter;function a(e,t){for(var n=e.length;n--;)if(e[n].listener===t)return n;return-1}function c(e){return function(){return this[e].apply(this,arguments)}}i.getListeners=function(e){var t,n,r=this._getEvents();if(e instanceof RegExp)for(n in t={},r)r.hasOwnProperty(n)&&e.test(n)&&(t[n]=r[n]);else t=r[e]||(r[e]=[]);return t},i.flattenListeners=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n.push(e[t].listener);return n},i.getListenersAsObject=function(e){var t,n=this.getListeners(e);return n instanceof Array&&((t={})[e]=n),t||n},i.addListener=function(e,t){if(!function e(t){return"function"==typeof t||t instanceof RegExp||!(!t||"object"!=typeof t)&&e(t.listener)}(t))throw new TypeError("listener must be a function");var n,r=this.getListenersAsObject(e),o="object"==typeof t;for(n in r)r.hasOwnProperty(n)&&-1===a(r[n],t)&&r[n].push(o?t:{listener:t,once:!1});return this},i.on=c("addListener"),i.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},i.once=c("addOnceListener"),i.defineEvent=function(e){return this.getListeners(e),this},i.defineEvents=function(e){for(var t=0;t<e.length;t+=1)this.defineEvent(e[t]);return this},i.removeListener=function(e,t){var n,r,o=this.getListenersAsObject(e);for(r in o)o.hasOwnProperty(r)&&-1!==(n=a(o[r],t))&&o[r].splice(n,1);return this},i.off=c("removeListener"),i.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},i.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},i.manipulateListeners=function(e,t,n){var r,o,i=e?this.removeListener:this.addListener,s=e?this.removeListeners:this.addListeners;if("object"!=typeof t||t instanceof RegExp)for(r=n.length;r--;)i.call(this,t,n[r]);else for(r in t)t.hasOwnProperty(r)&&(o=t[r])&&("function"==typeof o?i.call(this,r,o):s.call(this,r,o));return this},i.removeEvent=function(e){var t,n=typeof e,r=this._getEvents();if("string"===n)delete r[e];else if(e instanceof RegExp)for(t in r)r.hasOwnProperty(t)&&e.test(t)&&delete r[t];else delete this._events;return this},i.removeAllListeners=c("removeEvent"),i.emitEvent=function(e,t){var n,r,o,i,s=this.getListenersAsObject(e);for(i in s)if(s.hasOwnProperty(i))for(n=s[i].slice(0),o=0;o<n.length;o++)!0===(r=n[o]).once&&this.removeListener(e,r.listener),r.listener.apply(this,t||[])===this._getOnceReturnValue()&&this.removeListener(e,r.listener);return this},i.trigger=c("emitEvent"),i.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return this.emitEvent(e,t)},i.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},i._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},i._getEvents=function(){return this._events||(this._events={})},o.noConflict=function(){return t.EventEmitter=s,o},void 0===(r=function(){return o}.call(t,n,t,e))||(e.exports=r)}(this||{})},function(e,t,n){"use strict";var r=n(15),o=n(72);function i(e){e.onuploading&&this.on("uploading",e.onuploading),o.call(this,e)}var s=o.prototype,a=i.prototype=Object.create(s);a.doSend=function(){var e=this.options,t=e.headers,n=this.xhr=new XMLHttpRequest;if("multipart/form-data"===t["Content-Type"]){delete t["Content-Type"],n.upload.onprogress=this.onProgress.bind(this),n.upload.onload=this.onProgress.bind(this);var o=e.data;e.data=new window.FormData,o&&r.getKeys(o,e.putFileAtEnd).forEach(function(t){var n=o[t];n.tagName&&"INPUT"===n.tagName.toUpperCase()?"file"===n.type&&[].forEach.call(n.files,function(t){e.data.append(r.dataset(n,"name")||n.name||t.name||"file-"+r.uniqueID(),t)}):e.data.append(t,n)})}else t["x-nos-token"]&&(n.upload.onprogress=this.onProgress.bind(this),n.upload.onload=this.onProgress.bind(this));n.onreadystatechange=this.onStateChange.bind(this),0!==e.timeout&&(this.timer=setTimeout(this.onTimeout.bind(this),e.timeout)),n.open(e.method,e.url,!e.sync),Object.keys(t).forEach(function(e){n.setRequestHeader(e,t[e])}),e.cookie&&"withCredentials"in n&&(n.withCredentials=!0),n.send(e.data),this.afterSend()},a.onProgress=function(e){e.lengthComputable&&e.loaded<=e.total&&this.emit("uploading",e)},a.onStateChange=function(){var e=this.xhr;4===e.readyState&&this.onLoad({status:e.status,result:e.responseText||""})},a.getResponseHeader=function(e){var t=this.xhr;return t?t.getResponseHeader(e):""},a.destroy=function(){clearTimeout(this.timer);try{this.xhr.onreadystatechange=r.f,this.xhr.abort()}catch(e){console.log("error:","ignore error ajax destroy,",e)}s.destroy.call(this)},e.exports=i},function(e,t,n){"use strict";n(210).polyfill(),n(5).isBrowser=!0},,,,,,,,function(e,t){e.exports=function(e){var t=n.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)};var n=Object.prototype.toString},function(e,t,n){"use strict";var r=n(39);function o(){}o.typeMap={text:0,image:1,audio:2,video:3,geo:4,notification:5,file:6,tip:10,robot:11,custom:100};var i=o.typeReverseMap={0:"text",1:"image",2:"audio",3:"video",4:"geo",5:"notification",6:"file",10:"tip",11:"robot",100:"custom"};o.validTypes=Object.keys(o.typeMap),o.setFlow=function(e,t){var n=t===e.from;n&&t===e.to&&(n=r.deviceId===e.fromDeviceId),e.flow=n?"out":"in","robot"===e.type&&e.content&&e.content.msgOut&&(e.flow="in")},o.getType=function(e){var t=e.type;return i[t]||t},e.exports=o},function(module,exports,__webpack_require__){(function(global,module){var __WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;/*! Socket.IO.js build:0.9.11, development. Copyright(c) 2011 LearnBoost <<EMAIL>> MIT Licensed */function getGlobal(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0!==global?global:{}}var root=getGlobal(),io=module.exports;void 0===root.location&&(root.location=null),root.io?module&&(module.exports=io=root.io):root.io=io,function(){!function(e,t){var n=e;n.version="0.9.11",n.protocol=1,n.transports=[],n.j=[],n.sockets={},n.connect=function(e,r){var o,i,s=n.util.parseUri(e);t&&t.location&&(s.protocol=s.protocol||t.location.protocol.slice(0,-1),s.host=s.host||(t.document?t.document.domain:t.location.hostname),s.port=s.port||t.location.port),o=n.util.uniqueUri(s);var a={host:s.ipv6uri?"["+s.host+"]":s.host,secure:"https"===s.protocol,port:s.port||("https"===s.protocol?443:80),query:s.query||""};return n.util.merge(a,r),!a["force new connection"]&&n.sockets[o]||(i=new n.Socket(a)),!a["force new connection"]&&i&&(n.sockets[o]=i),(i=i||n.sockets[o]).of(s.path.length>1?s.path:"")}}(module.exports,root),function(e,t){var n=e.util={},r=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,o=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];n.parseUri=function(e){var t=e,n=e.indexOf("["),i=e.indexOf("]");-1!=n&&-1!=i&&(e=e.substring(0,n)+e.substring(n,i).replace(/:/g,";")+e.substring(i,e.length));for(var s=r.exec(e||""),a={},c=14;c--;)a[o[c]]=s[c]||"";return-1!=n&&-1!=i&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a},n.uniqueUri=function(e){var n=e.protocol,r=e.host,o=e.port;return"document"in t&&t.document?(r=r||document.domain,o=o||("https"==n&&"https:"!==document.location.protocol?443:document.location.port)):(r=r||"localhost",o||"https"!=n||(o=443)),(n||"http")+"://"+r+":"+(o||80)},n.query=function(e,t){var r=n.chunkQuery(e||""),o=[];for(var i in n.merge(r,n.chunkQuery(t||"")),r)r.hasOwnProperty(i)&&o.push(i+"="+r[i]);return o.length?"?"+o.join("&"):""},n.chunkQuery=function(e){for(var t,n={},r=e.split("&"),o=0,i=r.length;o<i;++o)(t=r[o].split("="))[0]&&(n[t[0]]=t[1]);return n};var i=!1;n.load=function(e){if("undefined"!=typeof document&&document&&"complete"===document.readyState||i)return e();n.on(t,"load",e,!1)},n.on=function(e,t,n,r){e.attachEvent?e.attachEvent("on"+t,n):e.addEventListener&&e.addEventListener(t,n,r)},n.request=function(e){if(e&&"undefined"!=typeof XDomainRequest&&!n.ua.hasCORS)return new XDomainRequest;if("undefined"!=typeof XMLHttpRequest&&(!e||n.ua.hasCORS))return new XMLHttpRequest;if(!e)try{return new(root[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}return null},void 0!==root&&n.load(function(){i=!0}),n.defer=function(e){if(!n.ua.webkit||"undefined"!=typeof importScripts)return e();n.load(function(){setTimeout(e,100)})},n.merge=function(e,t,r,o){var i,s=o||[],a=void 0===r?2:r;for(i in t)t.hasOwnProperty(i)&&n.indexOf(s,i)<0&&("object"==typeof e[i]&&a?n.merge(e[i],t[i],a-1,s):(e[i]=t[i],s.push(t[i])));return e},n.mixin=function(e,t){n.merge(e.prototype,t.prototype)},n.inherit=function(e,t){function n(){}n.prototype=t.prototype,e.prototype=new n},n.isArray=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},n.intersect=function(e,t){for(var r=[],o=e.length>t.length?e:t,i=e.length>t.length?t:e,s=0,a=i.length;s<a;s++)~n.indexOf(o,i[s])&&r.push(i[s]);return r},n.indexOf=function(e,t,n){var r=e.length;for(n=n<0?n+r<0?0:n+r:n||0;n<r&&e[n]!==t;n++);return r<=n?-1:n},n.toArray=function(e){for(var t=[],n=0,r=e.length;n<r;n++)t.push(e[n]);return t},n.ua={},n.ua.hasCORS="undefined"!=typeof XMLHttpRequest&&function(){try{var e=new XMLHttpRequest}catch(e){return!1}return null!=e.withCredentials}(),n.ua.webkit="undefined"!=typeof navigator&&/webkit/i.test(navigator.userAgent),n.ua.iDevice="undefined"!=typeof navigator&&/iPad|iPhone|iPod/i.test(navigator.userAgent)}(void 0!==io?io:module.exports,root),function(e,t){function n(){}e.EventEmitter=n,n.prototype.on=function(e,n){return this.$events||(this.$events={}),this.$events[e]?t.util.isArray(this.$events[e])?this.$events[e].push(n):this.$events[e]=[this.$events[e],n]:this.$events[e]=n,this},n.prototype.addListener=n.prototype.on,n.prototype.once=function(e,t){var n=this;function r(){n.removeListener(e,r),t.apply(this,arguments)}return r.listener=t,this.on(e,r),this},n.prototype.removeListener=function(e,n){if(this.$events&&this.$events[e]){var r=this.$events[e];if(t.util.isArray(r)){for(var o=-1,i=0,s=r.length;i<s;i++)if(r[i]===n||r[i].listener&&r[i].listener===n){o=i;break}if(o<0)return this;r.splice(o,1),r.length||delete this.$events[e]}else(r===n||r.listener&&r.listener===n)&&delete this.$events[e]}return this},n.prototype.removeAllListeners=function(e){return void 0===e?(this.$events={},this):(this.$events&&this.$events[e]&&(this.$events[e]=null),this)},n.prototype.listeners=function(e){return this.$events||(this.$events={}),this.$events[e]||(this.$events[e]=[]),t.util.isArray(this.$events[e])||(this.$events[e]=[this.$events[e]]),this.$events[e]},n.prototype.emit=function(e){if(!this.$events)return!1;var n=this.$events[e];if(!n)return!1;var r=Array.prototype.slice.call(arguments,1);if("function"==typeof n)n.apply(this,r);else{if(!t.util.isArray(n))return!1;for(var o=n.slice(),i=0,s=o.length;i<s;i++)o[i].apply(this,r)}return!0}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(exports,nativeJSON){"use strict";if(nativeJSON&&nativeJSON.parse)return exports.JSON={parse:nativeJSON.parse,stringify:nativeJSON.stringify};var JSON=exports.JSON={};function f(e){return e<10?"0"+e:e}function date(e,t){return isFinite(e.valueOf())?e.getUTCFullYear()+"-"+f(e.getUTCMonth()+1)+"-"+f(e.getUTCDate())+"T"+f(e.getUTCHours())+":"+f(e.getUTCMinutes())+":"+f(e.getUTCSeconds())+"Z":null}var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,r,o,i,s,a=gap,c=t[e];switch(c instanceof Date&&(c=date(e)),"function"==typeof rep&&(c=rep.call(t,e,c)),typeof c){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,s=[],"[object Array]"===Object.prototype.toString.apply(c)){for(i=c.length,n=0;n<i;n+=1)s[n]=str(n,c)||"null";return o=0===s.length?"[]":gap?"[\n"+gap+s.join(",\n"+gap)+"\n"+a+"]":"["+s.join(",")+"]",gap=a,o}if(rep&&"object"==typeof rep)for(i=rep.length,n=0;n<i;n+=1)"string"==typeof rep[n]&&(o=str(r=rep[n],c))&&s.push(quote(r)+(gap?": ":":")+o);else for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(o=str(r,c))&&s.push(quote(r)+(gap?": ":":")+o);return o=0===s.length?"{}":gap?"{\n"+gap+s.join(",\n"+gap)+"\n"+a+"}":"{"+s.join(",")+"}",gap=a,o}}JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("socket.io:: replacer cannot JSON.stringify");return str("",{"":e})},JSON.parse=function(text,reviver){var j;function walk(e,t){var n,r,o=e[t];if(o&&"object"==typeof o)for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(void 0!==(r=walk(o,n))?o[n]=r:delete o[n]);return reviver.call(e,t,o)}if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("socket.io:: reviver cannot JSON.parse")}}(void 0!==io?io:module.exports,"undefined"!=typeof JSON?JSON:void 0),function(e,t){var n=e.parser={},r=n.packets=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],o=n.reasons=["transport not supported","client not handshaken","unauthorized"],i=n.advice=["reconnect"],s=t.JSON,a=t.util.indexOf;n.encodePacket=function(e){var t=a(r,e.type),n=e.id||"",c=e.endpoint||"",u=e.ack,l=null;switch(e.type){case"error":var m=e.reason?a(o,e.reason):"",p=e.advice?a(i,e.advice):"";""===m&&""===p||(l=m+(""!==p?"+"+p:""));break;case"message":""!==e.data&&(l=e.data);break;case"event":var d={name:e.name};e.args&&e.args.length&&(d.args=e.args),l=s.stringify(d);break;case"json":l=s.stringify(e.data);break;case"connect":e.qs&&(l=e.qs);break;case"ack":l=e.ackId+(e.args&&e.args.length?"+"+s.stringify(e.args):"")}var f=[t,n+("data"==u?"+":""),c];return null!=l&&f.push(l),f.join(":")},n.encodePayload=function(e){var t="";if(1==e.length)return e[0];for(var n=0,r=e.length;n<r;n++){t+="�"+e[n].length+"�"+e[n]}return t};var c=/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/;n.decodePacket=function(e){if(!(a=e.match(c)))return{};var t=a[2]||"",n=(e=a[5]||"",{type:r[a[1]],endpoint:a[4]||""});switch(t&&(n.id=t,a[3]?n.ack="data":n.ack=!0),n.type){case"error":var a=e.split("+");n.reason=o[a[0]]||"",n.advice=i[a[1]]||"";break;case"message":n.data=e||"";break;case"event":try{var u=s.parse(e);n.name=u.name,n.args=u.args}catch(e){}n.args=n.args||[];break;case"json":try{n.data=s.parse(e)}catch(e){}break;case"connect":n.qs=e||"";break;case"ack":if((a=e.match(/^([0-9]+)(\+)?(.*)/))&&(n.ackId=a[1],n.args=[],a[3]))try{n.args=a[3]?s.parse(a[3]):[]}catch(e){}}return n},n.decodePayload=function(e){var t=function(e,t){for(var n=0,r=e;r<t.length;r++){if("�"==t.charAt(r))return n;n++}return n};if("�"==e.charAt(0)){for(var r=[],o=1,i="";o<e.length;o++)if("�"==e.charAt(o)){var s=e.substr(o+1).substr(0,i);if("�"!=e.charAt(o+1+Number(i))&&o+1+Number(i)!=e.length){var a=Number(i);l=t(o+a+1,e),s=e.substr(o+1).substr(0,a+l),o+=l}r.push(n.decodePacket(s)),o+=Number(i)+1,i=""}else i+=e.charAt(o);return r}return[n.decodePacket(e)]}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t){function n(e,t){this.socket=e,this.sessid=t}e.Transport=n,t.util.mixin(n,t.EventEmitter),n.prototype.heartbeats=function(){return!0},n.prototype.onData=function(e){if(this!==this.socket.transport)return this;if(this.clearCloseTimeout(),(this.socket.connected||this.socket.connecting||this.socket.reconnecting)&&this.setCloseTimeout(),""!==e){var n=t.parser.decodePayload(e);if(n&&n.length)for(var r=0,o=n.length;r<o;r++)this.onPacket(n[r])}return this},n.prototype.onPacket=function(e){return this.socket.setHeartbeatTimeout(),"heartbeat"==e.type?this.onHeartbeat():("connect"==e.type&&""==e.endpoint&&this.onConnect(),"error"==e.type&&"reconnect"==e.advice&&(this.isOpen=!1),this.socket.onPacket(e),this)},n.prototype.setCloseTimeout=function(){if(!this.closeTimeout){var e=this;this.closeTimeout=setTimeout(function(){e.onDisconnect()},this.socket.closeTimeout)}},n.prototype.onDisconnect=function(){return this.isOpen&&this.close(),this.clearTimeouts(),this.socket?(this.socket.transport===this?this.socket.onDisconnect():this.socket.setBuffer(!1),this):this},n.prototype.onConnect=function(){return this.socket.onConnect(),this},n.prototype.clearCloseTimeout=function(){this.closeTimeout&&(clearTimeout(this.closeTimeout),this.closeTimeout=null)},n.prototype.clearTimeouts=function(){this.clearCloseTimeout(),this.reopenTimeout&&clearTimeout(this.reopenTimeout)},n.prototype.packet=function(e){this.send(t.parser.encodePacket(e))},n.prototype.onHeartbeat=function(e){this.packet({type:"heartbeat"})},n.prototype.onOpen=function(){this.isOpen=!0,this.clearCloseTimeout(),this.socket.onOpen()},n.prototype.onClose=function(){this.isOpen=!1,this.socket.transport===this?this.socket.onClose():this.socket.setBuffer(!1),this.onDisconnect(),this.onDisconnectDone instanceof Function&&this.onDisconnectDone(null),this.onConnectionOver instanceof Function&&this.onConnectionOver(null)},n.prototype.onDisconnectDone=function(){},n.prototype.onConnectionOver=function(){},n.prototype.prepareUrl=function(){var e=this.socket.options;return this.scheme()+"://"+e.host+":"+e.port+"/"+e.resource+"/"+t.protocol+"/"+this.name+"/"+this.sessid},n.prototype.ready=function(e,t){t.call(this)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t,n){function r(e){if(this.options={port:80,secure:!1,document:"document"in n&&document,resource:"socket.io",transports:e.transports||t.transports,"connect timeout":1e4,"try multiple transports":!0,reconnect:!0,"reconnection delay":500,"reconnection limit":1/0,"reopen delay":3e3,"max reconnection attempts":10,"sync disconnect on unload":!1,"auto connect":!0,"flash policy port":10843,manualFlush:!1},t.util.merge(this.options,e),this.connected=!1,this.open=!1,this.connecting=!1,this.reconnecting=!1,this.namespaces={},this.buffer=[],this.doBuffer=!1,this.options["sync disconnect on unload"]&&(!this.isXDomain()||t.util.ua.hasCORS)){var r=this;t.util.on(n,"beforeunload",function(){r.disconnectSync()},!1)}this.options["auto connect"]&&this.connect()}function o(){}e.Socket=r,t.util.mixin(r,t.EventEmitter),r.prototype.of=function(e){return this.namespaces[e]||(this.namespaces[e]=new t.SocketNamespace(this,e),""!==e&&this.namespaces[e].packet({type:"connect"})),this.namespaces[e]},r.prototype.publish=function(){var e;for(var t in this.emit.apply(this,arguments),this.namespaces)this.namespaces.hasOwnProperty(t)&&(e=this.of(t)).$emit.apply(e,arguments)},r.prototype.handshake=function(e){var n=this,r=this.options;function i(t){t instanceof Error?(n.connecting=!1,n.onError(t.message)):e.apply(null,t.split(":"))}var s=["http"+(r.secure?"s":"")+":/",r.host+":"+r.port,r.resource,t.protocol,t.util.query(this.options.query,"t="+ +new Date)].join("/");if(this.isXDomain()&&!t.util.ua.hasCORS&&"undefined"!=typeof document&&document&&document.getElementsByTagName){var a=document.getElementsByTagName("script")[0],c=document.createElement("script");c.src=s+"&jsonp="+t.j.length,c.onreadystatechange=function(){"loaded"==this.readyState&&c.parentNode&&(c.parentNode.removeChild(c),n.connecting=!1,!n.reconnecting&&n.onError("Server down or port not open"),n.publish("handshake_failed"))},a.parentNode.insertBefore(c,a),t.j.push(function(e){i(e),c.parentNode.removeChild(c)})}else{var u=t.util.request();u.open("GET",s,!0),u.timeout=1e4,this.isXDomain()&&(u.withCredentials=!0),u.onreadystatechange=function(){4==u.readyState&&(u.onreadystatechange=o,200==u.status?i(u.responseText):403==u.status?(n.connecting=!1,n.onError(u.responseText),n.publish("handshake_failed")):(n.connecting=!1,!n.reconnecting&&n.onError(u.responseText),n.publish("handshake_failed")))},u.ontimeout=function(e){n.connecting=!1,!n.reconnecting&&n.onError(u.responseText),n.publish("handshake_failed")},u.send(null)}},r.prototype.connect=function(e){if(this.connecting)return this;var n=this;return n.connecting=!0,this.handshake(function(r,o,i,s){n.sessionid=r,n.closeTimeout=1e3*i,n.heartbeatTimeout=1e3*o,n.transports||(n.transports=n.origTransports=s?t.util.intersect(s.split(","),n.options.transports):n.options.transports),n.setHeartbeatTimeout(),n.once("connect",function(){clearTimeout(n.connectTimeoutTimer),n.connectTimeoutTimer=null,e&&"function"==typeof e&&e()}),n.doConnect()}),this},r.prototype.doConnect=function(){var e=this;if(e.transport&&e.transport.clearTimeouts(),e.transport=e.getTransport(e.transports),!e.transport)return e.publish("connect_failed");e.transport.ready(e,function(){e.connecting=!0,e.publish("connecting",e.transport.name),e.transport.open(),e.options["connect timeout"]&&(e.connectTimeoutTimer&&clearTimeout(e.connectTimeoutTimer),e.connectTimeoutTimer=setTimeout(e.tryNextTransport.bind(e),e.options["connect timeout"]))})},r.prototype.getTransport=function(e){for(var n,r=e||this.transports,o=0;n=r[o];o++){if(t.Transport[n]&&t.Transport[n].check(this)&&(!this.isXDomain()||t.Transport[n].xdomainCheck(this)))return new t.Transport[n](this,this.sessionid)}return null},r.prototype.tryNextTransport=function(){if(!this.connected&&(this.connecting=!1,this.options["try multiple transports"])){for(var e=this.transports;e.length>0&&e.splice(0,1)[0]!=this.transport.name;);e.length?this.doConnect():this.publish("connect_failed")}},r.prototype.setHeartbeatTimeout=function(){if(clearTimeout(this.heartbeatTimeoutTimer),!this.transport||this.transport.heartbeats()){var e=this;this.heartbeatTimeoutTimer=setTimeout(function(){e.transport&&e.transport.onClose()},this.heartbeatTimeout)}},r.prototype.packet=function(e){return this.connected&&!this.doBuffer?this.transport.packet(e):this.buffer.push(e),this},r.prototype.setBuffer=function(e){this.doBuffer=e,!e&&this.connected&&this.buffer.length&&(this.options.manualFlush||this.flushBuffer())},r.prototype.flushBuffer=function(){this.transport.payload(this.buffer),this.buffer=[]},r.prototype.disconnect=function(){return(this.connected||this.connecting)&&(this.open&&this.of("").packet({type:"disconnect"}),this.onDisconnect("booted")),this},r.prototype.disconnectSync=function(){var e=t.util.request(),n=["http"+(this.options.secure?"s":"")+":/",this.options.host+":"+this.options.port,this.options.resource,t.protocol,"",this.sessionid].join("/")+"/?disconnect=1";e.open("GET",n,!1),e.send(null),this.onDisconnect("booted")},r.prototype.isXDomain=function(){var e=n&&n.location||{},t=e.port||("https:"==e.protocol?443:80);return this.options.host!==e.hostname||this.options.port!=t},r.prototype.onConnect=function(){this.connected||(this.connected=!0,this.connecting=!1,this.doBuffer||this.setBuffer(!1),this.emit("connect"))},r.prototype.onOpen=function(){this.open=!0},r.prototype.onClose=function(){this.open=!1,clearTimeout(this.heartbeatTimeoutTimer)},r.prototype.onPacket=function(e){this.of(e.endpoint).onPacket(e)},r.prototype.onError=function(e){e&&e.advice&&"reconnect"===e.advice&&(this.connected||this.connecting)&&(this.disconnect(),this.options.reconnect&&this.reconnect()),this.publish("error",e&&e.reason?e.reason:e)},r.prototype.onDisconnect=function(e){var t=this.connected,n=this.connecting;this.connected=!1,this.connecting=!1,this.open=!1,(t||n)&&(this.transport.close(),this.transport.clearTimeouts(),t&&(this.publish("disconnect",e),"booted"!=e&&this.options.reconnect&&!this.reconnecting&&this.reconnect()),n&&(this.connectTimeoutTimer&&clearTimeout(this.connectTimeoutTimer),this.tryNextTransport()))},r.prototype.reconnect=function(){this.reconnecting=!0,this.reconnectionAttempts=0,this.reconnectionDelay=this.options["reconnection delay"];var e=this,t=this.options["max reconnection attempts"],n=this.options["try multiple transports"],r=this.options["reconnection limit"];function o(){if(e.connected){for(var t in e.namespaces)e.namespaces.hasOwnProperty(t)&&""!==t&&e.namespaces[t].packet({type:"connect"});e.publish("reconnect",e.transport.name,e.reconnectionAttempts)}clearTimeout(e.reconnectionTimer),e.removeListener("connect_failed",i),e.removeListener("connect",i),e.reconnecting=!1,delete e.reconnectionAttempts,delete e.reconnectionDelay,delete e.reconnectionTimer,delete e.redoTransports,e.options["try multiple transports"]=n}function i(){if(e.reconnecting)return e.connected?o():e.connecting&&e.reconnecting?e.reconnectionTimer=setTimeout(i,1e3):void(e.reconnectionAttempts++>=t?e.redoTransports?(e.publish("reconnect_failed"),o()):(e.on("connect_failed",i),e.options["try multiple transports"]=!0,e.transports=e.origTransports,e.transport=e.getTransport(),e.redoTransports=!0,e.connect()):(e.reconnectionDelay<r&&(e.reconnectionDelay*=2),e.connect(),e.publish("reconnecting",e.reconnectionDelay,e.reconnectionAttempts),e.reconnectionTimer=setTimeout(i,e.reconnectionDelay)))}this.options["try multiple transports"]=!1,this.reconnectionTimer=setTimeout(i,this.reconnectionDelay),this.on("connect",i)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t){function n(e,t){this.socket=e,this.name=t||"",this.flags={},this.json=new r(this,"json"),this.ackPackets=0,this.acks={}}function r(e,t){this.namespace=e,this.name=t}e.SocketNamespace=n,t.util.mixin(n,t.EventEmitter),n.prototype.$emit=t.EventEmitter.prototype.emit,n.prototype.of=function(){return this.socket.of.apply(this.socket,arguments)},n.prototype.packet=function(e){return e.endpoint=this.name,this.socket.packet(e),this.flags={},this},n.prototype.send=function(e,t){var n={type:this.flags.json?"json":"message",data:e};return"function"==typeof t&&(n.id=++this.ackPackets,n.ack=!0,this.acks[n.id]=t),this.packet(n)},n.prototype.emit=function(e){var t=Array.prototype.slice.call(arguments,1),n=t[t.length-1],r={type:"event",name:e};return"function"==typeof n&&(r.id=++this.ackPackets,r.ack="data",this.acks[r.id]=n,t=t.slice(0,t.length-1)),r.args=t,this.packet(r)},n.prototype.disconnect=function(){return""===this.name?this.socket.disconnect():(this.packet({type:"disconnect"}),this.$emit("disconnect")),this},n.prototype.onPacket=function(e){var n=this;function r(){n.packet({type:"ack",args:t.util.toArray(arguments),ackId:e.id})}switch(e.type){case"connect":this.$emit("connect");break;case"disconnect":""===this.name?this.socket.onDisconnect(e.reason||"booted"):this.$emit("disconnect",e.reason);break;case"message":case"json":var o=["message",e.data];"data"==e.ack?o.push(r):e.ack&&this.packet({type:"ack",ackId:e.id}),this.$emit.apply(this,o);break;case"event":o=[e.name].concat(e.args);"data"==e.ack&&o.push(r),this.$emit.apply(this,o);break;case"ack":this.acks[e.ackId]&&(this.acks[e.ackId].apply(this,e.args),delete this.acks[e.ackId]);break;case"error":console.error("SocketIO on packet error: ",e),e.advice?this.socket.onError(e):"unauthorized"===e.reason?this.$emit("connect_failed",e.reason):this.$emit("error",e.reason)}},r.prototype.send=function(){this.namespace.flags[this.name]=!0,this.namespace.send.apply(this.namespace,arguments)},r.prototype.emit=function(){this.namespace.flags[this.name]=!0,this.namespace.emit.apply(this.namespace,arguments)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t,n){function r(e){t.Transport.apply(this,arguments)}e.websocket=r,t.util.inherit(r,t.Transport),r.prototype.name="websocket",r.prototype.open=function(){var e,r=t.util.query(this.socket.options.query),o=this;return e||(e=n.MozWebSocket||n.WebSocket),this.websocket=new e(this.prepareUrl()+r),this.websocket.onopen=function(){o.onOpen(),o.socket.setBuffer(!1)},this.websocket.onmessage=function(e){o.onData(e.data)},this.websocket.onclose=function(){o.socket.setBuffer(!0),o.onClose()},this.websocket.onerror=function(e){o.onError(e)},this},t.util.ua.iDevice?r.prototype.send=function(e){var t=this;return setTimeout(function(){t.websocket.send(e)},0),this}:r.prototype.send=function(e){return this.websocket.send(e),this},r.prototype.payload=function(e){for(var t=0,n=e.length;t<n;t++)this.packet(e[t]);return this},r.prototype.close=function(){return this.websocket.close(),this},r.prototype.onError=function(e){this.socket.onError(e)},r.prototype.scheme=function(){return this.socket.options.secure?"wss":"ws"},r.check=function(){return"WebSocket"in n&&!("__addTask"in WebSocket)||"MozWebSocket"in n},r.xdomainCheck=function(){return!0},t.transports.push("websocket")}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t,n){function r(e){e&&(t.Transport.apply(this,arguments),this.sendBuffer=[])}function o(){}e.XHR=r,t.util.inherit(r,t.Transport),r.prototype.open=function(){return this.socket.setBuffer(!1),this.onOpen(),this.get(),this.setCloseTimeout(),this},r.prototype.payload=function(e){for(var n=[],r=0,o=e.length;r<o;r++)n.push(t.parser.encodePacket(e[r]));this.send(t.parser.encodePayload(n))},r.prototype.send=function(e){return this.post(e),this},r.prototype.post=function(e){var t=this;this.socket.setBuffer(!0),this.sendXHR=this.request("POST"),n.XDomainRequest&&this.sendXHR instanceof XDomainRequest?this.sendXHR.onload=this.sendXHR.onerror=function(){this.onload=o,t.socket.setBuffer(!1)}:this.sendXHR.onreadystatechange=function(){4==this.readyState&&(this.onreadystatechange=o,t.posting=!1,200==this.status?t.socket.setBuffer(!1):t.onClose())},this.sendXHR.send(e)},r.prototype.close=function(){return this.onClose(),this},r.prototype.request=function(e){var n=t.util.request(this.socket.isXDomain()),r=t.util.query(this.socket.options.query,"t="+ +new Date);if(n.open(e||"GET",this.prepareUrl()+r,!0),"POST"==e)try{n.setRequestHeader?n.setRequestHeader("Content-type","text/plain;charset=UTF-8"):n.contentType="text/plain"}catch(e){}return n},r.prototype.scheme=function(){return this.socket.options.secure?"https":"http"},r.check=function(e,r){try{var o=t.util.request(r),i=n.XDomainRequest&&o instanceof XDomainRequest,s=e&&e.options&&e.options.secure?"https:":"http:",a=n.location&&s!=n.location.protocol;if(o&&(!i||!a))return!0}catch(e){}return!1},r.xdomainCheck=function(e){return r.check(e,!0)}}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t,n){function r(){t.Transport.XHR.apply(this,arguments)}function o(){}e["xhr-polling"]=r,t.util.inherit(r,t.Transport.XHR),t.util.merge(r,t.Transport.XHR),r.prototype.name="xhr-polling",r.prototype.heartbeats=function(){return!1},r.prototype.open=function(){return t.Transport.XHR.prototype.open.call(this),!1},r.prototype.get=function(){if(this.isOpen){var e=this;this.xhr=this.request(),n.XDomainRequest&&this.xhr instanceof XDomainRequest?(this.xhr.onload=function(){this.onload=o,this.onerror=o,e.retryCounter=1,e.onData(this.responseText),e.get()},this.xhr.onerror=function(){e.retryCounter++,!e.retryCounter||e.retryCounter>3?e.onClose():e.get()}):this.xhr.onreadystatechange=function(){4==this.readyState&&(this.onreadystatechange=o,200==this.status?(e.onData(this.responseText),e.get()):e.onClose())},this.xhr.send(null)}},r.prototype.onClose=function(){if(t.Transport.XHR.prototype.onClose.call(this),this.xhr){this.xhr.onreadystatechange=this.xhr.onload=this.xhr.onerror=o;try{this.xhr.abort()}catch(e){}this.xhr=null}},r.prototype.ready=function(e,n){var r=this;t.util.defer(function(){n.call(r)})},t.transports.push("xhr-polling")}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_RESULT__=function(){return io}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)}()}).call(this,__webpack_require__(35),__webpack_require__(320)(module))},function(e,t,n){"use strict";var r=n(54),o=n(19),i="https://statistic.live.126.net/statics/report/common/form",s="nimErrEvent",a={reportErrEventUrl:i,localKey:s,reportErrEvent:function(e){try{var t=localStorage.getItem(s);if(!t)return;t=JSON.parse(t);var n=[];Object.keys(t).forEach(function(e){n.push(t[e])});var a={app_key:e.appKey,sdk_ver:e.sdk_ver,platform:"Web",os_ver:o.os.family+" "+o.os.version,manufacturer:o.manufacturer,model:o.name};r(i,{method:"POST",timeout:2e3,headers:{"Content-Type":"application/json"},data:JSON.stringify({common:{device_id:e.deviceId,sdk_type:"IM"},event:{logReport:n,deviceinfo:a}}),onload:function(){localStorage.removeItem(s)},onerror:function(e){}})}catch(e){}},saveErrEvent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e.code&&e.module)try{var t=localStorage.getItem(s)||"{}";t=JSON.parse(t);var n=e.code+e.module+e.accid;t[n]?t[n].count++:t[n]={errorCode:e.code,module:e.module,accid:e.accid,timestamp:(new Date).getTime(),count:1},localStorage.setItem(s,JSON.stringify(t))}catch(e){}}};e.exports=a},function(e,t,n){"use strict";var r={1:"ROOM_CLOSE",2:"ROOM_JOIN",3:"INVITE",4:"CANCEL_INVITE",5:"REJECT",6:"ACCEPT",7:"LEAVE",8:"CONTROL"},o={1:"accid",2:"uid",3:"createTime",4:"expireTime",5:"web_uid"},i={10404:"ROOM_NOT_EXISTS",10405:"ROOM_HAS_EXISTS",10406:"ROOM_MEMBER_NOT_EXISTS",10407:"ROOM_MEMBER_HAS_EXISTS",10408:"INVITE_NOT_EXISTS",10409:"INVITE_HAS_REJECT",10410:"INVITE_HAS_ACCEPT",10201:"PEER_NIM_OFFLINE",10202:"PEER_PUSH_OFFLINE",10419:"ROOM_MEMBER_EXCEED",10420:"ROOM_MEMBER_HAS_EXISTS_OTHER_CLIENT",10417:"UID_CONFLICT"};e.exports={parseAvSignalType:function(e){return r[e]||e},parseAvSignalMember:function(e){var t={};return Object.keys(e).forEach(function(n){t[o[n]]=e[n]}),t},parseAvSignalError:function(e){return e.message=i[e.code]||e.message||e,e}}},function(e,t,n){"use strict";var r=n(0),o={stripmeta:0,blur:2,quality:3,crop:4,rotate:5,thumbnail:7,interlace:9},i={0:"stripmeta",1:"type",2:"blur",3:"quality",4:"crop",5:"rotate",6:"pixel",7:"thumbnail",8:"watermark",9:"interlace",10:"tmp"};function s(e){r.verifyOptions(e,"type","image::ImageOp"),r.verifyParamValid("type",e.type,s.validTypes,"image::ImageOp"),r.merge(this,e),this.type=o[e.type]}s.validTypes=Object.keys(o),s.reverse=function(e){var t=r.copy(e);return t.type=i[t.type],t},s.reverseImageOps=function(e){return e.map(function(e){return s.reverse(e)})},e.exports=s},function(e,t,n){"use strict";var r=n(0),o={fromDataURL:function(e){var t=r.getGlobal(),n=void 0;n=e.split(",")[0].indexOf("base64")>=0?t.atob(e.split(",")[1]):t.decodeURIComponent(e.split(",")[1]);for(var o=e.split(",")[0].split(":")[1].split(";")[0],i=new Uint8Array(n.length),s=0;s<n.length;s++)i[s]=n.charCodeAt(s);return new t.Blob([i],{type:o})}};e.exports=o},function(e,t,n){"use strict";var r=n(0),o={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},i={genResponseBody:function(e){return o[e=e||"file"]},parseResponse:function(e,t){r.notundef(e.size)&&(e.size=+e.size),r.notundef(e.w)&&(e.w=+e.w),r.notundef(e.h)&&(e.h=+e.h),r.notundef(e.dur)&&(e.dur=+e.dur);var n=e.orientation;if(r.notundef(n)&&(delete e.orientation,t&&("right, top"===n||"left, bottom"===n))){var o=e.w;e.w=e.h,e.h=o}return e}};e.exports=i},,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(74),o=n(0),i=o.notundef,s={"-2":"unset","-1":"restricted",0:"common",1:"owner",2:"manager",3:"guest",4:"anonymous"};function a(e){i(e.nick)&&(this.nick=""+e.nick),i(e.avatar)&&(this.avatar=""+e.avatar),i(e.custom)&&(this.custom=""+e.custom)}a.reverse=function(e){var t=o.copy(e);return i(t.chatroomId)&&(t.chatroomId=""+t.chatroomId),i(t.avatar)&&(t.avatar=(0,r.genPrivateUrl)(t.avatar)),i(t.type)&&(t.type=s[t.type]),i(t.level)&&(t.level=+t.level),i(t.online)&&(t.online=1==+t.online),i(t.enterTime)&&(t.enterTime=+t.enterTime),i(t.guest)&&(t.guest=1==+t.guest),i(t.blacked)&&(t.blacked=1==+t.blacked),i(t.gaged)&&(t.gaged=1==+t.gaged),i(t.valid)&&(t.valid=1==+t.valid),i(t.updateTime)&&(t.updateTime=+t.updateTime),i(t.tempMuted)?t.tempMuted=1==+t.tempMuted:t.tempMuted=!1,i(t.tempMuteDuration)?t.tempMuteDuration=+t.tempMuteDuration:t.tempMuteDuration=0,t.online||delete t.enterTime,t.guest&&(t.type="guest",delete t.valid),"common"!==t.type&&delete t.level,delete t.guest,t},a.reverseMembers=function(e){return e.map(function(e){return a.reverse(e)})},a.validTypes=Object.keys(s),a.typeReverseMap=s,e.exports=a},function(e,t,n){"use strict";var r=n(19),o=n(307),i=n(182),s=n(81),a=n(75),c=n(0),u=n(208),l=n(186),m=n(54),p=n(301),d=n(300);e.exports=function(e){c.merge(e,{platform:r,xhr:o,io:i,naturalSort:s,deepAccess:a,util:c,support:u,blob:l,ajax:m,LoggerPlugin:p,usePlugin:d})}},function(e,t,n){"use strict";var r=n(0),o=n(86),i=r.merge({},o.idMap,{auth:{id:2,login:3,kicked:5,logout:6,multiPortLogin:7,kick:8},user:{id:3,updatePushToken:1,appBackground:2,markInBlacklist:3,getBlacklist:4,markInMutelist:5,getMutelist:6,getRelations:8,getUsers:7,updateMyInfo:10,updateDonnop:15,syncMyInfo:109,syncUpdateMyInfo:110},notify:{id:4,markRead:3,syncOfflineMsgs:4,batchMarkRead:5,syncOfflineSysMsgs:6,syncOfflineNetcallMsgs:8,syncRoamingMsgs:9,syncMsgReceipts:12,syncRobots:15,syncBroadcastMsgs:16,syncSuperTeamRoamingMsgs:17,syncOfflineSuperTeamSysMsgs:18,syncDeleteSuperTeamMsgOfflineRoaming:19,syncDeleteMsgSelf:21,syncSessionsWithMoreRoaming:22,syncStickTopSessions:23,syncSessionHistoryMsgsDelete:24},sync:{id:5,sync:1,syncTeamMembers:2,syncSuperTeamMembers:3},msg:{id:7,sendMsg:1,msg:2,sysMsg:3,getHistoryMsgs:6,sendCustomSysMsg:7,searchHistoryMsgs:8,deleteSessions:9,getSessions:10,syncSendMsg:101,sendMsgReceipt:11,msgReceipt:12,deleteMsg:13,msgDeleted:14,markSessionAck:16,broadcastMsg:17,clearServerHistoryMsgs:18,getServerSessions:19,getServerSession:20,updateServerSession:21,deleteServerSessions:22,deleteMsgSelf:23,deleteMsgSelfBatch:24,onClearServerHistoryMsgs:118,syncUpdateServerSession:121,onDeleteMsgSelf:123,onDeleteMsgSelfBatch:124},msgExtend:{id:23,getThreadMsgs:1,getMsgsByIdServer:2,addQuickComment:3,deleteQuickComment:4,onQuickComment:5,onDeleteQuickComment:6,getQuickComments:7,addCollect:8,deleteCollects:9,updateCollect:10,getCollects:11,addStickTopSession:12,deleteStickTopSession:13,updateStickTopSession:14,addMsgPin:15,updateMsgPin:16,deleteMsgPin:17,onAddMsgPin:18,onUpdateMsgPin:19,onDeleteMsgPin:20,getMsgPins:21,syncAddQuickComment:103,syncDeleteQuickComment:104,syncAddStickTopSession:112,syncDeleteStickTopSession:113,syncUpdateStickTopSession:114,syncAddMsgPin:115,syncUpdateMsgPin:116,syncDeleteMsgPin:117},team:{id:8,createTeam:1,sendTeamMsg:2,teamMsg:3,teamMsgs:4,addTeamMembers:5,removeTeamMembers:6,updateTeam:7,leaveTeam:8,getTeam:9,getTeams:10,getTeamMembers:11,dismissTeam:12,applyTeam:13,passTeamApply:14,rejectTeamApply:15,addTeamManagers:16,removeTeamManagers:17,transferTeam:18,updateInfoInTeam:19,updateNickInTeam:20,acceptTeamInvite:21,rejectTeamInvite:22,getTeamHistoryMsgs:23,searchTeamHistoryMsgs:24,updateMuteStateInTeam:25,getMyTeamMembers:26,getMutedTeamMembers:27,sendTeamMsgReceipt:28,getTeamMsgReads:29,getTeamMsgReadAccounts:30,notifyTeamMsgReads:31,muteTeamAll:32,getTeamMemberInvitorAccid:33,syncMyTeamMembers:126,syncTeams:109,syncTeamMembers:111,syncCreateTeam:101,syncSendTeamMsg:102,syncUpdateTeamMember:119},superTeam:{id:21,sendSuperTeamMsg:2,superTeamMsg:3,addSuperTeamMembers:5,removeSuperTeamMembers:6,leaveSuperTeam:7,updateSuperTeam:8,getSuperTeam:9,getSuperTeams:12,updateInfoInSuperTeam:10,getMySuperTeamMembers:11,getSuperTeamMembers:13,getSuperTeamHistoryMsgs:14,getSuperTeamMembersByJoinTime:15,sendSuperTeamCustomSysMsg:16,deleteSuperTeamMsg:17,superTeamMsgDelete:18,superTeamCustomSysMsg:19,applySuperTeam:20,passSuperTeamApply:21,rejectSuperTeamApply:22,acceptSuperTeamInvite:23,rejectSuperTeamInvite:24,markSuperTeamSessionAck:25,addSuperTeamManagers:26,removeSuperTeamManagers:27,updateSuperTeamMute:28,updateSuperTeamMembersMute:29,updateNickInSuperTeam:30,transferSuperTeam:31,syncMySuperTeamMembers:111,syncSuperTeams:109,syncSuperTeamMembers:113,syncCreateSuperTeam:101,syncSendSuperTeamMsg:102,syncUpdateSuperTeamMember:110,syncDeleteSuperTeamMsg:117},friend:{id:12,friendRequest:1,syncFriendRequest:101,deleteFriend:2,syncDeleteFriend:102,updateFriend:3,syncUpdateFriend:103,getFriends:4},chatroom:{id:13,getChatroomAddress:1},filter:{id:101,sendFilterMsg:1,filterMsg:2,filterSysMsg:3,sendFilterCustomSysMsg:7},eventService:{id:14,publishEvent:1,pushEvent:2,subscribeEvent:3,unSubscribeEventsByAccounts:4,unSubscribeEventsByType:5,querySubscribeEventsByAccounts:6,querySubscribeEventsByType:7,pushEvents:9}}),s=r.merge({},o.cmdConfig,{login:{sid:i.auth.id,cid:i.auth.login,params:[{type:"Property",name:"login"}]},logout:{sid:i.auth.id,cid:i.auth.logout},kick:{sid:i.auth.id,cid:i.auth.kick,params:[{type:"StrArray",name:"deviceIds"}]},updatePushToken:{sid:i.user.id,cid:i.user.updatePushToken,params:[{type:"String",name:"tokenName"},{type:"String",name:"token"},{type:"int",name:"pushkit"}]},appBackground:{sid:i.user.id,cid:i.user.appBackground,params:[{type:"bool",name:"isBackground"},{type:"Int",name:"badge"}]},markInBlacklist:{sid:i.user.id,cid:i.user.markInBlacklist,params:[{type:"String",name:"account"},{type:"bool",name:"isAdd"}]},getBlacklist:{sid:i.user.id,cid:i.user.getBlacklist,params:[{type:"long",name:"time"}]},markInMutelist:{sid:i.user.id,cid:i.user.markInMutelist,params:[{type:"String",name:"account"},{type:"bool",name:"isAdd"}]},getMutelist:{sid:i.user.id,cid:i.user.getMutelist,params:[{type:"long",name:"time"}]},getRelations:{sid:i.user.id,cid:i.user.getRelations,params:[{type:"long",name:"timetag"}]},getUsers:{sid:i.user.id,cid:i.user.getUsers,params:[{type:"StrArray",name:"accounts"}]},updateMyInfo:{sid:i.user.id,cid:i.user.updateMyInfo,params:[{type:"Property",name:"user"}]},updateDonnop:{sid:i.user.id,cid:i.user.updateDonnop,params:[{type:"Property",name:"donnop"}]},markRead:{sid:i.notify.id,cid:i.notify.markRead,params:[{type:"long",name:"id"},{type:"ph",name:"ph"}]},batchMarkRead:{sid:i.notify.id,cid:i.notify.batchMarkRead,params:[{type:"byte",name:"sid"},{type:"byte",name:"cid"},{type:"LongArray",name:"ids"}]},sync:{sid:i.sync.id,cid:i.sync.sync,params:[{type:"Property",name:"sync"}]},syncTeamMembers:{sid:i.sync.id,cid:i.sync.syncTeamMembers,params:[{type:"LongLongMap",name:"sync"}]},syncSuperTeamMembers:{sid:i.sync.id,cid:i.sync.syncSuperTeamMembers,params:[{type:"LongLongMap",name:"sync"}]},sendMsg:{sid:i.msg.id,cid:i.msg.sendMsg,params:[{type:"Property",name:"msg"}]},getHistoryMsgs:{sid:i.msg.id,cid:i.msg.getHistoryMsgs,params:[{type:"String",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},sendCustomSysMsg:{sid:i.msg.id,cid:i.msg.sendCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},searchHistoryMsgs:{sid:i.msg.id,cid:i.msg.searchHistoryMsgs,params:[{type:"String",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"String",name:"keyword"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},getSessions:{sid:i.msg.id,cid:i.msg.getSessions,params:[{type:"long",name:"time"}]},deleteSessions:{sid:i.msg.id,cid:i.msg.deleteSessions,params:[{type:"StrArray",name:"sessions"}]},sendMsgReceipt:{sid:i.msg.id,cid:i.msg.sendMsgReceipt,params:[{type:"Property",name:"msgReceipt"}]},deleteMsg:{sid:i.msg.id,cid:i.msg.deleteMsg,params:[{type:"Property",name:"sysMsg"}]},markSessionAck:{sid:i.msg.id,cid:i.msg.markSessionAck,params:[{type:"byte",name:"scene"},{type:"String",name:"to"},{type:"long",name:"timetag"}]},clearServerHistoryMsgs:{sid:i.msg.id,cid:i.msg.clearServerHistoryMsgs,params:[{type:"Property",name:"clearMsgsParams"}]},clearServerHistoryMsgsWithSync:{sid:i.msg.id,cid:i.msg.clearServerHistoryMsgs,params:[{type:"Property",name:"clearMsgsParamsWithSync"}]},onClearServerHistoryMsgs:{sid:i.msg.id,cid:i.msg.clearServerHistoryMsgs},getServerSessions:{sid:i.msg.id,cid:i.msg.getServerSessions,params:[{type:"Property",name:"sessionReqTag"}]},getServerSession:{sid:i.msg.id,cid:i.msg.getServerSession,params:[{type:"Property",name:"session"}]},updateServerSession:{sid:i.msg.id,cid:i.msg.updateServerSession,params:[{type:"Property",name:"session"}]},deleteServerSessions:{sid:i.msg.id,cid:i.msg.deleteServerSessions,params:[{type:"PropertyArray",name:"sessions",entity:"session"}]},deleteMsgSelf:{sid:i.msg.id,cid:i.msg.deleteMsgSelf,params:[{type:"Property",name:"deleteMsgSelfTag"}]},deleteMsgSelfBatch:{sid:i.msg.id,cid:i.msg.deleteMsgSelfBatch,params:[{type:"PropertyArray",name:"deleteMsgSelfTags",entity:"deleteMsgSelfTag"}]},onDeleteMsgSelf:{sid:i.msg.id,cid:i.msg.onDeleteMsgSelf},onDeleteMsgSelfBatch:{sid:i.msg.id,cid:i.msg.onDeleteMsgSelfBatch},sendSuperTeamMsg:{sid:i.superTeam.id,cid:i.superTeam.sendSuperTeamMsg,params:[{type:"Property",name:"msg"}]},addSuperTeamMembers:{sid:i.superTeam.id,cid:i.superTeam.addSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"}]},removeSuperTeamMembers:{sid:i.superTeam.id,cid:i.superTeam.removeSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},leaveSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.leaveSuperTeam,params:[{type:"long",name:"teamId"}]},updateSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.updateSuperTeam,params:[{type:"Property",name:"team"}]},getSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.getSuperTeam,params:[{type:"long",name:"teamId"}]},getSuperTeams:{sid:i.superTeam.id,cid:i.superTeam.getSuperTeams,params:[{type:"long",name:"timetag"}]},getSuperTeamMembers:{sid:i.superTeam.id,cid:i.superTeam.getSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},updateInfoInSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.updateInfoInSuperTeam,params:[{type:"Property",name:"superTeamMember"}]},getSuperTeamHistoryMsgs:{sid:i.superTeam.id,cid:i.superTeam.getSuperTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},applySuperTeam:{sid:i.superTeam.id,cid:i.superTeam.applySuperTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"ps"}]},passSuperTeamApply:{sid:i.superTeam.id,cid:i.superTeam.passSuperTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectSuperTeamApply:{sid:i.superTeam.id,cid:i.superTeam.rejectSuperTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},acceptSuperTeamInvite:{sid:i.superTeam.id,cid:i.superTeam.acceptSuperTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectSuperTeamInvite:{sid:i.superTeam.id,cid:i.superTeam.rejectSuperTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},markSuperTeamSessionAck:{sid:i.superTeam.id,cid:i.superTeam.markSuperTeamSessionAck,params:[{type:"long",name:"to"},{type:"long",name:"timetag"}]},addSuperTeamManagers:{sid:i.superTeam.id,cid:i.superTeam.addSuperTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},removeSuperTeamManagers:{sid:i.superTeam.id,cid:i.superTeam.removeSuperTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},updateSuperTeamMute:{sid:i.superTeam.id,cid:i.superTeam.updateSuperTeamMute,params:[{type:"long",name:"teamId"},{type:"int",name:"mute"}]},updateSuperTeamMembersMute:{sid:i.superTeam.id,cid:i.superTeam.updateSuperTeamMembersMute,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"int",name:"mute"}]},updateNickInSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.updateNickInSuperTeam,params:[{type:"Property",name:"superTeamMember"}]},transferSuperTeam:{sid:i.superTeam.id,cid:i.superTeam.transferSuperTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"bool",name:"leave"}]},getSuperTeamMembersByJoinTime:{sid:i.superTeam.id,cid:i.superTeam.getSuperTeamMembersByJoinTime,params:[{type:"long",name:"teamId"},{type:"long",name:"joinTime"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},sendSuperTeamCustomSysMsg:{sid:i.superTeam.id,cid:i.superTeam.sendSuperTeamCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},deleteSuperTeamMsg:{sid:i.superTeam.id,cid:i.superTeam.deleteSuperTeamMsg,params:[{type:"Property",name:"sysMsg"}]},getMySuperTeamMembers:{sid:i.superTeam.id,cid:i.superTeam.getMySuperTeamMembers,params:[{type:"LongArray",name:"teamIds"}]},createTeam:{sid:i.team.id,cid:i.team.createTeam,params:[{type:"Property",name:"team"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"}]},sendTeamMsg:{sid:i.team.id,cid:i.team.sendTeamMsg,params:[{type:"Property",name:"msg"}]},addTeamMembers:{sid:i.team.id,cid:i.team.addTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"},{type:"String",name:"attach"}]},removeTeamMembers:{sid:i.team.id,cid:i.team.removeTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},updateTeam:{sid:i.team.id,cid:i.team.updateTeam,params:[{type:"Property",name:"team"}]},leaveTeam:{sid:i.team.id,cid:i.team.leaveTeam,params:[{type:"long",name:"teamId"}]},getTeam:{sid:i.team.id,cid:i.team.getTeam,params:[{type:"long",name:"teamId"}]},getTeams:{sid:i.team.id,cid:i.team.getTeams,params:[{type:"long",name:"timetag"}]},getTeamMembers:{sid:i.team.id,cid:i.team.getTeamMembers,params:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},dismissTeam:{sid:i.team.id,cid:i.team.dismissTeam,params:[{type:"long",name:"teamId"}]},applyTeam:{sid:i.team.id,cid:i.team.applyTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"ps"}]},passTeamApply:{sid:i.team.id,cid:i.team.passTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectTeamApply:{sid:i.team.id,cid:i.team.rejectTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},addTeamManagers:{sid:i.team.id,cid:i.team.addTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},removeTeamManagers:{sid:i.team.id,cid:i.team.removeTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},transferTeam:{sid:i.team.id,cid:i.team.transferTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"bool",name:"leave"}]},updateInfoInTeam:{sid:i.team.id,cid:i.team.updateInfoInTeam,params:[{type:"Property",name:"teamMember"}]},updateNickInTeam:{sid:i.team.id,cid:i.team.updateNickInTeam,params:[{type:"Property",name:"teamMember"}]},acceptTeamInvite:{sid:i.team.id,cid:i.team.acceptTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectTeamInvite:{sid:i.team.id,cid:i.team.rejectTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},getTeamHistoryMsgs:{sid:i.team.id,cid:i.team.getTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},searchTeamHistoryMsgs:{sid:i.team.id,cid:i.team.searchTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"String",name:"keyword"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},updateMuteStateInTeam:{sid:i.team.id,cid:i.team.updateMuteStateInTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"int",name:"mute"}]},getMyTeamMembers:{sid:i.team.id,cid:i.team.getMyTeamMembers,params:[{type:"LongArray",name:"teamIds"}]},getMutedTeamMembers:{sid:i.team.id,cid:i.team.getMutedTeamMembers,params:[{type:"long",name:"teamId"}]},sendTeamMsgReceipt:{sid:i.team.id,cid:i.team.sendTeamMsgReceipt,params:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},getTeamMsgReads:{sid:i.team.id,cid:i.team.getTeamMsgReads,params:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},getTeamMsgReadAccounts:{sid:i.team.id,cid:i.team.getTeamMsgReadAccounts,params:[{type:"Property",name:"teamMsgReceipt"}]},muteTeamAll:{sid:i.team.id,cid:i.team.muteTeamAll,params:[{type:"long",name:"teamId"},{type:"int",name:"mute"}]},getTeamMemberInvitorAccid:{sid:i.team.id,cid:i.team.getTeamMemberInvitorAccid,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},friendRequest:{sid:i.friend.id,cid:i.friend.friendRequest,params:[{type:"String",name:"account"},{type:"byte",name:"type"},{type:"String",name:"ps"}]},deleteFriend:{sid:i.friend.id,cid:i.friend.deleteFriend,params:[{type:"String",name:"account"},{type:"Property",name:"delFriendParams"}]},updateFriend:{sid:i.friend.id,cid:i.friend.updateFriend,params:[{type:"Property",name:"friend"}]},getFriends:{sid:i.friend.id,cid:i.friend.getFriends,params:[{type:"long",name:"timetag"}]},getChatroomAddress:{sid:i.chatroom.id,cid:i.chatroom.getChatroomAddress,params:[{type:"long",name:"chatroomId"},{type:"bool",name:"isWeixinApp"},{type:"number",name:"type"}]},sendFilterMsg:{sid:i.filter.id,cid:i.filter.sendFilterMsg,params:[{type:"Property",name:"msg"}]},sendFilterCustomSysMsg:{sid:i.filter.id,cid:i.filter.sendFilterCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},publishEvent:{sid:i.eventService.id,cid:i.eventService.publishEvent,params:[{type:"Property",name:"msgEvent"}]},pushEvent:{sid:i.eventService.id,cid:i.eventService.pushEvent},subscribeEvent:{sid:i.eventService.id,cid:i.eventService.subscribeEvent,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},unSubscribeEventsByAccounts:{sid:i.eventService.id,cid:i.eventService.unSubscribeEventsByAccounts,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},unSubscribeEventsByType:{sid:i.eventService.id,cid:i.eventService.unSubscribeEventsByType,params:[{type:"Property",name:"msgEventSubscribe"}]},querySubscribeEventsByAccounts:{sid:i.eventService.id,cid:i.eventService.querySubscribeEventsByAccounts,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},querySubscribeEventsByType:{sid:i.eventService.id,cid:i.eventService.querySubscribeEventsByType,params:[{type:"Property",name:"msgEventSubscribe"}]},pushEvents:{sid:i.eventService.id,cid:i.eventService.pushEvents},getThreadMsgs:{sid:i.msgExtend.id,cid:i.msgExtend.getThreadMsgs,params:[{type:"Property",name:"msg"},{type:"Property",name:"threadMsgReq"}]},getMsgsByIdServer:{sid:i.msgExtend.id,cid:i.msgExtend.getMsgsByIdServer,params:[{type:"PropertyArray",name:"reqMsgs",entity:"msg"}]},addQuickComment:{sid:i.msgExtend.id,cid:i.msgExtend.addQuickComment,params:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},deleteQuickComment:{sid:i.msgExtend.id,cid:i.msgExtend.deleteQuickComment,params:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},getQuickComments:{sid:i.msgExtend.id,cid:i.msgExtend.getQuickComments,params:[{type:"PropertyArray",name:"commentReq",entity:"commentReq"}]},addCollect:{sid:i.msgExtend.id,cid:i.msgExtend.addCollect,params:[{type:"Property",name:"collect"}]},deleteCollects:{sid:i.msgExtend.id,cid:i.msgExtend.deleteCollects,params:[{type:"PropertyArray",name:"collectList",entity:"collect"}]},updateCollect:{sid:i.msgExtend.id,cid:i.msgExtend.updateCollect,params:[{type:"Property",name:"collect"}]},getCollects:{sid:i.msgExtend.id,cid:i.msgExtend.getCollects,params:[{type:"Property",name:"collectQuery"}]},addStickTopSession:{sid:i.msgExtend.id,cid:i.msgExtend.addStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},updateStickTopSession:{sid:i.msgExtend.id,cid:i.msgExtend.updateStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},deleteStickTopSession:{sid:i.msgExtend.id,cid:i.msgExtend.deleteStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},addMsgPin:{sid:i.msgExtend.id,cid:i.msgExtend.addMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},updateMsgPin:{sid:i.msgExtend.id,cid:i.msgExtend.updateMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},deleteMsgPin:{sid:i.msgExtend.id,cid:i.msgExtend.deleteMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},getMsgPins:{sid:i.msgExtend.id,cid:i.msgExtend.getMsgPins,params:[{type:"Property",name:"msgPinReq"}]}}),a=r.merge({},o.packetConfig,{"2_3":{service:"auth",cmd:"login",response:[{type:"Property",name:"loginRes"},{type:"PropertyArray",name:"loginPorts",entity:"loginPort"},{type:"Property",name:"aosPushInfo"}]},"2_5":{service:"auth",cmd:"kicked",response:[{type:"Number",name:"from"},{type:"Number",name:"reason"},{type:"String",name:"custom"},{type:"Number",name:"customClientType"}]},"2_6":{service:"auth",cmd:"logout"},"2_7":{service:"auth",cmd:"multiPortLogin",response:[{type:"Number",name:"state"},{type:"PropertyArray",name:"loginPorts",entity:"loginPort"}]},"2_8":{service:"auth",cmd:"kick",response:[{type:"StrArray",name:"deviceIds"}]},"3_1":{service:"user",cmd:"updatePushToken"},"3_2":{service:"user",cmd:"appBackground"},"3_3":{service:"user",cmd:"markInBlacklist"},"3_103":{service:"user",cmd:"syncMarkInBlacklist",response:[{type:"String",name:"account"},{type:"Boolean",name:"isAdd"}]},"3_4":{service:"user",cmd:"getBlacklist",response:[{type:"StrArray",name:"blacklist"}]},"3_5":{service:"user",cmd:"markInMutelist"},"3_105":{service:"user",cmd:"syncMarkInMutelist",response:[{type:"String",name:"account"},{type:"Boolean",name:"isAdd"}]},"3_6":{service:"user",cmd:"getMutelist",response:[{type:"StrArray",name:"mutelist"}]},"3_8":{service:"user",cmd:"getRelations",response:[{type:"PropertyArray",name:"specialRelations",entity:"specialRelation"},{type:"Number",name:"timetag"}]},"3_7":{service:"user",cmd:"getUsers",response:[{type:"PropertyArray",name:"users",entity:"user"}]},"3_10":{service:"user",cmd:"updateMyInfo",response:[{type:"Number",name:"timetag"}]},"3_15":{service:"user",cmd:"updateDonnop",response:[{type:"Number",name:"timetag"}]},"3_115":{service:"user",cmd:"syncUpdateDonnop",response:[{type:"Property",name:"donnop"},{type:"Number",name:"timetag"}]},"3_109":{service:"user",cmd:"syncMyInfo",response:[{type:"Property",name:"user"},{type:"Number",name:"timetag"}]},"3_110":{service:"user",cmd:"syncUpdateMyInfo",response:[{type:"Property",name:"user"}]},"4_1":{service:"notify"},"4_2":{service:"notify"},"4_3":{service:"notify",cmd:"markRead"},"4_4":{service:"notify",cmd:"syncOfflineMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_5":{service:"notify",cmd:"batchMarkRead"},"4_6":{service:"notify",cmd:"syncOfflineSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"4_8":{service:"notify",cmd:"syncOfflineNetcallMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_9":{service:"notify",cmd:"syncRoamingMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_12":{service:"notify",cmd:"syncMsgReceipts",response:[{type:"PropertyArray",name:"msgReceipts",entity:"msgReceipt"},{type:"Number",name:"timetag"}]},"4_13":{service:"notify",cmd:"syncDonnop",response:[{type:"Property",name:"donnop"},{type:"Number",name:"timetag"}]},"4_14":{service:"notify",cmd:"syncSessionAck",response:[{type:"StrLongMap",name:"p2p"},{type:"LongLongMap",name:"team"},{type:"Number",name:"timetag"}]},"4_15":{service:"notify",cmd:"syncRobots",response:[{type:"PropertyArray",name:"robots",entity:"robot"}]},"4_16":{service:"notify",cmd:"syncBroadcastMsgs",response:[{type:"PropertyArray",name:"broadcastMsgs",entity:"broadcastMsg"}]},"4_17":{service:"notify",cmd:"syncSuperTeamRoamingMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_18":{service:"notify",cmd:"syncOfflineSuperTeamSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"4_19":{service:"notify",cmd:"syncDeleteSuperTeamMsgOfflineRoaming",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"},{type:"Number",name:"timetag"},{type:"Number",name:"type"}]},"4_20":{service:"notify",cmd:"syncSuperTeamSessionAck",response:[{type:"LongLongMap",name:"superTeam"},{type:"Number",name:"timetag"}]},"4_21":{service:"notify",cmd:"syncDeleteMsgSelf",response:[{type:"PropertyArray",name:"deletedMsgs",entity:"deleteMsgSelfTag"}]},"4_22":{service:"notify",cmd:"syncSessionsWithMoreRoaming",response:[{type:"PropertyArray",name:"sessions",entity:"msg"}]},"4_23":{service:"notify",cmd:"syncStickTopSessions",response:[{type:"Number",name:"timetag"},{type:"boolean",name:"modify"},{type:"PropertyArray",name:"sessions",entity:"stickTopSession"}]},"4_24":{service:"notify",cmd:"syncSessionHistoryMsgsDelete",response:[{type:"PropertyArray",name:"sessionHistoryMsgsDeleteTags",entity:"clearMsgsParamsWithSync"}]},"4_100":{service:"notify",cmd:"syncOfflineFilterMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_101":{service:"notify",cmd:"syncOfflineFilterSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"5_1":{service:"sync",cmd:"syncDone",response:[{type:"Number",name:"timetag"}]},"5_2":{service:"sync",cmd:"syncTeamMembersDone",response:[{type:"Number",name:"timetag"}]},"5_3":{service:"sync",cmd:"syncSuperTeamMembersDone",response:[{type:"Number",name:"timetag"}]},"7_1":{service:"msg",cmd:"sendMsg",response:[{type:"Property",name:"msg"}],trivialErrorCodes:[7101]},"7_2":{service:"msg",cmd:"msg",response:[{type:"Property",name:"msg"}]},"7_3":{service:"msg",cmd:"sysMsg",response:[{type:"Property",name:"sysMsg"}]},"7_6":{service:"msg",cmd:"getHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_7":{service:"msg",cmd:"sendCustomSysMsg",trivialErrorCodes:[7101]},"7_8":{service:"msg",cmd:"searchHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_9":{service:"msg",cmd:"deleteSessions"},"7_10":{service:"msg",cmd:"getSessions",response:[{type:"StrArray",name:"sessions"}]},"7_101":{service:"msg",cmd:"syncSendMsg",response:[{type:"Property",name:"msg"}]},"7_11":{service:"msg",cmd:"sendMsgReceipt",response:[{type:"Property",name:"msgReceipt"}]},"7_12":{service:"msg",cmd:"msgReceipt",response:[{type:"Property",name:"msgReceipt"}]},"7_13":{service:"msg",cmd:"onDeleteMsg"},"7_14":{service:"msg",cmd:"onMsgDeleted",response:[{type:"Property",name:"sysMsg"}]},"7_15":{service:"msg",cmd:"onDeleteMsgOfflineRoaming",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"},{type:"Number",name:"timetag"},{type:"Number",name:"type"}]},"7_16":{service:"msg",cmd:"onMarkSessionAck"},"7_17":{service:"msg",cmd:"broadcastMsg",response:[{type:"Property",name:"broadcastMsg"}]},"7_18":{service:"msg",cmd:"clearServerHistoryMsgs",response:[{type:"Long",name:"timetag"}]},"7_19":{service:"session",cmd:"getServerSessions",response:[{type:"Property",name:"sessionReqTag"},{type:"PropertyArray",name:"sessionList",entity:"session"}]},"7_20":{service:"session",cmd:"getServerSession",response:[{type:"Property",name:"session"}]},"7_21":{service:"session",cmd:"updateServerSession"},"7_22":{service:"session",cmd:"deleteServerSessions"},"7_23":{service:"msg",cmd:"deleteMsgSelf",response:[{type:"Long",name:"timetag"}]},"7_24":{service:"msg",cmd:"deleteMsgSelfBatch",response:[{type:"Long",name:"timetag"}]},"7_118":{service:"msg",cmd:"onClearServerHistoryMsgs",response:[{type:"Property",name:"sessionHistoryMsgsDeleteTag",entity:"clearMsgsParamsWithSync"}]},"7_123":{service:"msg",cmd:"onDeleteMsgSelf",response:[{type:"Property",name:"deleteMsgSelfTag"}]},"7_124":{service:"msg",cmd:"onDeleteMsgSelfBatch",response:[{type:"PropertyArray",name:"deleteMsgSelfTags",entity:"deleteMsgSelfTag"}]},"7_116":{service:"msg",cmd:"syncMarkSessionAck",response:[{type:"Number",name:"scene"},{type:"String",name:"to"},{type:"Number",name:"timetag"}]},"7_121":{service:"msg",cmd:"syncUpdateServerSession",response:[{type:"Property",name:"session"}]},"23_1":{service:"msgExtend",cmd:"getThreadMsgs",response:[{type:"Property",name:"threadMsg",entity:"msg"},{type:"Property",name:"threadMsgsMeta"},{type:"PropertyArray",name:"msgs",entity:"msg"}]},"23_2":{service:"msgExtend",cmd:"getMsgsByIdServer",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"23_3":{service:"msgExtend",cmd:"addQuickComment",response:[{type:"Number",name:"timetag"}]},"23_4":{service:"msgExtend",cmd:"deleteQuickComment",response:[{type:"Number",name:"timetag"}]},"23_5":{service:"msgExtend",cmd:"onQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_6":{service:"msgExtend",cmd:"onDeleteQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_7":{service:"msgExtend",cmd:"getQuickComments",response:[{type:"PropertyArray",name:"commentRes",entity:"commentRes"}]},"23_8":{service:"msgExtend",cmd:"addCollect",response:[{type:"Property",name:"collect"}]},"23_9":{service:"msgExtend",cmd:"deleteCollects",response:[{type:"Number",name:"deleteNum"}]},"23_10":{service:"msgExtend",cmd:"updateCollect",response:[{type:"Property",name:"collect"}]},"23_11":{service:"msgExtend",cmd:"getCollects",response:[{type:"Number",name:"total"},{type:"PropertyArray",name:"collectList",entity:"collect"}]},"23_12":{service:"msgExtend",cmd:"addStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_13":{service:"msgExtend",cmd:"deleteStickTopSession",response:[{type:"Number",name:"timetag"}]},"23_14":{service:"msgExtend",cmd:"updateStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_15":{service:"msgExtend",cmd:"addMsgPin",response:[{type:"Number",name:"timetag"}]},"23_16":{service:"msgExtend",cmd:"updateMsgPin",response:[{type:"Number",name:"timetag"}]},"23_17":{service:"msgExtend",cmd:"deleteMsgPin",response:[{type:"Number",name:"timetag"}]},"23_18":{service:"msgExtend",cmd:"onAddMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_19":{service:"msgExtend",cmd:"onUpdateMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_20":{service:"msgExtend",cmd:"onDeleteMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_21":{service:"msgExtend",cmd:"getMsgPins",response:[{type:"Number",name:"timetag"},{type:"Boolean",name:"modify"},{type:"PropertyArray",name:"pins",entity:"msgPinRes"}]},"23_103":{service:"msgExtend",cmd:"syncAddQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_104":{service:"msgExtend",cmd:"syncDeleteQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_112":{service:"msgExtend",cmd:"syncAddStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_113":{service:"msgExtend",cmd:"syncDeleteStickTopSession",response:[{type:"Number",name:"timetag"},{type:"Property",name:"stickTopSession"}]},"23_114":{service:"msgExtend",cmd:"syncUpdateStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_115":{service:"msgExtend",cmd:"syncAddMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_116":{service:"msgExtend",cmd:"syncUpdateMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_117":{service:"msgExtend",cmd:"syncDeleteMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"21_2":{service:"superTeam",cmd:"sendSuperTeamMsg",response:[{type:"Property",name:"msg"}]},"21_3":{service:"superTeam",cmd:"superTeamMsg",response:[{type:"Property",name:"msg"}]},"21_5":{service:"superTeam",cmd:"addSuperTeamMembers",response:[{type:"StrArray",name:"abortedAccidList"},{type:"long",name:"timetag"}]},"21_6":{service:"superTeam",cmd:"removeSuperTeamMembers"},"21_7":{service:"superTeam",cmd:"leaveSuperTeam"},"21_8":{service:"superTeam",cmd:"updateSuperTeam",response:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},"21_9":{service:"superTeam",cmd:"getSuperTeam",response:[{type:"Property",name:"team"}]},"21_12":{service:"superTeam",cmd:"getSuperTeams",response:[{type:"PropertyArray",name:"teams",entity:"superTeam"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_10":{service:"superTeam",cmd:"updateInfoInSuperTeam"},"21_13":{service:"superTeam",cmd:"getSuperTeamMembers",response:[{type:"long",name:"timetag"}]},"21_11":{service:"superTeam",cmd:"getMySuperTeamMembers",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_14":{service:"superTeam",cmd:"getSuperTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"21_15":{service:"superTeam",cmd:"getSuperTeamMembersByJoinTime",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_16":{service:"superTeam",cmd:"sendSuperTeamCustomSysMsg",trivialErrorCodes:[7101]},"21_17":{service:"superTeam",cmd:"onDeleteSuperTeamMsg"},"21_18":{service:"superTeam",cmd:"onSuperTeamMsgDelete",response:[{type:"Property",name:"sysMsg"}]},"21_19":{service:"superTeam",cmd:"superTeamCustomSysMsg",response:[{type:"Property",name:"sysMsg"}]},"21_20":{service:"superTeam",cmd:"applySuperTeam",response:[{type:"Property",name:"team"}]},"21_21":{service:"superTeam",cmd:"passSuperTeamApply"},"21_22":{service:"superTeam",cmd:"rejectSuperTeamApply"},"21_23":{service:"superTeam",cmd:"acceptSuperTeamInvite",response:[{type:"Property",name:"team"}]},"21_24":{service:"superTeam",cmd:"rejectSuperTeamInvite"},"21_25":{service:"superTeam",cmd:"onMarkSuperTeamSessionAck"},"21_26":{service:"superTeam",cmd:"addSuperTeamManagers"},"21_27":{service:"superTeam",cmd:"removeSuperTeamManagers"},"21_28":{service:"superTeam",cmd:"updateSuperTeamMute"},"21_29":{service:"superTeam",cmd:"updateSuperTeamMembersMute",response:[{type:"long",name:"timetag"}]},"21_30":{service:"superTeam",cmd:"updateNickInSuperTeam"},"21_31":{service:"superTeam",cmd:"transferSuperTeam"},"21_113":{service:"superTeam",cmd:"syncSuperTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"superTeamMember"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_111":{service:"superTeam",cmd:"syncMySuperTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"superTeamMember"},{type:"long",name:"timetag"}]},"21_109":{service:"superTeam",cmd:"syncSuperTeams",response:[{type:"PropertyArray",name:"teams",entity:"superTeam"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_101":{service:"superTeam",cmd:"syncCreateSuperTeam",response:[{type:"Property",name:"team"}]},"21_102":{service:"superTeam",cmd:"syncSendSuperTeamMsg",response:[{type:"Property",name:"msg"}]},"21_110":{service:"superTeam",cmd:"syncUpdateSuperTeamMember",response:[{type:"Property",name:"teamMember",entity:"superTeamMember"}]},"21_117":{service:"superTeam",cmd:"syncDeleteSuperTeamMsg",response:[{type:"Property",name:"sysMsg"}]},"21_125":{service:"superTeam",cmd:"syncMarkSuperTeamSessionAck",response:[{type:"Long",name:"to"},{type:"Long",name:"timetag"}]},"8_1":{service:"team",cmd:"createTeam",response:[{type:"Property",name:"team"},{type:"StrArray",name:"abortedAccidList"}]},"8_2":{service:"team",cmd:"sendTeamMsg",response:[{type:"Property",name:"msg"}]},"8_3":{service:"team",cmd:"teamMsg",response:[{type:"Property",name:"msg"}]},"8_4":{service:"team",cmd:"teamMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_5":{service:"team",cmd:"addTeamMembers",response:[{type:"long",name:"time"},{type:"StrArray",name:"abortedAccidList"}]},"8_6":{service:"team",cmd:"removeTeamMembers"},"8_7":{service:"team",cmd:"updateTeam",response:[{type:"Number",name:"id"},{type:"Number",name:"time"}]},"8_8":{service:"team",cmd:"leaveTeam"},"8_9":{service:"team",cmd:"getTeam",response:[{type:"Property",name:"team"}]},"8_10":{service:"team",cmd:"getTeams",response:[{type:"PropertyArray",name:"teams",entity:"team"},{type:"Number",name:"timetag"}]},"8_11":{service:"team",cmd:"getTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_12":{service:"team",cmd:"dismissTeam"},"8_13":{service:"team",cmd:"applyTeam",response:[{type:"Property",name:"team"}]},"8_14":{service:"team",cmd:"passTeamApply"},"8_15":{service:"team",cmd:"rejectTeamApply"},"8_16":{service:"team",cmd:"addTeamManagers"},"8_17":{service:"team",cmd:"removeTeamManagers"},"8_18":{service:"team",cmd:"transferTeam"},"8_19":{service:"team",cmd:"updateInfoInTeam"},"8_20":{service:"team",cmd:"updateNickInTeam"},"8_21":{service:"team",cmd:"acceptTeamInvite",response:[{type:"Property",name:"team"}]},"8_22":{service:"team",cmd:"rejectTeamInvite"},"8_23":{service:"team",cmd:"getTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_24":{service:"team",cmd:"searchTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_25":{service:"team",cmd:"updateMuteStateInTeam"},"8_26":{service:"team",cmd:"getMyTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"teamMember"}]},"8_27":{service:"team",cmd:"getMutedTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"teamMembers",entity:"teamMember"}]},"8_28":{service:"team",cmd:"sendTeamMsgReceipt",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_29":{service:"team",cmd:"getTeamMsgReads",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_30":{service:"team",cmd:"getTeamMsgReadAccounts",response:[{type:"String",name:"idClient"},{type:"StrArray",name:"readAccounts"},{type:"StrArray",name:"unreadAccounts"}]},"8_31":{service:"team",cmd:"notifyTeamMsgReads",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_32":{service:"team",cmd:"muteTeamAll",response:[]},"8_33":{service:"team",cmd:"getTeamMemberInvitorAccid",response:[{type:"object",name:"accountsMap"}]},"8_126":{service:"team",cmd:"syncMyTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_109":{service:"team",cmd:"syncTeams",response:[{type:"Number",name:"timetag"},{type:"PropertyArray",name:"teams",entity:"team"}]},"8_111":{service:"team",cmd:"syncTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_101":{service:"team",cmd:"syncCreateTeam",response:[{type:"Property",name:"team"}]},"8_102":{service:"team",cmd:"syncSendTeamMsg",response:[{type:"Property",name:"msg"}]},"8_119":{service:"team",cmd:"syncUpdateTeamMember",response:[{type:"Property",name:"teamMember"}]},"12_1":{service:"friend",cmd:"friendRequest"},"12_101":{service:"friend",cmd:"syncFriendRequest",response:[{type:"String",name:"account"},{type:"Number",name:"type"},{type:"String",name:"ps"}]},"12_2":{service:"friend",cmd:"deleteFriend"},"12_102":{service:"friend",cmd:"syncDeleteFriend",response:[{type:"String",name:"account"}]},"12_3":{service:"friend",cmd:"updateFriend"},"12_103":{service:"friend",cmd:"syncUpdateFriend",response:[{type:"Property",name:"friend"}]},"12_4":{service:"friend",cmd:"getFriends",response:[{type:"PropertyArray",name:"friends",entity:"friend"},{type:"Number",name:"timetag"}]},"12_5":{service:"friend",cmd:"syncFriends",response:[{type:"PropertyArray",name:"friends",entity:"friend"},{type:"Number",name:"timetag"}]},"12_6":{service:"friend",cmd:"syncFriendUsers",response:[{type:"PropertyArray",name:"users",entity:"user"},{type:"Number",name:"timetag"}]},"13_1":{service:"chatroom",cmd:"getChatroomAddress",response:[{type:"StrArray",name:"address"}]},"14_1":{service:"eventService",cmd:"publishEvent",response:[{type:"Property",name:"msgEvent"}]},"14_2":{service:"eventService",cmd:"pushEvent",response:[{type:"Property",name:"msgEvent"}]},"14_3":{service:"eventService",cmd:"subscribeEvent",response:[{type:"StrArray",name:"accounts"}]},"14_4":{service:"eventService",cmd:"unSubscribeEventsByAccounts",response:[{type:"StrArray",name:"accounts"}]},"14_5":{service:"eventService",cmd:"unSubscribeEventsByType"},"14_6":{service:"eventService",cmd:"querySubscribeEventsByAccounts",response:[{type:"PropertyArray",name:"msgEventSubscribes",entity:"msgEventSubscribe"}]},"14_7":{service:"eventService",cmd:"querySubscribeEventsByType",response:[{type:"PropertyArray",name:"msgEventSubscribes",entity:"msgEventSubscribe"}]},"14_9":{service:"eventService",cmd:"pushEvents",response:[{type:"PropertyArray",name:"msgEvents",entity:"msgEvent"}]},"101_1":{service:"filter",cmd:"sendFilterMsg",response:[{type:"Property",name:"msg"}]},"101_2":{service:"filter",cmd:"filterMsg",response:[{type:"Property",name:"msg"}]},"101_3":{service:"filter",cmd:"filterSysMsg",response:[{type:"Property",name:"sysMsg"}]},"101_7":{service:"filter",cmd:"sendFilterCustomSysMsg"}});e.exports={idMap:i,cmdConfig:s,packetConfig:a}},,function(e,t){function n(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}e.exports=n,n.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(e){this.ms=e},n.prototype.setMax=function(e){this.max=e},n.prototype.setJitter=function(e){this.jitter=e}},,function(e,t,n){"use strict";var r={set:function(e,t,n){r[e]=t,n&&(n.support=t)}};e.exports=r},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var c,u=[],l=!1,m=-1;function p(){l&&c&&(l=!1,c.length?u=c.concat(u):m=-1,u.length&&d())}function d(){if(!l){var e=a(p);l=!0;for(var t=u.length;t;){for(c=u,u=[];++m<t;)c&&c[m].run();m=-1,t=u.length}c=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function g(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new f(e,t)),1!==u.length||l||a(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=g,o.addListener=g,o.once=g,o.off=g,o.removeListener=g,o.removeAllListeners=g,o.emit=g,o.prependListener=g,o.prependOnceListener=g,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,n){(function(t,n){
/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.4+314e4831
 */var r;r=function(){"use strict";function e(e){return"function"==typeof e}var r=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},o=0,i=void 0,s=void 0,a=function(e,t){f[o]=e,f[o+1]=t,2===(o+=2)&&(s?s(g):T())};var c="undefined"!=typeof window?window:void 0,u=c||{},l=u.MutationObserver||u.WebKitMutationObserver,m="undefined"==typeof self&&void 0!==t&&"[object process]"==={}.toString.call(t),p="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function d(){var e=setTimeout;return function(){return e(g,1)}}var f=new Array(1e3);function g(){for(var e=0;e<o;e+=2){(0,f[e])(f[e+1]),f[e]=void 0,f[e+1]=void 0}o=0}var y,h,v,b,T=void 0;function S(e,t){var n=this,r=new this.constructor(_);void 0===r[M]&&D(r);var o=n._state;if(o){var i=arguments[o-1];a(function(){return U(o,r,i,n._result)})}else F(n,r,e,t);return r}function k(e){if(e&&"object"==typeof e&&e.constructor===this)return e;var t=new this(_);return E(t,e),t}m?T=function(){return t.nextTick(g)}:l?(h=0,v=new l(g),b=document.createTextNode(""),v.observe(b,{characterData:!0}),T=function(){b.data=h=++h%2}):p?((y=new MessageChannel).port1.onmessage=g,T=function(){return y.port2.postMessage(0)}):T=void 0===c?function(){try{var e=Function("return this")().require("vertx");return void 0!==(i=e.runOnLoop||e.runOnContext)?function(){i(g)}:d()}catch(e){return d()}}():d();var M=Math.random().toString(36).substring(2);function _(){}var C=void 0,x=1,P=2,w={error:null};function O(e){try{return e.then}catch(e){return w.error=e,w}}function I(t,n,r){n.constructor===t.constructor&&r===S&&n.constructor.resolve===k?function(e,t){t._state===x?N(e,t._result):t._state===P?j(e,t._result):F(t,void 0,function(t){return E(e,t)},function(t){return j(e,t)})}(t,n):r===w?(j(t,w.error),w.error=null):void 0===r?N(t,n):e(r)?function(e,t,n){a(function(e){var r=!1,o=function(e,t,n,r){try{e.call(t,n,r)}catch(e){return e}}(n,t,function(n){r||(r=!0,t!==n?E(e,n):N(e,n))},function(t){r||(r=!0,j(e,t))},e._label);!r&&o&&(r=!0,j(e,o))},e)}(t,n,r):N(t,n)}function E(e,t){var n,r;e===t?j(e,new TypeError("You cannot resolve a promise with itself")):(r=typeof(n=t),null===n||"object"!==r&&"function"!==r?N(e,t):I(e,t,O(t)))}function A(e){e._onerror&&e._onerror(e._result),R(e)}function N(e,t){e._state===C&&(e._result=t,e._state=x,0!==e._subscribers.length&&a(R,e))}function j(e,t){e._state===C&&(e._state=P,e._result=t,a(A,e))}function F(e,t,n,r){var o=e._subscribers,i=o.length;e._onerror=null,o[i]=t,o[i+x]=n,o[i+P]=r,0===i&&e._state&&a(R,e)}function R(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var r=void 0,o=void 0,i=e._result,s=0;s<t.length;s+=3)r=t[s],o=t[s+n],r?U(n,r,o,i):o(i);e._subscribers.length=0}}function U(t,n,r,o){var i=e(r),s=void 0,a=void 0,c=void 0,u=void 0;if(i){if((s=function(e,t){try{return e(t)}catch(e){return w.error=e,w}}(r,o))===w?(u=!0,a=s.error,s.error=null):c=!0,n===s)return void j(n,new TypeError("A promises callback cannot return that same promise."))}else s=o,c=!0;n._state!==C||(i&&c?E(n,s):u?j(n,a):t===x?N(n,s):t===P&&j(n,s))}var L=0;function D(e){e[M]=L++,e._state=void 0,e._result=void 0,e._subscribers=[]}var q=function(){function e(e,t){this._instanceConstructor=e,this.promise=new e(_),this.promise[M]||D(this.promise),r(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?N(this.promise,this._result):(this.length=this.length||0,this._enumerate(t),0===this._remaining&&N(this.promise,this._result))):j(this.promise,new Error("Array Methods must be provided an Array"))}return e.prototype._enumerate=function(e){for(var t=0;this._state===C&&t<e.length;t++)this._eachEntry(e[t],t)},e.prototype._eachEntry=function(e,t){var n=this._instanceConstructor,r=n.resolve;if(r===k){var o=O(e);if(o===S&&e._state!==C)this._settledAt(e._state,t,e._result);else if("function"!=typeof o)this._remaining--,this._result[t]=e;else if(n===B){var i=new n(_);I(i,e,o),this._willSettleAt(i,t)}else this._willSettleAt(new n(function(t){return t(e)}),t)}else this._willSettleAt(r(e),t)},e.prototype._settledAt=function(e,t,n){var r=this.promise;r._state===C&&(this._remaining--,e===P?j(r,n):this._result[t]=n),0===this._remaining&&N(r,this._result)},e.prototype._willSettleAt=function(e,t){var n=this;F(e,void 0,function(e){return n._settledAt(x,t,e)},function(e){return n._settledAt(P,t,e)})},e}();var B=function(){function e(t){this[M]=L++,this._result=this._state=void 0,this._subscribers=[],_!==t&&("function"!=typeof t&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof e?function(e,t){try{t(function(t){E(e,t)},function(t){j(e,t)})}catch(t){j(e,t)}}(this,t):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(e){var t=this.constructor;return this.then(function(n){return t.resolve(e()).then(function(){return n})},function(n){return t.resolve(e()).then(function(){throw n})})},e}();return B.prototype.then=S,B.all=function(e){return new q(this,e).promise},B.race=function(e){var t=this;return r(e)?new t(function(n,r){for(var o=e.length,i=0;i<o;i++)t.resolve(e[i]).then(n,r)}):new t(function(e,t){return t(new TypeError("You must pass an array to race."))})},B.resolve=k,B.reject=function(e){var t=new this(_);return j(t,e),t},B._setScheduler=function(e){s=e},B._setAsap=function(e){a=e},B._asap=a,B.polyfill=function(){var e=void 0;if(void 0!==n)e=n;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var r=null;try{r=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===r&&!t.cast)return}e.Promise=B},B.Promise=B,B},e.exports=r()}).call(this,n(209),n(35))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(0),o=r.notundef,i=r.undef;function s(e){o(e.name)&&(this.name=""+e.name),o(e.announcement)&&(this.announcement=""+e.announcement),o(e.broadcastUrl)&&(this.broadcastUrl=""+e.broadcastUrl),o(e.custom)&&(this.custom=""+e.custom),o(e.queuelevel)&&(this.queuelevel=parseInt(e.queuelevel))}s.reverse=function(e){var t=r.copy(e);return i(t.announcement)&&(t.announcement=""),i(t.broadcastUrl)&&(t.broadcastUrl=""),i(t.custom)&&(t.custom=""),o(t.createTime)&&(t.createTime=+t.createTime),o(t.updateTime)&&(t.updateTime=+t.updateTime),o(t.onlineMemberNum)&&(t.onlineMemberNum=+t.onlineMemberNum),o(t.mute)&&(t.mute="1"===t.mute),t},e.exports=s},function(e,t,n){"use strict";var r=n(39);e.exports=function(e){var t,n,o,i;e.db&&(r.db=e.db),e.rnfs&&(r.rnfs=e.rnfs,r.rnfs.size||(r.rnfs.size=1048576),r.rnfs.nimPromise=(t=r.rnfs,n=t.size/2-256,i=0,o=r.rnfs.DocumentDirectoryPath+"/nimlog_"+i+".log",t.exists(o).then(function(e){return e?t.stat(o):Promise.reject(0)}).then(function(e){return e&&e.size>n?Promise.reject(1):Promise.reject(0)}).catch(function(e){return"number"==typeof e?t.nimIndex=e:console.error("initRnfs::ERROR",e),Promise.resolve()})))}},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};function s(e){var t=this,n=e.url||null;t.level={debug:0,log:1,info:2,warn:3,error:4}[e.level]||0,t.logCache=[],t.logNum=1,t.timeInterval=5e3,window.onerror=function(e,n,r,o,i){t.error(i)},setInterval(function(){t.logCache.length>0&&n&&t.postLogs(n,t.logCache)},t.timeInterval)}s.prototype.debug=function(){this.level>0||(console.debug.apply(this,arguments),this.cacheLogs.apply(this,["[degbug]"].concat(arguments)))},s.prototype.log=function(){this.level>1||(console.log.apply(this,arguments),this.cacheLogs.apply(this,["[log]"].concat(arguments)))},s.prototype.info=function(){this.level>2||(console.info.apply(this,arguments),this.cacheLogs.apply(this,["[info]"].concat(arguments)))},s.prototype.warn=function(){this.level>3||(console.warn.apply(this,arguments),this.cacheLogs.apply(this,["[warn]"].concat(arguments)))},s.prototype.error=function(){this.level>4||(console.error.apply(this,arguments),this.cacheLogs.apply(this,["[error]"].concat(arguments)))},s.prototype.cacheLogs=function(e,t){for(var n=[],r=0;r<t.length;r++){var o=t[r];"object"===(void 0===o?"undefined":(0,i.default)(o))?n.push(JSON.stringify(o)):n.push(o)}var s=this.logNum+++" "+e+" "+n.join("; ");this.logCache.push(s.replace("%c",""))},s.prototype.postLogs=function(e,t){var n=this,r=new XMLHttpRequest;r.onreadystatechange=function(){4===r.readyState&&(200===r.status?(console.info("LoggerPlugin::日志上报完成"),n.logCache=[],n.timeInterval=5e3):n.timeInterval+=5e3)},r.open("POST",e),r.setRequestHeader("Content-Type","plain/text;charset=utf-8"),r.timeout=360,r.send(t.join("\n"))},e.exports=s},function(e,t){e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var o in r)n.call(r,o)&&(e[o]=r[o])}return e};var n=Object.prototype.hasOwnProperty},function(e,t,n){var r=n(180);e.exports=function(e,t,n){if(!r(t))throw new TypeError("iterator must be a function");arguments.length<3&&(n=this);"[object Array]"===o.call(e)?function(e,t,n){for(var r=0,o=e.length;r<o;r++)i.call(e,r)&&t.call(n,e[r],r,e)}(e,t,n):"string"==typeof e?function(e,t,n){for(var r=0,o=e.length;r<o;r++)t.call(n,e.charAt(r),r,e)}(e,t,n):function(e,t,n){for(var r in e)i.call(e,r)&&t.call(n,e[r],r,e)}(e,t,n)};var o=Object.prototype.toString,i=Object.prototype.hasOwnProperty},function(e,t){(t=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},t.right=function(e){return e.replace(/\s*$/,"")}},function(e,t,n){var r=n(304),o=n(303);e.exports=function(e){if(!e)return{};var t={};return o(r(e).split("\n"),function(e){var n,o=e.indexOf(":"),i=r(e.slice(0,o)).toLowerCase(),s=r(e.slice(o+1));void 0===t[i]?t[i]=s:(n=t[i],"[object Array]"===Object.prototype.toString.call(n)?t[i].push(s):t[i]=[t[i],s])}),t}},function(e,t,n){(function(t){var n;n="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},e.exports=n}).call(this,n(35))},function(e,t,n){"use strict";var r=n(306),o=n(180),i=n(305),s=n(302);function a(e,t,n){var r=e;return o(t)?(n=t,"string"==typeof e&&(r={uri:e})):r=s(t,{uri:e}),r.callback=n,r}function c(e,t,n){return u(t=a(e,t,n))}function u(e){if(void 0===e.callback)throw new Error("callback argument missing");var t=!1,n=function(n,r,o){t||(t=!0,e.callback(n,r,o))};function r(e){return clearTimeout(l),e instanceof Error||(e=new Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,n(e,h)}function o(){if(!a){var t;clearTimeout(l),t=e.useXDR&&void 0===u.status?200:1223===u.status?204:u.status;var r=h,o=null;return 0!==t?(r={body:function(){var e=void 0;if(e=u.response?u.response:u.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(u),y)try{e=JSON.parse(e)}catch(e){}return e}(),statusCode:t,method:p,headers:{},url:m,rawRequest:u},u.getAllResponseHeaders&&(r.headers=i(u.getAllResponseHeaders()))):o=new Error("Internal XMLHttpRequest Error"),n(o,r,r.body)}}var s,a,u=e.xhr||null;u||(u=e.cors||e.useXDR?new c.XDomainRequest:new c.XMLHttpRequest);var l,m=u.url=e.uri||e.url,p=u.method=e.method||"GET",d=e.body||e.data,f=u.headers=e.headers||{},g=!!e.sync,y=!1,h={body:void 0,headers:{},statusCode:0,method:p,url:m,rawRequest:u};if("json"in e&&!1!==e.json&&(y=!0,f.accept||f.Accept||(f.Accept="application/json"),"GET"!==p&&"HEAD"!==p&&(f["content-type"]||f["Content-Type"]||(f["Content-Type"]="application/json"),d=JSON.stringify(!0===e.json?d:e.json))),u.onreadystatechange=function(){4===u.readyState&&setTimeout(o,0)},u.onload=o,u.onerror=r,u.onprogress=function(){},u.onabort=function(){a=!0},u.ontimeout=r,u.open(p,m,!g,e.username,e.password),g||(u.withCredentials=!!e.withCredentials),!g&&e.timeout>0&&(l=setTimeout(function(){if(!a){a=!0,u.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",r(e)}},e.timeout)),u.setRequestHeader)for(s in f)f.hasOwnProperty(s)&&u.setRequestHeader(s,f[s]);else if(e.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(e.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in e&&(u.responseType=e.responseType),"beforeSend"in e&&"function"==typeof e.beforeSend&&e.beforeSend(u),u.send(d||null),u}e.exports=c,c.XMLHttpRequest=r.XMLHttpRequest||function(){},c.XDomainRequest="withCredentials"in new c.XMLHttpRequest?c.XMLHttpRequest:r.XDomainRequest,function(e,t){for(var n=0;n<e.length;n++)t(e[n])}(["get","put","post","patch","head","delete"],function(e){c["delete"===e?"del":e]=function(t,n,r){return(n=a(t,n,r)).method=e.toUpperCase(),u(n)}})},,,,function(e,t,n){"use strict";e.exports={imLogin:{3:"clientType",4:"os",6:"sdkVersion",8:"appLogin",9:"protocolVersion",10:"pushTokenName",11:"pushToken",13:"deviceId",18:"appKey",19:"account",24:"browser",26:"session",32:"deviceInfo",38:"customTag",112:"isReactNative",1000:"token"},nosToken:{1:"objectName",2:"token",3:"bucket",4:"expireTime",7:"expireSec",8:"tag",9:"shortUrl"},audioToText:{2:"url"},imageOp:{0:"type",1:"stripmeta",2:"typeType",3:"blurRadius",4:"blurSigma",5:"qualityQuality",6:"cropX",7:"cropY",8:"cropWidth",9:"cropHeight",10:"rotateAngle",11:"pixelPixel",12:"thumbnailMode",13:"thumbnailWidth",14:"thumbnailHeight",15:"thumbnailAxisX",16:"thumbnailAxisY",17:"thumbnailCenterX",18:"thumbnailCenterY",19:"thumbnailEnlarge",20:"thumbnailToStatic",21:"watermarkType",22:"watermarkGravity",23:"watermarkDissolve",24:"watermarkDx",25:"watermarkDy",26:"watermarkImage",27:"watermarkText",28:"watermarkFont",29:"watermarkFontSize",30:"watermarkFontColor",31:"interlace"},robot:{4:"account",5:"nick",6:"avatar",7:"intro",8:"config",9:"valid",10:"createTime",11:"updateTime",12:"custid",13:"botid",14:"bindTime",_6_safe:"_avatar_safe"},clientAntispam:{1:"version",2:"md5",3:"nosurl",4:"thesaurus"},fileQuickTransfer:{1:"md5",2:"url",3:"size",4:"threshold",_2_safe:"_url_safe"},transToken:{1:"name",2:"type",3:"transType",4:"size",5:"extra",6:"body"},transInfo:{1:"docId",2:"name",3:"prefix",4:"size",5:"type",6:"state",7:"transType",8:"transSize",9:"pageCount",10:"picInfo",11:"extra",12:"flag"},nosFileUrlTag:{0:"safeUrl",1:"originUrl"},nosAccessTokenTag:{0:"token",1:"url",2:"userAgent",3:"ext"},fileListParam:{1:"fromDocId",2:"limit"},avSignalTag:{1:"type",2:"channelName",3:"channelId",4:"channelCreateTime",5:"channelExpireTime",6:"creator",7:"ext",8:"channelInValid",10:"from",11:"to",12:"requestId",13:"needPush",14:"pushTitle",15:"pushContent",16:"pushPayload",17:"needBadge",18:"members",19:"attach",20:"attachExt",21:"isSave",22:"msgid",23:"uid",24:"time"},login:{1:"appKey",2:"account",3:"deviceId",5:"chatroomId",8:"appLogin",20:"chatroomNick",21:"chatroomAvatar",22:"chatroomCustom",23:"chatroomEnterCustom",26:"session",38:"isAnonymous",_21_safe:"_chatroomAvatar_safe"},chatroom:{1:"id",3:"name",4:"announcement",5:"broadcastUrl",12:"custom",14:"createTime",15:"updateTime",16:"queuelevel",100:"creator",101:"onlineMemberNum",102:"mute"},msg:{1:"idClient",2:"type",3:"attach",4:"custom",5:"resend",6:"userUpdateTime",7:"fromNick",8:"fromAvatar",9:"fromCustom",10:"yidunEnable",11:"antiSpamContent",12:"skipHistory",13:"body",14:"antiSpamBusinessId",15:"clientAntiSpam",16:"antiSpamUsingYidun",20:"time",21:"from",22:"chatroomId",23:"fromClientType",25:"highPriority",27:"callbackExt",28:"subType",29:"yidunAntiCheating",30:"env",_8_safe:"_fromAvatar_safe"},chatroomMember:{1:"chatroomId",2:"account",3:"type",4:"level",5:"nick",6:"avatar",7:"custom",8:"online",9:"guest",10:"enterTime",12:"blacked",13:"gaged",14:"valid",15:"updateTime",16:"tempMuted",17:"tempMuteDuration",_6_safe:"_avatar_safe"}}},function(e,t,n){"use strict";e.exports={imLogin:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appKey:18,account:19,browser:24,session:26,deviceInfo:32,isReactNative:112,token:1e3,customTag:38},nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},audioToText:{url:2},imageOp:{type:0,stripmeta:1,typeType:2,blurRadius:3,blurSigma:4,qualityQuality:5,cropX:6,cropY:7,cropWidth:8,cropHeight:9,rotateAngle:10,pixelPixel:11,thumbnailMode:12,thumbnailWidth:13,thumbnailHeight:14,thumbnailAxisX:15,thumbnailAxisY:16,thumbnailCenterX:17,thumbnailCenterY:18,thumbnailEnlarge:19,thumbnailToStatic:20,watermarkType:21,watermarkGravity:22,watermarkDissolve:23,watermarkDx:24,watermarkDy:25,watermarkImage:26,watermarkText:27,watermarkFont:28,watermarkFontSize:29,watermarkFontColor:30,interlace:31},robot:{account:4,nick:5,avatar:6,intro:7,config:8,valid:9,createTime:10,updateTime:11,custid:12,botid:13,bindTime:14},clientAntispam:{version:1,md5:2,nosurl:3,thesaurus:4},fileQuickTransfer:{md5:1,url:2,size:3,threshold:4},transToken:{name:1,type:2,transType:3,size:4,extra:5,body:6},transInfo:{docId:1,name:2,prefix:3,size:4,type:5,state:6,transType:7,transSize:8,pageCount:9,picInfo:10,extra:11,flag:12},nosFileUrlTag:{safeUrl:0,originUrl:1},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3},fileListParam:{fromDocId:1,limit:2},avSignalTag:{type:1,channelName:2,channelId:3,channelCreateTime:4,channelExpireTime:5,creator:6,ext:7,channelInValid:8,from:10,to:11,requestId:12,needPush:13,pushTitle:14,pushContent:15,pushPayload:16,needBadge:17,members:18,attach:19,attachExt:20,isSave:21,msgid:22,uid:23,time:24},login:{appKey:1,account:2,deviceId:3,chatroomId:5,appLogin:8,chatroomNick:20,chatroomAvatar:21,chatroomCustom:22,chatroomEnterCustom:23,session:26,isAnonymous:38},chatroom:{id:1,name:3,announcement:4,broadcastUrl:5,custom:12,createTime:14,updateTime:15,queuelevel:16,creator:100,onlineMemberNum:101,mute:102},msg:{idClient:1,type:2,attach:3,custom:4,resend:5,userUpdateTime:6,fromNick:7,fromAvatar:8,fromCustom:9,yidunEnable:10,antiSpamContent:11,skipHistory:12,body:13,antiSpamBusinessId:14,clientAntiSpam:15,antiSpamUsingYidun:16,time:20,from:21,chatroomId:22,fromClientType:23,highPriority:25,callbackExt:27,subType:28,yidunAntiCheating:29,env:30},chatroomMember:{chatroomId:1,account:2,type:3,level:4,nick:5,avatar:6,custom:7,online:8,guest:9,enterTime:10,blacked:12,gaged:13,valid:14,updateTime:15,tempMuted:16,tempMuteDuration:17}}},function(e,t,n){"use strict";var r=n(0),o=n(86),i=r.merge({},o.idMap,{chatroom:{id:13,login:2,kicked:3,logout:4,sendMsg:6,msg:7,getChatroomMembers:8,getHistoryMsgs:9,markChatroomMember:11,closeChatroom:12,getChatroom:13,updateChatroom:14,updateMyChatroomMemberInfo:15,getChatroomMembersInfo:16,kickChatroomMember:17,updateChatroomMemberTempMute:19,queueOffer:20,queuePoll:21,queueList:22,peak:23,queueDrop:24,queueInit:25,queueChange:26},user:{id:3,syncRobot:16}}),s=r.merge({},o.cmdConfig,{login:{sid:i.chatroom.id,cid:i.chatroom.login,params:[{type:"byte",name:"type"},{type:"Property",name:"login"},{type:"Property",name:"imLogin"}]},logout:{sid:i.chatroom.id,cid:i.chatroom.logout},sendMsg:{sid:i.chatroom.id,cid:i.chatroom.sendMsg,params:[{type:"Property",name:"msg"}]},getChatroomMembers:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMembers,params:[{type:"byte",name:"type"},{type:"long",name:"time"},{type:"int",name:"limit"}]},getHistoryMsgs:{sid:i.chatroom.id,cid:i.chatroom.getHistoryMsgs,params:[{type:"long",name:"timetag"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},markChatroomMember:{sid:i.chatroom.id,cid:i.chatroom.markChatroomMember,params:[{type:"string",name:"account"},{type:"int",name:"type"},{type:"bool",name:"isAdd"},{type:"int",name:"level"},{type:"string",name:"custom"}]},closeChatroom:{sid:i.chatroom.id,cid:i.chatroom.closeChatroom,params:[{type:"string",name:"custom"}]},getChatroom:{sid:i.chatroom.id,cid:i.chatroom.getChatroom},updateChatroom:{sid:i.chatroom.id,cid:i.chatroom.updateChatroom,params:[{type:"Property",name:"chatroom"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"}]},updateMyChatroomMemberInfo:{sid:i.chatroom.id,cid:i.chatroom.updateMyChatroomMemberInfo,params:[{type:"Property",name:"chatroomMember"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"},{type:"bool",name:"needSave"}]},getChatroomMembersInfo:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMembersInfo,params:[{type:"StrArray",name:"accounts"}]},kickChatroomMember:{sid:i.chatroom.id,cid:i.chatroom.kickChatroomMember,params:[{type:"string",name:"account"},{type:"string",name:"custom"}]},updateChatroomMemberTempMute:{sid:i.chatroom.id,cid:i.chatroom.updateChatroomMemberTempMute,params:[{type:"String",name:"account"},{type:"long",name:"duration"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"}]},queueOffer:{sid:i.chatroom.id,cid:i.chatroom.queueOffer,params:[{type:"string",name:"elementKey"},{type:"string",name:"elementValue"},{type:"bool",name:"transient"}]},queuePoll:{sid:i.chatroom.id,cid:i.chatroom.queuePoll,params:[{type:"string",name:"elementKey"}]},queueList:{sid:i.chatroom.id,cid:i.chatroom.queueList},peak:{sid:i.chatroom.id,cid:i.chatroom.peak},queueDrop:{sid:i.chatroom.id,cid:i.chatroom.queueDrop},queueInit:{sid:i.chatroom.id,cid:i.chatroom.queueInit,params:[{type:"int",name:"limit"}]},queueChange:{sid:i.chatroom.id,cid:i.chatroom.queueChange,params:[{type:"StrStrMap",name:"elementMap"},{type:"bool",name:"needNotify"},{type:"string",name:"notifyExt"}]},syncRobot:{sid:i.user.id,cid:i.user.syncRobot,params:[{type:"long",name:"timetag"}]}}),a=r.merge({},o.packetConfig,{"4_10":{service:"notify"},"4_11":{service:"notify"},"3_16":{service:"chatroom",cmd:"syncRobot",response:[{type:"PropertyArray",name:"robots",entity:"robot"}]},"13_2":{service:"chatroom",cmd:"login",response:[{type:"Property",name:"chatroom"},{type:"Property",name:"chatroomMember"}]},"13_3":{service:"chatroom",cmd:"kicked",response:[{type:"Number",name:"reason"},{type:"String",name:"custom"}]},"13_4":{service:"chatroom",cmd:"logout"},"13_6":{service:"chatroom",cmd:"sendMsg",response:[{type:"Property",name:"msg"}]},"13_7":{service:"chatroom",cmd:"msg",response:[{type:"Property",name:"msg"}]},"13_8":{service:"chatroom",cmd:"getChatroomMembers",response:[{type:"PropertyArray",name:"members",entity:"chatroomMember"}]},"13_9":{service:"chatroom",cmd:"getHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"13_11":{service:"chatroom",cmd:"markChatroomMember",response:[{type:"Property",name:"chatroomMember"}]},"13_12":{service:"chatroom",cmd:"closeChatroom"},"13_13":{service:"chatroom",cmd:"getChatroom",response:[{type:"Property",name:"chatroom"}]},"13_14":{service:"chatroom",cmd:"updateChatroom"},"13_15":{service:"chatroom",cmd:"updateMyChatroomMemberInfo"},"13_16":{service:"chatroom",cmd:"getChatroomMembersInfo",response:[{type:"PropertyArray",name:"members",entity:"chatroomMember"}]},"13_17":{service:"chatroom",cmd:"kickChatroomMember"},"13_19":{service:"chatroom",cmd:"updateChatroomMemberTempMute"},"13_20":{service:"chatroom",cmd:"queueOffer"},"13_21":{service:"chatroom",cmd:"queuePoll",response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},"13_22":{service:"chatroom",cmd:"queueList",response:[{type:"KVArray",name:"queueList"}]},"13_23":{service:"chatroom",cmd:"peak",response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},"13_24":{service:"chatroom",cmd:"queueDrop"},"13_25":{service:"chatroom",cmd:"queueInit"},"13_26":{service:"chatroom",cmd:"queueChange",response:[{type:"StrArray",name:"elementKeyArray"}]}});e.exports={idMap:i,cmdConfig:s,packetConfig:a}},function(e,t,n){"use strict";e.exports={negotiateTransportTag:{"-1":"version",1:"serializeList",101:"serialize"},initTransportTag:{},nosToken:{1:"objectName",2:"token",3:"bucket",4:"expireTime",7:"expireSec",8:"tag",9:"shortUrl"},audioToText:{2:"url"},imageOp:{0:"type",1:"stripmeta",2:"typeType",3:"blurRadius",4:"blurSigma",5:"qualityQuality",6:"cropX",7:"cropY",8:"cropWidth",9:"cropHeight",10:"rotateAngle",11:"pixelPixel",12:"thumbnailMode",13:"thumbnailWidth",14:"thumbnailHeight",15:"thumbnailAxisX",16:"thumbnailAxisY",17:"thumbnailCenterX",18:"thumbnailCenterY",19:"thumbnailEnlarge",20:"thumbnailToStatic",21:"watermarkType",22:"watermarkGravity",23:"watermarkDissolve",24:"watermarkDx",25:"watermarkDy",26:"watermarkImage",27:"watermarkText",28:"watermarkFont",29:"watermarkFontSize",30:"watermarkFontColor",31:"interlace"},robot:{4:"account",5:"nick",6:"avatar",7:"intro",8:"config",9:"valid",10:"createTime",11:"updateTime",12:"custid",13:"botid",14:"bindTime",_6_safe:"_avatar_safe"},clientAntispam:{1:"version",2:"md5",3:"nosurl",4:"thesaurus"},fileQuickTransfer:{1:"md5",2:"url",3:"size",4:"threshold",_2_safe:"_url_safe"},transToken:{1:"name",2:"type",3:"transType",4:"size",5:"extra",6:"body"},transInfo:{1:"docId",2:"name",3:"prefix",4:"size",5:"type",6:"state",7:"transType",8:"transSize",9:"pageCount",10:"picInfo",11:"extra",12:"flag"},nosFileUrlTag:{0:"safeUrl",1:"originUrl"},nosAccessTokenTag:{0:"token",1:"url",2:"userAgent",3:"ext"},fileListParam:{1:"fromDocId",2:"limit"},avSignalTag:{1:"type",2:"channelName",3:"channelId",4:"channelCreateTime",5:"channelExpireTime",6:"creator",7:"ext",8:"channelInValid",10:"from",11:"to",12:"requestId",13:"needPush",14:"pushTitle",15:"pushContent",16:"pushPayload",17:"needBadge",18:"members",19:"attach",20:"attachExt",21:"isSave",22:"msgid",23:"uid",24:"time"},login:{3:"clientType",4:"os",6:"sdkVersion",8:"appLogin",9:"protocolVersion",10:"pushTokenName",11:"pushToken",13:"deviceId",18:"appKey",19:"account",24:"browser",26:"session",32:"deviceInfo",38:"customTag",39:"customClientType",112:"isReactNative",1000:"token"},loginRes:{17:"lastLoginDeviceId",38:"customTag",102:"connectionId",103:"ip",104:"port",106:"country",111:"hasXMPush"},loginPort:{3:"type",4:"os",5:"mac",13:"deviceId",19:"account",32:"deviceInfo",38:"customTag",102:"connectionId",103:"ip",109:"time"},aosPushInfo:{110:"pushType",111:"hasTokenPreviously"},sync:{1:"myInfo",2:"offlineMsgs",3:"teams",6:"netcallMsgs",7:"roamingMsgs",9:"relations",11:"friends",12:"sessions",13:"friendUsers",14:"msgReceipts",15:"myTeamMembers",16:"donnop",17:"deleteMsg",18:"sessionAck",19:"robots",20:"broadcastMsgs",21:"avSignal",22:"superTeams",23:"myInfoInSuperTeams",24:"superTeamRoamingMsgs",25:"deleteSuperTeamMsg",26:"superTeamSessionAck",27:"deleteMsgSelf",28:"stickTopSessions",29:"sessionHistoryMsgsDelete",100:"filterMsgs"},donnop:{1:"open"},sessionReqTag:{1:"minTimestamp",2:"maxTimestamp",3:"needLastMsg",4:"limit",5:"hasMore"},session:{1:"id",2:"updateTime",3:"ext",4:"lastMsg",5:"lastMsgType"},superTeam:{1:"teamId",3:"name",4:"type",5:"owner",6:"level",7:"selfCustom",8:"valid",9:"memberNum",10:"memberUpdateTime",11:"createTime",12:"updateTime",13:"validToCurrentUser",14:"intro",15:"announcement",16:"joinMode",17:"bits",18:"custom",19:"serverCustom",20:"avatar",21:"beInviteMode",22:"inviteMode",23:"updateTeamMode",24:"updateCustomMode",100:"mute",101:"muteType",_20_safe:"_avatar_safe"},superTeamMember:{1:"teamId",3:"account",4:"type",5:"nickInTeam",7:"bits",8:"active",9:"valid",10:"createTime",11:"updateTime",12:"custom",13:"mute",14:"invitoraccid",15:"joinTime"},team:{1:"teamId",3:"name",4:"type",5:"owner",6:"level",7:"selfCustom",8:"valid",9:"memberNum",10:"memberUpdateTime",11:"createTime",12:"updateTime",13:"validToCurrentUser",14:"intro",15:"announcement",16:"joinMode",17:"bits",18:"custom",19:"serverCustom",20:"avatar",21:"beInviteMode",22:"inviteMode",23:"updateTeamMode",24:"updateCustomMode",100:"mute",101:"muteType",_20_safe:"_avatar_safe"},teamMember:{1:"teamId",3:"account",4:"type",5:"nickInTeam",7:"bits",8:"active",9:"valid",10:"joinTime",11:"updateTime",12:"custom",13:"mute",14:"invitorAccid"},msg:{0:"scene",1:"to",2:"from",4:"fromClientType",5:"fromDeviceId",6:"fromNick",7:"time",8:"type",9:"body",10:"attach",11:"idClient",12:"idServer",13:"resend",14:"userUpdateTime",15:"custom",16:"pushPayload",17:"pushContent",18:"apnsAccounts",19:"apnsContent",20:"apnsForcePush",21:"yidunEnable",22:"antiSpamContent",23:"antiSpamBusinessId",24:"clientAntiSpam",25:"antiSpamUsingYidun",26:"needMsgReceipt",28:"needUpdateSession",29:"replyMsgFromAccount",30:"replyMsgToAccount",31:"replyMsgTime",32:"replyMsgIdServer",33:"replyMsgIdClient",34:"threadMsgFromAccount",35:"threadMsgToAccount",36:"threadMsgTime",37:"threadMsgIdServer",38:"threadMsgIdClient",39:"delete",40:"callbackExt",41:"subType",42:"yidunAntiCheating",43:"env",100:"isHistoryable",101:"isRoamingable",102:"isSyncable",104:"isMuted",105:"cc",106:"isInBlackList",107:"isPushable",108:"isOfflinable",109:"isUnreadable",110:"needPushNick",111:"isReplyMsg",112:"tempTeamMemberCount"},threadMsgReq:{1:"beginTime",2:"endTime",3:"lastMsgId",4:"limit",5:"reverse"},threadMsgsMeta:{1:"total",2:"lastMsgTime"},comment:{1:"from",2:"body",3:"time",4:"custom",5:"needPush",6:"needBadge",7:"pushTitle",8:"apnsText",9:"pushPayload"},commentReq:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",100:"timestamp"},commentRes:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",7:"detail",8:"modify",100:"timestamp"},collect:{1:"id",2:"type",3:"data",4:"custom",5:"uniqueId",6:"createTime",7:"updateTime"},collectQuery:{1:"beginTime",2:"endTime",3:"lastMsgId",4:"limit",5:"reverse",6:"type"},stickTopSession:{1:"id",2:"topCustom",3:"createTime",4:"updateTime"},pinTag:{1:"pinFrom",2:"pinCustom",3:"createTime",4:"updateTime"},msgPinReq:{1:"sessionId",2:"timetag"},msgPinRes:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",7:"pinFrom",8:"pinCustom"},msgReceipt:{1:"to",2:"from",7:"time",11:"idClient"},teamMsgReceipt:{0:"teamId",1:"idServer",100:"read",101:"unread",102:"idClient",103:"account"},deleteMsgSelfTag:{1:"scene",2:"from",3:"to",4:"idServer",5:"idClient",6:"time",7:"deletedTime",8:"custom"},sysMsg:{0:"time",1:"type",2:"to",3:"from",4:"ps",5:"attach",6:"idServer",7:"sendToOnlineUsersOnly",8:"apnsText",9:"pushPayload",10:"deletedIdClient",11:"deletedIdServer",12:"yidunEnable",13:"antiSpamContent",14:"deletedMsgTime",15:"deletedMsgFromNick",16:"opeAccount",21:"env",105:"cc",107:"isPushable",109:"isUnreadable",110:"needPushNick"},broadcastMsg:{1:"broadcastId",2:"fromAccid",3:"fromUid",4:"timestamp",5:"body"},friend:{4:"account",5:"flag",6:"beflag",7:"source",8:"alias",9:"bits",10:"custom",11:"createTime",12:"updateTime",13:"serverex"},user:{1:"account",3:"nick",4:"avatar",5:"sign",6:"gender",7:"email",8:"birth",9:"tel",10:"custom",12:"createTime",13:"updateTime",_4_safe:"_avatar_safe"},specialRelation:{0:"account",1:"isMuted",2:"isBlacked",3:"createTime",4:"updateTime"},msgType:{0:"text",1:"picture",2:"audio",3:"video",4:"location",5:"notification",6:"file",7:"netcall_audio",8:"netcall_vedio",9:"datatunnel_new",10:"tips",11:"robot",100:"custom"},msgEvent:{1:"type",2:"value",3:"idClient",4:"custom",5:"validTime",6:"broadcastType",7:"sync",8:"validTimeType",9:"durable",10:"time",11:"idServer",12:"clientType",13:"serverConfig",14:"serverCustom",101:"appid",103:"account",104:"enableMultiClient",106:"consid"},msgEventSubscribe:{1:"type",2:"subscribeTime",3:"sync",102:"to",104:"from",105:"time"},clearMsgsParams:{1:"account",2:"delRoam"},clearMsgsParamsWithSync:{0:"type",1:"otherAccid",2:"delRoam",3:"toTid",4:"isSyncSelf",5:"fromAccid",6:"time",7:"ext"},delFriendParams:{1:"delAlias"}}},function(e,t,n){"use strict";e.exports={negotiateTransportTag:{version:-1,serializeList:1,serialize:101},initTransportTag:{},nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},audioToText:{url:2},imageOp:{type:0,stripmeta:1,typeType:2,blurRadius:3,blurSigma:4,qualityQuality:5,cropX:6,cropY:7,cropWidth:8,cropHeight:9,rotateAngle:10,pixelPixel:11,thumbnailMode:12,thumbnailWidth:13,thumbnailHeight:14,thumbnailAxisX:15,thumbnailAxisY:16,thumbnailCenterX:17,thumbnailCenterY:18,thumbnailEnlarge:19,thumbnailToStatic:20,watermarkType:21,watermarkGravity:22,watermarkDissolve:23,watermarkDx:24,watermarkDy:25,watermarkImage:26,watermarkText:27,watermarkFont:28,watermarkFontSize:29,watermarkFontColor:30,interlace:31},robot:{account:4,nick:5,avatar:6,intro:7,config:8,valid:9,createTime:10,updateTime:11,custid:12,botid:13,bindTime:14},clientAntispam:{version:1,md5:2,nosurl:3,thesaurus:4},fileQuickTransfer:{md5:1,url:2,size:3,threshold:4},transToken:{name:1,type:2,transType:3,size:4,extra:5,body:6},transInfo:{docId:1,name:2,prefix:3,size:4,type:5,state:6,transType:7,transSize:8,pageCount:9,picInfo:10,extra:11,flag:12},nosFileUrlTag:{safeUrl:0,originUrl:1},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3},fileListParam:{fromDocId:1,limit:2},avSignalTag:{type:1,channelName:2,channelId:3,channelCreateTime:4,channelExpireTime:5,creator:6,ext:7,channelInValid:8,from:10,to:11,requestId:12,needPush:13,pushTitle:14,pushContent:15,pushPayload:16,needBadge:17,members:18,attach:19,attachExt:20,isSave:21,msgid:22,uid:23,time:24},login:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appKey:18,account:19,browser:24,session:26,deviceInfo:32,isReactNative:112,token:1e3,customTag:38,customClientType:39},loginRes:{lastLoginDeviceId:17,customTag:38,connectionId:102,ip:103,port:104,country:106,hasXMPush:111},loginPort:{type:3,os:4,mac:5,deviceId:13,account:19,deviceInfo:32,connectionId:102,ip:103,time:109,customTag:38},aosPushInfo:{pushType:110,hasTokenPreviously:111},sync:{myInfo:1,offlineMsgs:2,teams:3,netcallMsgs:6,roamingMsgs:7,relations:9,friends:11,sessions:12,friendUsers:13,msgReceipts:14,myTeamMembers:15,donnop:16,deleteMsg:17,sessionAck:18,robots:19,broadcastMsgs:20,avSignal:21,superTeams:22,myInfoInSuperTeams:23,superTeamRoamingMsgs:24,deleteSuperTeamMsg:25,superTeamSessionAck:26,deleteMsgSelf:27,stickTopSessions:28,sessionHistoryMsgsDelete:29,filterMsgs:100},donnop:{open:1},sessionReqTag:{minTimestamp:1,maxTimestamp:2,needLastMsg:3,limit:4,hasMore:5},session:{id:1,updateTime:2,ext:3,lastMsg:4},superTeam:{teamId:1,name:3,type:4,owner:5,level:6,selfCustom:7,valid:8,memberNum:9,memberUpdateTime:10,createTime:11,updateTime:12,validToCurrentUser:13,intro:14,announcement:15,joinMode:16,bits:17,custom:18,serverCustom:19,avatar:20,beInviteMode:21,inviteMode:22,updateTeamMode:23,updateCustomMode:24,mute:100,muteType:101},superTeamMember:{teamId:1,account:3,type:4,nickInTeam:5,bits:7,active:8,valid:9,createTime:10,updateTime:11,custom:12,mute:13,invitoraccid:14,joinTime:15},team:{teamId:1,name:3,type:4,owner:5,level:6,selfCustom:7,valid:8,memberNum:9,memberUpdateTime:10,createTime:11,updateTime:12,validToCurrentUser:13,intro:14,announcement:15,joinMode:16,bits:17,custom:18,serverCustom:19,avatar:20,beInviteMode:21,inviteMode:22,updateTeamMode:23,updateCustomMode:24,mute:100,muteType:101},teamMember:{teamId:1,account:3,type:4,nickInTeam:5,bits:7,active:8,valid:9,joinTime:10,updateTime:11,custom:12,mute:13,invitorAccid:14},msg:{scene:0,to:1,from:2,fromClientType:4,fromDeviceId:5,fromNick:6,time:7,type:8,body:9,attach:10,idClient:11,idServer:12,resend:13,userUpdateTime:14,custom:15,pushPayload:16,pushContent:17,apnsAccounts:18,apnsContent:19,apnsForcePush:20,yidunEnable:21,antiSpamContent:22,antiSpamBusinessId:23,clientAntiSpam:24,antiSpamUsingYidun:25,needMsgReceipt:26,needUpdateSession:28,replyMsgFromAccount:29,replyMsgToAccount:30,replyMsgTime:31,replyMsgIdServer:32,replyMsgIdClient:33,threadMsgFromAccount:34,threadMsgToAccount:35,threadMsgTime:36,threadMsgIdServer:37,threadMsgIdClient:38,delete:39,callbackExt:40,subType:41,yidunAntiCheating:42,env:43,isHistoryable:100,isRoamingable:101,isSyncable:102,isMuted:104,cc:105,isInBlackList:106,isPushable:107,isOfflinable:108,isUnreadable:109,needPushNick:110,isReplyMsg:111,tempTeamMemberCount:112},threadMsgReq:{beginTime:1,endTime:2,lastMsgId:3,limit:4,reverse:5},threadMsgsMeta:{total:1,lastMsgTime:2},comment:{from:1,body:2,time:3,custom:4,needPush:5,needBadge:6,pushTitle:7,apnsText:8,pushPayload:9},commentReq:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,timestamp:100},commentRes:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,detail:7,modify:8,timestamp:100},collect:{id:1,type:2,data:3,custom:4,uniqueId:5,createTime:6,updateTime:7},collectQuery:{beginTime:1,endTime:2,lastMsgId:3,limit:4,reverse:5,type:6},stickTopSession:{id:1,topCustom:2,createTime:3,updateTime:4},pinTag:{pinFrom:1,pinCustom:2,createTime:3,updateTime:4},msgPinReq:{sessionId:1,timetag:2},msgPinRes:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,pinFrom:7,pinCustom:8},msgReceipt:{to:1,from:2,time:7,idClient:11},teamMsgReceipt:{teamId:0,idServer:1,read:100,unread:101,idClient:102,account:103},deleteMsgSelfTag:{scene:1,from:2,to:3,idServer:4,idClient:5,time:6,deletedTime:7,custom:8},sysMsg:{time:0,type:1,to:2,from:3,ps:4,attach:5,idServer:6,sendToOnlineUsersOnly:7,apnsText:8,pushPayload:9,deletedIdClient:10,deletedIdServer:11,yidunEnable:12,antiSpamContent:13,deletedMsgTime:14,deletedMsgFromNick:15,opeAccount:16,env:21,cc:105,isPushable:107,isUnreadable:109,needPushNick:110},broadcastMsg:{broadcastId:1,fromAccid:2,fromUid:3,timestamp:4,body:5},friend:{account:4,flag:5,beflag:6,source:7,alias:8,bits:9,custom:10,createTime:11,updateTime:12,serverex:13},user:{account:1,nick:3,avatar:4,sign:5,gender:6,email:7,birth:8,tel:9,custom:10,createTime:12,updateTime:13},specialRelation:{account:0,isMuted:1,isBlacked:2,createTime:3,updateTime:4},msgType:{text:0,picture:1,audio:2,video:3,location:4,notification:5,file:6,netcall_audio:7,netcall_vedio:8,datatunnel_new:9,tips:10,robot:11,custom:100},msgEvent:{type:1,value:2,idClient:3,custom:4,validTime:5,broadcastType:6,sync:7,validTimeType:8,durable:9,time:10,idServer:11,clientType:12,serverConfig:13,serverCustom:14,appid:101,account:103,enableMultiClient:104,consid:106},msgEventSubscribe:{type:1,subscribeTime:2,sync:3,to:102,from:104,time:105},clearMsgsParams:{account:1,delRoam:2},clearMsgsParamsWithSync:{type:0,otherAccid:1,isDeleteRoam:2,toTid:3,isSyncSelf:4,fromAccid:5,time:6,ext:7},delFriendParams:{delAlias:1}}},function(e,t,n){"use strict";var r=n(61).fn,o=n(0),i=n(184);r.processAvSignal=function(e){switch(e.cmd){case"signalingCreate":case"signalingDelay":case"signalingClose":case"signalingJoin":case"signalingLeave":case"signalingInvite":case"signalingCancel":case"signalingReject":case"signalingAccept":case"signalingControl":case"signalingSyncMsgRead":case"signalingGetChannelInfo":break;case"signalingNotify":this.onSignalingNotify(e);break;case"signalingMutilClientSyncNotify":this.onSignalingMutilClientSyncNotify(e);break;case"signalingUnreadMessageSyncNotify":this.onSignalingUnreadMessageSyncNotify(e);break;case"signalingChannelsSyncNotify":this.onSignalingMembersSyncNotify(e);break;default:this.logger.log("avSignal::unhandled cmd:",e.cmd)}};var s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e.needPush&&(e.needPush="1"===e.needPush),e.needBadge&&(e.needBadge="1"===e.needBadge),e.channelInValid&&(e.channelInValid="1"===e.channelInValid),e.attach){var t=JSON.parse(e.attach);e.eventType=i.parseAvSignalType(t.type)}if(e.members){var n=JSON.parse(e.members);e.members=n.map(function(e){return i.parseAvSignalMember(e)})}return e};r.onSignalingNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{e.raw&&e.raw.r&&e.raw.r.length&&e.content&&e.content.avSignalTag&&(e.content.avSignalTag.msgid=e.raw.r[0]);var n=e.content.avSignalTag;n=Array.isArray(n)?n.map(function(e){return s(e)}):s(n),this.emitAPI({type:"signalingNotify",obj:n}),o.isFunction(this.options.onSignalingNotify)&&this.options.onSignalingNotify(n)}},r.onSignalingMutilClientSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingMutilClientSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else this.emitAPI({type:"signalingMutilClientSyncNotify",obj:e.content}),o.isFunction(this.options.onSignalingMutilClientSyncNotify)&&this.options.onSignalingMutilClientSyncNotify(e.content)},r.onSignalingUnreadMessageSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingUnreadMessageSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{var n=e.content.avSignalTag;Array.isArray(n)&&(n=n.map(function(e){return s(e)})),this.emitAPI({type:"signalingUnreadMessageSyncNotify",obj:n}),o.isFunction(this.options.onSignalingUnreadMessageSyncNotify)&&this.options.onSignalingUnreadMessageSyncNotify(n)}},r.onSignalingMembersSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingMembersSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{var n=e.content.avSignalTag;Array.isArray(n)||(n=[n]),n=n.map(function(e){return s(e)}),this.emitAPI({type:"signalingChannelsSyncNotify",obj:n}),o.isFunction(this.options.onSignalingMembersSyncNotify)&&this.options.onSignalingMembersSyncNotify(n)}}},function(e,t,n){"use strict";var r=n(61).fn,o=n(185),i=n(5),s=n(39);r.processMisc=function(e){switch(e.cmd){case"getSimpleNosToken":e.error||(e.obj=e.content.nosTokens[0]);break;case"getNosToken":e.error||(e.obj=e.content.nosToken);break;case"uploadSdkLogUrl":e.error?this.logger.error("uploadSdkLogUrl::error",e.error):this.logger.info("uploadSdkLogUrl::success",e.obj&&e.obj.url);break;case"notifyUploadLog":e.error||(i.isRN&&this.uploadLocalLog(),this.emitAPI({type:"notifyUploadLog"}));break;case"audioToText":e.error||(e.obj.text=e.content.text);break;case"processImage":e.obj.imageOps=o.reverseImageOps(e.obj.imageOps),e.error||(e.obj={url:e.content.url});break;case"getNosTokenTrans":e.error||(e.obj={nosToken:e.content.nosToken,docId:e.content.docId});break;case"getNosOriginUrl":e.error||(e.obj=e.content.nosFileUrlTag.originUrl);break;case"notifyTransLog":e.error||this.emitAPI({type:"notifyTransLog",obj:e.content.transInfo});break;case"fetchFile":case"fetchFileList":case"removeFile":e.error||(e.obj=e.content);break;case"getServerTime":e.obj=e.content.time}},r.uploadLocalLog=function(e){if(i.isRN&&s.rnfs){var t=s.rnfs,n=this,r=t.nimIndex,o=(t.nimIndex+1)%2;t.nimPromise=t.nimPromise.then(function(){return Promise.all([t.exists(a(r)),t.exists(a(o))])}).then(function(e){return e&&(e[0]||e[1])?e[0]&&e[1]?t.copyFile(a(o),a(2)).then(function(){return t.readFile(a(r))}).then(function(e){return t.appendFile(a(2),e)}):e[0]?t.copyFile(a(r),a(2)):void(e[1]&&t.copyFile(a(o),a(2))):Promise.reject()}).then(function(e){return new Promise(function(e,r){n.api.previewFile({filePath:a(2),done:function(r,o){if(Promise.all([t.unlink(a(2)),t.unlink(a(1)),t.unlink(a(0))]).finally(function(){e()}),r)n.logger.error("nim::uploadLocalLog:previewFile:error",r);else{var i=o.url;i.indexOf("?")>0?i+="&":i+="?",i+="download="+(new Date).getTime()+"_web.log",n.api.uploadSdkLogUrl({url:i})}}})})}).catch(function(e){t.unlink(a(2)).catch(function(e){}),n.logger.error("nim::protocol::uploadLocalLog",e)})}function a(e){return t.DocumentDirectoryPath+"/nimlog_"+e+".log"}}},function(e,t,n){"use strict";var r=n(61).fn,o=n(5);r.processLink=function(e){switch(e.cmd){case"heartbeat":this.startHeartbeat()}},r.startHeartbeat=function(){var e=this;e.stopHeartbeat(),e.heartbeatTimer=setTimeout(function(){e.sendCmd("heartbeat",null,e.onHeartbeat.bind(e))},o.heartbeatInterval)},r.stopHeartbeat=function(){this.heartbeatTimer&&(clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null)},r.onHeartbeat=function(e,t){e&&(e.callFunc="link::onHeartbeat",this.api.reportLogs({event:"ping_timeout"}),this.onCustomError("heartbeat error",e))},r.heartbeat=function(){}},function(e,t,n){"use strict";var r,o=n(57),i=(r=o)&&r.__esModule?r:{default:r};var s,a=n(61).fn,c=n(27),u=n(19),l=n(130),m=n(39),p=n(5),d=n(183),f=n(0),g=f.notundef;a.login=function(){this.doLogin()},a.doLogin=function(){var e=this;Promise.resolve().then(function(){return e.assembleLogin()}).then(function(t){e.sendCmd("login",(0,i.default)({},t),e.onLogin.bind(e)),e.autoconnect=!1})},a.genSessionKey=(s={},function(){var e=this.name;return s[e]=s[e]||f.guid()}),a.assembleIMLogin=function(){var e=this.options,t=e.account,n=this.autoconnect?0:1;this.sdkSession=this.genSessionKey();var r={appLogin:n,appKey:e.appKey,account:t,token:e.token,sdkVersion:p.info.sdkVersion,protocolVersion:p.info.protocolVersion,os:u.os.toString(),browser:u.name+" "+u.version,clientType:p.CLIENTTYPE||16,session:this.sdkSession,deviceId:m.deviceId,isReactNative:p.isRN?1:0,customTag:e.customTag||""};return e.customClientType&&(r.customClientType=+e.customClientType),r},a.onLogin=function(e,t){var n=this,r=0;n.loginResult=t,e?n.onAuthError(e,"link::onLogin"):(n.startHeartbeat(),n.afterLogin(t),n.initOnlineListener(),r=5e3),!0===n.options.logReport&&setTimeout(function(){var e={appKey:n.options.appKey,sdk_ver:p.info.version,deviceId:m.deviceId};d.reportErrEvent(e)},r)},a.afterLogin=f.emptyFunc,a.notifyLogin=function(){var e=this.loginResult;this.logger.info("link::notifyLogin: on connect",e),this.options.onconnect(e)},a.logout=function(){var e="done disconnect";if(this.doLogout)return this.doLogout=!1,e="done logout",void this.onAuthError(new c(e,"logout"),"link::logout");if(this.isConnected()){var t=new c(e,"logout");this.onAuthError(t,"link::logout")}},a.onKicked=function(e){var t=e.content,n=t.from,r=t.reason,o=t.custom,i=t.customClientType,s={reason:this.kickedReasons[r]||"unknown",message:this.kickedMessages[r]||"未知原因"};if(g(n)&&(s.from=l.reverseType(n)),g(o)&&(s.custom=o),+i>0&&(s.customClientType=i),this.shouldNotifyKicked(s)){var a=new c("被踢了","kicked");f.merge(a,s),this.onAuthError(a,"link::onKicked")}else this.logger.warn("link::onKicked: silentlyKick"),this.shouldReconnect=!0,this.hasNotifyDisconnected=!0,this.disconnectSocket()},a.shouldNotifyKicked=function(e){return"silentlyKick"!==e.reason},a.onAuthError=function(e,t){this.shouldReconnect=!1,(e=e||c.newConnectionError({callFunc:t})).callFunc=e.callFunc||t||null,this.markAllCallbackInvalid(e),this.notifyDisconnect(e)}},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";var r=n(61).fn,o=n(27),i=n(206),s=n(182),a=n(5),c=n(0);r.initConnect=function(){this.socket=null,this.retryCount=0,this.connecting=!1,this.shouldReconnect=!0,this.hasNotifyDisconnected=!1,this.doLogout=!1},r.resetConnect=function(){var e=this.options;c.notundef(e.needReconnect)?(c.verifyParamType("needReconnect",e.needReconnect,"boolean","link::resetConnect"),this.needReconnect=e.needReconnect):this.needReconnect=!0,this.logger.log("link::resetConnect: needReconnect "+this.needReconnect),c.notundef(e.reconnectionAttempts)&&c.verifyParamType("reconnectionAttempts",e.reconnectionAttempts,"number","link::resetConnect"),c.notundef(e.noCacheLinkUrl)&&c.verifyParamType("noCacheLinkUrl",e.noCacheLinkUrl,"boolean","link::resetConnect"),this.reconnectionAttempts="number"==typeof e.reconnectionAttempts?e.reconnectionAttempts:1/0,this.backoff=new i({min:a.reconnectionDelay,max:a.reconnectionDelayMax,jitter:a.reconnectionJitter})},r.connect=function(e){if(clearTimeout(this.reconnectTimer),this.isConnected())this.logger.warn("link::connect: already connected");else if(this.connecting)this.logger.warn("link::connect: already connecting");else if(this.connecting=!0,this.hasNotifyDisconnected=!1,this.socket&&this.socket.socket)this.logger.info("link::connect: try connecting..."),this.socket.socket.connect();else if(this.options.socketUrl&&"string"==typeof this.options.socketUrl)this.connectToUrl(this.options.socketUrl);else{var t=this.getNextSocketUrl();t&&!this.options.noCacheLinkUrl?this.connectToUrl(t):this.refreshSocketUrl()}},r.getNextSocketUrl=function(){return this.socketUrls.shift()},r.isConnected=function(){return!!this.socket&&!!this.socket.socket&&this.socket.socket.connected},r.connectToUrl=function(e){var t=this;if(e=e||"",t.logger.log("link::connectToUrl: "+e),"undefined"==typeof window){var n=c.getGlobal(),r=e.split(":");n&&!n.location&&r.length>1&&(n.location={protocol:r.shift(),port:r.pop(),hostname:r.join("")}),this.options.transports=["websocket"]}var o=this.options.transports||["websocket","xhr-polling"];t.socket=s.connect(e,{transports:o,reconnect:!1,"force new connection":!0,"connect timeout":a.connectTimeout}),t.logger.info("link::connectToUrl: socket url: "+e+", transports: "+JSON.stringify(o)),t.socket.on("connect",t.onConnect.bind(t)),t.socket.on("handshake_failed",t.onHandshakeFailed.bind(t)),t.socket.on("connect_failed",t.onConnectFailed.bind(t)),t.socket.on("error",t.onError.bind(t)),t.socket.on("message",t.onMessage.bind(t)),t.socket.on("disconnect",function(n){t.logger.warn("link::connectToUrl: socket url: "+e+", disconnected"),t.doLogout?t.logout():t.onDisconnect(!0,"link::socketDisconnect")})},r.disconnect=function(e){var t=this;function n(n){t.logger.info("link::disconnect: socket finally closed, ",n),clearTimeout(t.disconnectCallbackTimer),e(n)}e instanceof Function||(e=function(){}),clearTimeout(t.connectTimer),t.disconnectCallbackTimer=setTimeout(function(){e.call(t,"mark disconnected due to timeout")},1e4),t.socket&&t.socket.socket&&t.socket.socket.transport?t.socket.socket.transport.onDisconnectDone=function(e){n(e)}:n(null),t.isConnected()?(t.logger.log("link::disconnect: start disconnecting"),t.logout()):t.connecting?(t.logger.log("link::disconnect: abort connecting"),t.disconnectSocket()):(t.logger.log("link::disconnect: start otherwise"),t.connecting=!1,t.shouldReconnect=!1,t.needReconnect=!1,t.socket=null,t.options.ondisconnect({callFunc:"link::disconnect",message:"manually disconnect status"}))},r.onConnect=function(){this.backoff&&this.backoff.reset(),this.reconnectStatus=this.retryCount>0?1:0,this.retryCount=0,this.connecting=!1,this.shouldReconnect=!0,this.hasNotifyDisconnected=!1,this.logger.log("link::onConnect: socket onconnected, start login"),this.login(),this.api.reportLogs({event:"ws_connected"})},r.onHandshakeFailed=function(){this.logger.warn("link::onHandshakeFailed: shandshake failed"),this.api.reportLogs({event:"ws_handshake_failed"}),this.onDisconnect(1,"link::onHandshakeFailed")},r.onConnectFailed=function(){this.api.reportLogs({event:"ws_connect_failed"}),this.onDisconnect(1,"link::onConnectFailed")},r.onError=function(){var e=arguments[0];if(e){if(this.api.reportLogs({event:"connect_timeout"}),void 0!==e.x5ImgDecodeStatus)return;if("[object Object]"===Object.prototype.toString.call(e)&&Object.keys(e).length<=0)return;this.onMiscError("连接错误",new o(e,"LINK_ERROR",{callFunc:"link::onError"}))}this.connecting=!1},r.onDisconnect=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;1!==e&&(this.connected=e),this.connecting=!1,this.markAllCallbackInvalid(o.newNetworkError({callFunc:t})),this.stopHeartbeat(),this.reconnect()},r.willReconnect=function(){return this.shouldReconnect&&this.needReconnect&&this.retryCount<this.reconnectionAttempts},r.reconnect=function(){var e=this;if(e.willReconnect())if(e.socket&&e.socket.socket&&e.socket.socket.transport&&e.socket.socket.transport.websocket){e.logger.info("link::reconnect: on socket closed"),e.socket.socket.transport.onConnectionOver=function(){e.logger.log("link::reconnect: on connectionOver"),clearTimeout(e.reconnectTimer),e.doReconnect()};try{e.socket.socket.transport.websocket.close()}catch(t){e.logger.warn("link::reconnect: force disconnect error:",t)}}else clearTimeout(e.reconnectTimer),e.reconnectTimer=setTimeout(function(){e.logger.info("link::reconnect: on socket timeout"),e.doReconnect()},100);else e.notifyDisconnect()},r.doReconnect=function(){var e=this;if(e.socket&&e.socket.socket&&e.socket.socket.connected)e.logger.log("link::reconnect on connectionOver, but socket is connected");else{e.socket=null,e.connected&&(e.autoconnect=!0),e.retryCount++,e.appLogin=1;var t=e.backoff.duration();e.logger.info("link::reconnect: will retry after "+t+"ms, retryCount "+e.retryCount),e.options.onwillreconnect({retryCount:e.retryCount,duration:t}),clearTimeout(e.connectTimer),e.connectTimer=setTimeout(function(){e.connect()},t)}},r.notifyConnectError=function(e){var t=o.newConnectError({message:e,callFunc:"link::notifyConnectError"});this.logger.error("link::notifyConnectError:",t),this.options.onerror(t)},r.notifyDisconnect=function(e){var t=this;t.hasNotifyDisconnected||(t.hasNotifyDisconnected=!0,t.disconnectSocket(),(e=e||new o).retryCount=t.retryCount,e.willReconnect=t.willReconnect(),t.backoff&&t.backoff.reset(),t.retryCount=0,t.socket&&t.socket.socket&&t.socket.socket.transport&&t.socket.socket.transport.websocket?(t.logger.info("link::notifyDisconnect: ondisconnected",e),t.socket.socket.transport.onConnectionOver=function(){t.socket=null,t.options.ondisconnect(e)}):(t.logger.info("link::notifyDisconnect: ondisconnected/no transport ws",e),t.options.ondisconnect(e)),t.onWbNotifyHangup instanceof Function&&t.onWbNotifyHangup({content:{account:t.account,channelId:null,timetag:+Date()}}))},r.disconnectSocket=function(){if(this.isConnected()||this.connecting)try{this.connecting=!1,this.shouldReconnect=!1,this.socket.disconnect()}catch(e){this.logger.info("link::disconnectSocket: disconnect failed, error ",e)}},r.initOnlineListener=function(e){this.needReconnect&&this.options&&this.options.quickReconnect&&"undefined"!=typeof window&&c.isFunction(window.addEventListener)&&(this.onlineListener||(this.onlineListener=function(){var e=this;if(!e||!e.isConnected()||e.connecting)return;e.stopHeartbeat(),e.sendCmd("heartbeat",null,function(t){if(t)try{e.socket.socket.transport.websocket.onclose()}catch(e){c.onError("no sockcet",e)}})}.bind(this),window.addEventListener("online",this.onlineListener)))}},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=n(53).fn,a=n(0),c=n(86),u=n(184);s.signalingCreate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type,n=e.channelName,r=e.ext;return a.verifyOptions(e,"type","api::signalling"),this.sendCmdUsePromise("signalingCreate",{avSignalTag:{type:t,channelName:n,ext:r}}).then(function(e){var t=e.avSignalTag;return Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingDelay=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return a.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingDelay",{avSignalTag:e}).then(function(e){var t=e.avSignalTag;return Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingClose=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return a.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingClose",{avSignalTag:a.merge(e,{isSave:!0===t?1:0})}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingJoin=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return a.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingJoin",{avSignalTag:a.merge(e,{isSave:!0===t?1:0})}).then(function(e){var t=e.avSignalTag,n=t.members;return"string"==typeof t.members&&(n=JSON.parse(t.members).map(function(e){return u.parseAvSignalMember(e)})),t.members=n,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingLeave=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return a.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingLeave",{avSignalTag:a.merge(e,{isSave:!0===t?1:0})}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingGetChannelInfo=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.channelName;return a.verifyOptions(e,"channelName","api::signalling"),this.sendCmdUsePromise("signalingGetChannelInfo",{avSignalTag:{channelName:t}}).then(function(e){var t=e.avSignalTag,n=t.members;return"string"==typeof t.members&&(n=JSON.parse(t.members).map(function(e){return u.parseAvSignalMember(e)})),t.members=n,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingInvite=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.account,n=e.offlineEnabled,r=e.pushInfo,o=void 0===r?{}:r;a.verifyOptions(e,"channelId requestId account","api::signalling"),"object"===(0,i.default)(o.pushPayload)&&(o.pushPayload=JSON.stringify(o.pushPayload));var s=a.merge(e,o,{to:t,isSave:!0===n?1:0,needPush:!0===o.needPush?1:0,needBadge:!1===o.needBadge?0:1});return this.sendCmdUsePromise("signalingInvite",{avSignalTag:s}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,t.needBadge=1===t.needBadge,t.needPush=1===t.needPush,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingCancel=function(e){var t=e.account,n=e.offlineEnabled;return a.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingCancel",{avSignalTag:a.merge(e,{to:t,isSave:!0===n?1:0})}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingCreateAndJoin=function(e){var t=this,n=e.channelName,r=e.uid,o=void 0===r?0:r,i=e.offlineEnabled,s=void 0===i||i,c=e.attachExt,u=void 0===c?"":c;return this.signalingCreate(e).catch(function(e){return 10405===e.code?(t.logger.warn("api::avSignal:signalingCall room already exists:",e),t.signalingGetChannelInfo({channelName:n})):Promise.reject(e)}).then(function(e){var n={channelId:e.channelId,offlineEnabled:s,attachExt:u};return o&&a.merge(n,{uid:o}),t.signalingJoin(n)})},s.signalingCall=function(e){var t=this,n=e.account,r=e.offlineEnabled,o=e.requestId;a.verifyOptions(e,"type requestId account","api::signalling");var i="";return this.signalingCreateAndJoin(e).then(function(s){t.logger.log("api::avSignal:signalingCall join:",s);var a={channelId:i=s.channelId||i,account:n,requestId:o,offlineEnabled:r,attachExt:e.attachExt||"",pushInfo:e.pushInfo||{}};return t.signalingInvite(a)})},s.signalingReject=function(e){var t=e.account,n=e.offlineEnabled;return a.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingReject",{avSignalTag:a.merge(e,{to:t,isSave:!0===n?1:0})}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingAccept=function(e){var t=this,n=e.account,r=e.offlineEnabled;return a.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingAccept",{avSignalTag:a.merge(e,{to:n,isSave:!0===r?1:0})}).then(function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))}).then(function(n){if(e.autoJoin){var r={channelId:e.channelId,offlineEnabled:e.offlineEnabled,attachExt:e.joinAttachExt,uid:e.uid};return t.signalingJoin(r)}return n})},s.signalingControl=function(e){var t=e.account;return a.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingControl",{avSignalTag:a.merge(e,t?{to:t}:{})}).then(function(e){var t=e.avSignalTag;return Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingSync=function(){return this.sendCmdUsePromise("sync",{sync:{avSignal:0}}).then(function(e){var t=e.avSignalTag;return Promise.resolve(t)}).catch(function(e){return Promise.reject(u.parseAvSignalError(e))})},s.signalingMarkMsgRead=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};a.verifyOptions(e,"msgid","api::signalling");var t=c.idMap.avSignal,n=void 0;return n="string"==typeof e.msgid?[e.msgid]:e.msgid,this.sendCmd("batchMarkRead",{sid:t.id,cid:t.signalingNotify,ids:n})}},function(e,t,n){"use strict";var r=n(53).fn,o=n(0),i=n(54),s=n(5),a=n(19);(a=a||{}).name=a.name||"",a.version=a.version||"",r.reportLogs=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this,n=t.options,r=s.ntServerAddress;if(r){var c=s.info;e=o.merge(e,{appkey:n.appKey,uid:n.account,os:"web",session:t.protocol.sdkSession||"",ver:c.sdkVersion,type:t.subType,platform:""+a.name.toLowerCase()+a.version.replace(/(\.\d+)+$/,"")});var u=r+o.genUrlSep(r),l=[];for(var m in e)l.push(m+"="+e[m]);u+=l.join("&"),i(u,{proxyUrl:o.url2origin(u)+"/lbs/res/cors/nej_proxy_frame.html",timeout:s.xhrTimeout,onload:function(){},onerror:function(e){t.logger.info("report::ajax report error",e)}})}}},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=n(0),a=n(53).fn;function c(e,t,n,r){var o=!1,i="";if(1===n?e.indexOf(t)>=0&&(o=!0,i=t):2===n&&(i=new RegExp(t,"g")).test(e)&&(o=!0),o&&""!==i)switch(r){case 1:return e.replace(i,"**");case 2:return{code:2};case 3:return{code:3}}return e}function u(e,t){for(var n=t.match,r=t.operate,o=e,s=0;s<t.keys.length;s++){var a=t.keys[s],u=a.match||n,l=a.operate||r;try{if("object"===(void 0===(o=c(o,a.key,u,l))?"undefined":(0,i.default)(o)))return o}catch(e){this.logger.warn("misc::filterContent: js cannot parse this regexp ",e)}}return o}a.uploadSdkLogUrl=function(e){return s.verifyOptions(e,"url","misc::uploadSdkLogUrl"),this.cbAndSendCmd("uploadSdkLogUrl",e)},a.getClientAntispamLexicon=function(e){var t=this,n=(e=e||{}).done;n instanceof Function||(n=function(){}),e={clientAntispam:{version:0}};var r=this;return this.protocol.sendCmd("getClientAntispam",e,function(e,o,i){e?(r.protocol.logger.error("misc::getClientAntispamLexicon:",e),n.call(t,e,{})):(n.call(t,null,i),r.antispamLexicon=i.clientAntispam||{})})},a.filterClientAntispam=function(e){var t=e.content,n=e.antispamLexicon;if(!t)return{code:404,errmsg:"待反垃圾文本content不存在"};n=n||this.antispamLexicon||{};var r=this.antispamLexicon&&this.antispamLexicon.thesaurus;if(!r)return{code:404,errmsg:"没有反垃圾词库或者词库格式不合法"};try{r=JSON.parse(r).thesaurus}catch(e){return this.protocol.logger.error("misc::filterClientAntispam: parse thesaurus error"),{code:500,errmsg:"反垃圾词库格式不合法"}}for(var o=t,s=0;s<r.length;s++)if("object"===(void 0===(o=u.call(this,o,r[s]))?"undefined":(0,i.default)(o))){if(2===o.code)return{code:200,type:2,errmsg:"建议拒绝发送",content:t,result:""};if(3===o.code)return{code:200,type:3,errmsg:"建议服务器处理反垃圾，发消息带上字段clientAntiSpam",content:t,result:t}}return o===t?{code:200,type:0,errmsg:"",content:t,result:o}:{code:200,type:1,errmsg:"已对特殊字符做了过滤",content:t,result:o}},a.getServerTime=function(e){this.processCallback(e),this.sendCmd("getServerTime",{},e.callback)},a.getNosAccessToken=function(e){s.verifyOptions(e,"url","misc::getNosAccessToken"),this.processCallback(e);var t={url:e.url};e.userAgent&&(t.userAgent=e.userAgentv),e.ext&&(t.ext=e.ext),this.sendCmd("getNosAccessToken",{nosAccessTokenTag:t},function(t,n,r){var o=r&&r.nosAccessTokenTag&&r.nosAccessTokenTag.token,i=e.url,s=o?{token:o,resUrl:i.indexOf("?")?i+"&token="+o:i+"?token="+o}:{};e.done(t,s)})},a.deleteNosAccessToken=function(e){s.verifyOptions(e,"token","misc::deleteNosAccessToken"),this.processCallback(e),this.sendCmd("deleteNosAccessToken",{nosAccessTokenTag:{token:e.token}},e.callback)}},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r},s=n(74);var a,c=n(0),u=n(53).fn;u.viewImageSync=function(e){var t=this.options;c.verifyOptions(e,"url","nos::viewImageSync");var n=e.url,r=(0,s.url2object)(n),o=r.protocol,a=r.hostname,u=r.path,l=r.query;if("boolean"==typeof e.strip&&(l.stripmeta=e.strip?1:0),"number"==typeof e.quality&&(c.verifyParamMin("quality",e.quality,0,"nos::viewImageSync"),c.verifyParamMax("quality",e.quality,100,"nos::viewImageSync"),l.quality=Math.round(e.quality)),"boolean"==typeof e.interlace&&(l.interlace=e.interlace?1:0),"number"==typeof e.rotate&&(l.rotate=Math.round(e.rotate)),"object"===(0,i.default)(e.thumbnail)){var m=e.thumbnail.mode||"crop",p=e.thumbnail.width,d=e.thumbnail.height;if(p>=0&&d>=0&&p<4096&&d<4096&&(p>0||d>0)){switch(m){case"crop":m="y";break;case"contain":m="x";break;case"cover":m="z";break;default:m="x"}l.thumbnail=""+p+m+d}}if(t.downloadUrl){var f=(0,s.url2object)(e.url),g=t.downloadUrl,y=f.path,h=y.indexOf("/");if(-1!==h){var v=y.substring(0,h),b=y.substring(h+1);g=g.replace("{bucket}",v).replace("{object}",b)}var T=(0,s.url2object)(g);return(0,s.object2url)({protocol:T.protocol,hostname:T.hostname,path:T.path,query:c.merge(T.query,l)})}return(0,s.object2url)({protocol:o,hostname:a,path:u,query:l})},u.viewImageStripMeta=function(e){c.verifyOptions(e,"url strip","nos::viewImageStripMeta"),c.verifyParamType("strip",e.strip,"boolean","nos::viewImageStripMeta");var t="stripmeta="+(e.strip?1:0),n=(0,s.genUrlSep)(e.url);return e.url+n+t},u.viewImageQuality=function(e){c.verifyOptions(e,"url quality","nos::viewImageQuality"),c.verifyParamType("quality",e.quality,"number","nos::viewImageQuality"),c.verifyParamMin("quality",e.quality,0,"nos::viewImageQuality"),c.verifyParamMax("quality",e.quality,100,"nos::viewImageQuality");var t="quality="+Math.round(e.quality),n=(0,s.genUrlSep)(e.url);return e.url+n+t},u.viewImageInterlace=function(e){c.verifyOptions(e,"url","nos::viewImageInterlace");var t=(0,s.genUrlSep)(e.url);return e.url+t+"interlace=1"},u.viewImageRotate=function(e){for(c.verifyOptions(e,"url angle","nos::viewImageRotate"),c.verifyParamType("angle",e.angle,"number","nos::viewImageRotate");e.angle<0;)e.angle=e.angle+360;e.angle=e.angle%360;var t="rotate="+Math.round(e.angle),n=(0,s.genUrlSep)(e.url);return e.url+n+t},u.viewImageBlur=function(e){c.verifyOptions(e,"url radius sigma","nos::viewImageBlur"),c.verifyParamType("radius",e.radius,"number","nos::viewImageBlur"),c.verifyParamMin("radius",e.radius,1,"nos::viewImageBlur"),c.verifyParamMax("radius",e.radius,50,"nos::viewImageBlur"),c.verifyParamType("sigma",e.sigma,"number","nos::viewImageBlur"),c.verifyParamMin("sigma",e.sigma,0,"nos::viewImageBlur");var t="blur="+Math.round(e.radius)+"x"+Math.round(e.sigma),n=(0,s.genUrlSep)(e.url);return e.url+n+t},u.viewImageCrop=function(e){c.verifyOptions(e,"url x y width height","nos::viewImageCrop"),c.verifyParamType("x",e.x,"number","nos::viewImageCrop"),c.verifyParamMin("x",e.x,0,"nos::viewImageCrop"),c.verifyParamType("y",e.y,"number","nos::viewImageCrop"),c.verifyParamMin("y",e.y,0,"nos::viewImageCrop"),c.verifyParamType("width",e.width,"number","nos::viewImageCrop"),c.verifyParamMin("width",e.width,0,"nos::viewImageCrop"),c.verifyParamType("height",e.height,"number","nos::viewImageCrop"),c.verifyParamMin("height",e.height,0,"nos::viewImageCrop");var t="crop="+Math.round(e.x)+"_"+Math.round(e.y)+"_"+Math.round(e.width)+"_"+Math.round(e.height),n=(0,s.genUrlSep)(e.url);return e.url+n+t},u.viewImageThumbnail=(a={cover:"z",contain:"x",crop:"y"},function(e){c.verifyOptions(e,"url mode","nos::viewImageThumbnail"),c.verifyParamValid("mode",e.mode,Object.keys(a),"nos::viewImageThumbnail"),"contain"===e.mode?c.verifyParamAtLeastPresentOne(e,"width height","nos::viewImageThumbnail"):c.verifyOptions(e,"width height","nos::viewImageThumbnail"),c.undef(e.width)&&(e.width=0),c.undef(e.height)&&(e.height=0),c.verifyParamType("width",e.width,"number","nos::viewImageThumbnail"),c.verifyParamMin("width",e.width,0,"nos::viewImageThumbnail"),c.verifyParamType("height",e.height,"number","nos::viewImageThumbnail"),c.verifyParamMin("height",e.height,0,"nos::viewImageThumbnail");var t=Math.round(e.width),n=Math.round(e.height),r="thumbnail="+t+a[e.mode]+n;"crop"===e.mode&&c.notundef(e.axis)&&(c.undef(e.axis.x)&&(e.axis.x=5),c.undef(e.axis.y)&&(e.axis.y=5),c.verifyParamMin("axis.x",e.axis.x,0,"nos::viewImageThumbnail"),c.verifyParamMax("axis.x",e.axis.x,10,"nos::viewImageThumbnail"),c.verifyParamMin("axis.y",e.axis.y,0,"nos::viewImageThumbnail"),c.verifyParamMax("axis.y",e.axis.y,10,"nos::viewImageThumbnail"),r=r+"&axis="+Math.round(e.axis.x)+"_"+Math.round(e.axis.y)),c.notundef(e.enlarge)&&(c.verifyParamType("enlarge",e.enlarge,"boolean","nos::viewImageThumbnail"),e.enlarge&&(r+="&enlarge=1"));var o=(0,s.genUrlSep)(e.url);return e.url+o+r})},function(e,t,n){"use strict";var r,o=n(0),i=n(53).fn,s=n(185),a=n(187),c=n(27);i.transDoc=function(e){o.verifyOptions(e,"fileInput done","nos::transDoc");try{var t=e.fileInput.files[0],n=e.fileInputName=t.name,r={ppt:1,pptx:2,pdf:3},i=n.substring(n.lastIndexOf(".")+1);if(["ppt","pdf","pptx"].indexOf(i)<0)return void e.done(c.newNoFileError("请上传正确格式的文件【ppt, pptx, pdf】",{callFunc:"nos: transDoc",fileInput:e.fileInput}),e)}catch(t){return void e.done(c.newNoFileError("请上传正确的文件节点",{callFunc:"msg::previewFile",fileInput:e.fileInput}),e)}var s=JSON.stringify(a.genResponseBody("file")||{}).replace(/"/gi,'\\"'),u={transToken:{name:n,type:r[i],transType:"png"===e.transcode?11:10,size:t.size,body:s}};this.getNosTokenTrans({responseBody:u,nosToken:{nosScene:e.nosScene||this.nosScene,nosSurvivalTime:e.nosSurvivalTime},callback:function(t,n){t?e.done(t):(e.nosToken=n.nosToken,e.docId=n.docId,this._doPreviewFile(e))}.bind(this)})},i.getSimpleNosToken=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.num=1,o.verifyOptions(e),this.cbAndSendCmd("getSimpleNosToken",e)},i.getNosToken=function(e){var t=e.callback,n=e.nosToken,r=e.responseBody,o={tag:n.nosScene};n.nosSurvivalTime&&n.nosSurvivalTime!==1/0&&(o.expireSec=n.nosSurvivalTime),this.sendCmd("getNosToken",{responseBody:r,nosToken:o},t)},i.getNosTokenTrans=function(e){this.sendCmd("getNosTokenTrans",e.responseBody,e.callback)},i.packFileDownloadName=function(e){o.verifyOptions(e,"url name",!0,"","nos::packFileDownloadName");var t=e.url;return t+o.genUrlSep(t)+"download="+encodeURIComponent(e.name)},i.audioToMp3=function(e){o.verifyOptions(e,"url","nos::audioToMp3");var t=e.url;return t+o.genUrlSep(t)+"audioTrans&type=mp3"},i.removeFile=function(e){this.sendCmd("removeFile",e,e.callback)},i.fetchFile=function(e){this.sendCmd("fetchFile",e,e.callback)},i.fetchFileList=function(e){this.sendCmd("fetchFileList",e,e.callback)},i.stripImageMeta=function(e){return this.beforeProcessImage(e,"stripmeta")},i.qualityImage=function(e){return this.beforeProcessImage(e,"quality")},i.interlaceImage=function(e){return this.beforeProcessImage(e,"interlace")},i.rotateImage=function(e){return this.beforeProcessImage(e,"rotate")},i.blurImage=function(e){return this.beforeProcessImage(e,"blur")},i.cropImage=function(e){return this.beforeProcessImage(e,"crop")},i.thumbnailImage=function(e){return this.beforeProcessImage(e,"thumbnail")},i.beforeProcessImage=function(e,t){var n=o.copy(e);return n.type=t,e.ops=[n],this.processImage(e)},i.processImage=function(e){var t=this;o.verifyOptions(e,"url ops",!0,"","nos::processImage"),o.verifyParamType("ops",e.ops,"array","nos::processImage");var n=e.ops.map(function(e){return o.verifyOptions(e,"type",!0,"","nos::processImage"),o.verifyParamValid("type",e.type,s.validTypes,"nos::processImage"),t["gen"+e.type.slice(0,1).toUpperCase()+e.type.slice(1)+"Op"](e)});t.processCallback(e),t.sendCmd("processImage",{url:e.url,imageOps:n},e.callback)},i.genStripmetaOp=function(e){return new s({type:e.type,stripmeta:e.strip?1:0})},i.genQualityOp=function(e){o.verifyOptions(e,"quality",!0,"","nos::genQualityOp"),o.verifyParamType("quality",e.quality,"number","nos::genQualityOp"),o.verifyParamMin("quality",e.quality,0,"nos::genQualityOp"),o.verifyParamMax("quality",e.quality,100,"nos::genQualityOp");var t=Math.round(e.quality);return new s({type:e.type,qualityQuality:t})},i.genInterlaceOp=function(e){return new s({type:e.type})},i.genRotateOp=function(e){for(o.verifyOptions(e,"angle",!0,"","nos::genRotateOp"),o.verifyParamType("angle",e.angle,"number","nos::genRotateOp");e.angle<0;)e.angle=e.angle+360;e.angle=e.angle%360;var t=Math.round(e.angle);return new s({type:e.type,rotateAngle:t})},i.genBlurOp=function(e){o.verifyOptions(e,"radius sigma","nos::genBlurOp"),o.verifyParamType("radius",e.radius,"number","nos::genBlurOp"),o.verifyParamMin("radius",e.radius,1,"nos::genBlurOp"),o.verifyParamMax("radius",e.radius,50,"nos::genBlurOp"),o.verifyParamType("sigma",e.sigma,"number","nos::genBlurOp"),o.verifyParamMin("sigma",e.sigma,0,"nos::genBlurOp");var t=Math.round(e.radius),n=Math.round(e.sigma);return new s({type:e.type,blurRadius:t,blurSigma:n})},i.genCropOp=function(e){o.verifyOptions(e,"x y width height","nos::genCropOp"),o.verifyParamType("x",e.x,"number","nos::genCropOp"),o.verifyParamMin("x",e.x,0,"nos::genCropOp"),o.verifyParamType("y",e.y,"number","nos::genCropOp"),o.verifyParamMin("y",e.y,0,"nos::genCropOp"),o.verifyParamType("width",e.width,"number","nos::genCropOp"),o.verifyParamMin("width",e.width,0,"nos::genCropOp"),o.verifyParamType("height",e.height,"number","nos::genCropOp"),o.verifyParamMin("height",e.height,0,"nos::genCropOp");var t=Math.round(e.x),n=Math.round(e.y),r=Math.round(e.width),i=Math.round(e.height);return new s({type:e.type,cropX:t,cropY:n,cropWidth:r,cropHeight:i})},i.genThumbnailOp=(r={cover:"z",contain:"x",crop:"y"},function(e){o.verifyOptions(e,"mode","nos::genThumbnailOp"),o.verifyParamValid("mode",e.mode,Object.keys(r),"nos::genThumbnailOp"),"contain"===e.mode?o.verifyParamAtLeastPresentOne(e,"width height","nos::genThumbnailOp"):o.verifyOptions(e,"width height","nos::genThumbnailOp"),o.undef(e.width)&&(e.width=0),o.undef(e.height)&&(e.height=0),o.verifyParamType("width",e.width,"number","nos::genThumbnailOp"),o.verifyParamMin("width",e.width,0,"nos::genThumbnailOp"),o.verifyParamType("height",e.height,"number","nos::genThumbnailOp"),o.verifyParamMin("height",e.height,0,"nos::genThumbnailOp");var t=Math.round(e.width),n=Math.round(e.height),i=new s({type:e.type,thumbnailMode:r[e.mode],thumbnailWidth:t,thumbnailHeight:n});if("crop"===e.mode&&o.notundef(e.axis)){o.undef(e.axis.x)&&(e.axis.x=5),o.undef(e.axis.y)&&(e.axis.y=5),o.verifyParamMin("axis.x",e.axis.x,0,"nos::genThumbnailOp"),o.verifyParamMax("axis.x",e.axis.x,10,"nos::genThumbnailOp"),o.verifyParamMin("axis.y",e.axis.y,0,"nos::genThumbnailOp"),o.verifyParamMax("axis.y",e.axis.y,10,"nos::genThumbnailOp");var a=Math.round(e.axis.x),c=Math.round(e.axis.y);i.thumbnailAxisX=a,i.thumbnailAxisY=c}return o.notundef(e.enlarge)&&(o.verifyParamType("enlarge",e.enlarge,"boolean","nos::genThumbnailOp"),e.enlarge&&(i.thumbnailEnlarge=1)),i.thumbnailToStatic=this.options.thumbnailToStatic?1:0,i}),i.getNosOriginUrl=function(e){o.verifyOptions(e,"safeShortUrl",!0,"","nos::getNosOriginUrl"),o.verifyParamType("safeShortUrl",e.safeShortUrl,"string","nos::getNosOriginUrl"),/^http(s)?:/.test(e.safeShortUrl)&&~e.safeShortUrl.indexOf("im_url=1")?(this.processCallback(e),this.sendCmd("getNosOriginUrl",{nosFileUrlTag:{safeUrl:e.safeShortUrl}},e.callback)):e.done(new c("参数 “safeShortUrl” 内容非文件安全短链",{callFunc:"nos: getNosOriginUrl"}),e)}},function(e,t,n){"use strict";var r=n(5),o=n(0),i=n(27),s=n(54).upload,a=n(54).chunkUpload,c=n(54).abort,u=o.supportFormData;function l(e){var t=this;t.options=o.copy(e),o.verifyOptions(e,"url fileName"),o.verifyParamPresentJustOne(e,"blob fileInput"),o.verifyCallback(e,"beginupload uploadprogress uploaddone"),e.fileInput&&(e.fileInput=o.verifyFileInput(e.fileInput)),e.type&&o.verifyFileType(e.type),e.timeout?o.verifyParamType("timeout",e.timeout,"number"):e.timeout=6e5,o.verifyFileUploadCallback(e),e.data={},e.params&&o.merge(e.data,e.params);var n=e.fileName,c=e.fileInput;if(u){if(c){var l=e.type?o.filterFiles(c.files,e.type):[].slice.call(c.files,0);if(!l||!l.length)return void e.uploaddone(i.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保文件选择节点的文件不为空, 并且请确保选择了"+e.type+"类型的文件"));e.data[n]=l[0];var m=c.files[0].size}else if(e.blob){if(e.data[n]=e.blob,"file"!==e.type&&e.blob.type&&-1===e.blob.type.indexOf(e.type))return void e.uploaddone(i.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保选择了"+e.type+"类型的文件"));m=e.blob.size}if(e.maxSize&&m>e.maxSize)return void e.uploaddone(i.newFileTooLargeError("上传文件大小超过"+e.maxSize+"限制"));if(!e.commonUpload)return m>r.chunkMaxSize?void e.uploaddone(i.newFileTooLargeError("直传文件大小超过"+r.chunkMaxSize+"限制")):void(t.sn=a(e,n,t,-1));if(m>r.commonMaxSize)return void e.uploaddone(i.newFileTooLargeError("普通上传文件大小超过"+r.commonMaxSize+"限制"))}else o.dataset(c,"name",n),e.data.input=c;var p={data:e.data,onaftersend:function(){e.beginupload(t)},onuploading:function(t){var n=Math.floor(1e4*t.loaded/t.total)/100,r={docId:e.docId,total:t.total,loaded:t.loaded,percentage:n,percentageText:n+"%"};e.fileInput&&(r.fileInput=e.fileInput),e.blob&&(r.blob=e.blob),e.uploadprogress(r)},onload:function(n){n.docId=e.docId,n.Error?t.onError(n):e.uploaddone(null,n)},onerror:function(n){try{if(n.result)var r=JSON.parse(n.result);else r=n;t.onError(r)}catch(r){console.log("error: ignore error if could not parse obj.result",r),e.uploaddone(new i(n.message,n.code),t.options)}}};u||(p.mode="iframe"),p.putFileAtEnd=!0,t.sn=s(e.url,p)}l.prototype.onError=function(e){var t,n,r,o=this.options;n=(t=(e=e||{}).Error||e||{}).Code||t.code||"unknown",r=t.Message||t.message||"未知错误",o.uploaddone(new i(n+"("+r+")",n))},l.prototype.abort=function(){c(this.sn)},e.exports=l},function(e,t,n){var r,o,i;!function(n,s){"use strict";o=[],void 0===(i="function"==typeof(r=function(e){return function(t){t=t||{},function(){t.arrayAccessForm=t.arrayAccessForm||"none",t.emptyNodeForm=t.emptyNodeForm||"text",t.jsAttributeFilter=t.jsAttributeFilter,t.jsAttributeConverter=t.jsAttributeConverter,t.attributeConverters=t.attributeConverters||[],t.datetimeAccessFormPaths=t.datetimeAccessFormPaths||[],t.arrayAccessFormPaths=t.arrayAccessFormPaths||[],void 0===t.enableToStringFunc&&(t.enableToStringFunc=!0);void 0===t.skipEmptyTextNodesForObj&&(t.skipEmptyTextNodesForObj=!0);void 0===t.stripWhitespaces&&(t.stripWhitespaces=!0);void 0===t.useDoubleQuotes&&(t.useDoubleQuotes=!0);void 0===t.ignoreRoot&&(t.ignoreRoot=!1);void 0===t.escapeMode&&(t.escapeMode=!0);void 0===t.attributePrefix&&(t.attributePrefix="_");void 0===t.selfClosingElements&&(t.selfClosingElements=!0);void 0===t.keepCData&&(t.keepCData=!1);void 0===t.jsDateUTC&&(t.jsDateUTC=!1)}(),function(){function e(e){var t=String(e);return 1===t.length&&(t="0"+t),t}"function"!=typeof String.prototype.trim&&(String.prototype.trim=function(){return this.replace(/^\s+|^\n+|(\s|\n)+$/g,"")});"function"!=typeof Date.prototype.toISOString&&(Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+e(this.getUTCMonth()+1)+"-"+e(this.getUTCDate())+"T"+e(this.getUTCHours())+":"+e(this.getUTCMinutes())+":"+e(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"})}();var n={ELEMENT_NODE:1,TEXT_NODE:3,CDATA_SECTION_NODE:4,COMMENT_NODE:8,DOCUMENT_NODE:9};function r(e){var t=e.localName;return null==t&&(t=e.baseName),null!=t&&""!==t||(t=e.nodeName),t}function o(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"):e}function i(e,n,r){switch(t.arrayAccessForm){case"property":e[n]instanceof Array?e[n+"_asArray"]=e[n]:e[n+"_asArray"]=[e[n]]}if(!(e[n]instanceof Array)&&t.arrayAccessFormPaths.length>0){for(var o=!1,i=0;i<t.arrayAccessFormPaths.length;i++){var s=t.arrayAccessFormPaths[i];if("string"==typeof s){if(s===r){o=!0;break}}else if(s instanceof RegExp){if(s.test(r)){o=!0;break}}else if("function"==typeof s&&s(n,r)){o=!0;break}}o&&(e[n]=[e[n]])}}function s(e){var t=e.split(/[-T:+Z]/g),n=new Date(t[0],t[1]-1,t[2]),r=t[5].split(".");if(n.setHours(t[3],t[4],r[0]),r.length>1&&n.setMilliseconds(r[1]),t[6]&&t[7]){var o=60*t[6]+Number(t[7]),i=/\d\d-\d\d:\d\d$/.test(e)?"-":"+";o=0+("-"===i?-1*o:o),n.setMinutes(n.getMinutes()-o-n.getTimezoneOffset())}else-1!==e.indexOf("Z",e.length-1)&&(n=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds())));return n}function a(e,o){for(var a={__cnt:0},u=e.childNodes,l=0;l<u.length;l++){var m=u.item(l),p=r(m);m.nodeType!==n.COMMENT_NODE&&(a.__cnt++,null==a[p]?(a[p]=c(m,o+"."+p),i(a,p,o+"."+p)):(a[p]instanceof Array||(a[p]=[a[p]],i(a,p,o+"."+p)),a[p][a[p].length]=c(m,o+"."+p)))}for(var d=0;d<e.attributes.length;d++){var f=e.attributes.item(d);a.__cnt++;for(var g=f.value,y=0;y<t.attributeConverters.length;y++){var h=t.attributeConverters[y];h.test.call(null,f.name,f.value)&&(g=h.convert.call(null,f.name,f.value))}a[t.attributePrefix+f.name]=g}var v=e.prefix;return v&&(a.__cnt++,a.__prefix=v),a["#text"]&&(a.__text=a["#text"],a.__text instanceof Array&&(a.__text=a.__text.join("\n")),t.escapeMode&&(a.__text=a.__text.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&amp;/g,"&")),t.stripWhitespaces&&(a.__text=a.__text.trim()),delete a["#text"],"property"===t.arrayAccessForm&&delete a["#text_asArray"],a.__text=function(e,n,r){if(t.datetimeAccessFormPaths.length>0)for(var o=r.split(".#")[0],i=0;i<t.datetimeAccessFormPaths.length;i++){var a=t.datetimeAccessFormPaths[i];if("string"==typeof a){if(a===o)return s(e)}else if(a instanceof RegExp){if(a.test(o))return s(e)}else if("function"==typeof a&&a(o))return s(e)}return e}(a.__text,0,o+".#text")),a.hasOwnProperty("#cdata-section")&&(a.__cdata=a["#cdata-section"],delete a["#cdata-section"],"property"===t.arrayAccessForm&&delete a["#cdata-section_asArray"]),1===a.__cnt&&a.__text?a=a.__text:0===a.__cnt&&"text"===t.emptyNodeForm?a="":a.__cnt>1&&void 0!==a.__text&&t.skipEmptyTextNodesForObj&&(t.stripWhitespaces&&""===a.__text||""===a.__text.trim())&&delete a.__text,delete a.__cnt,t.keepCData||a.hasOwnProperty("__text")||!a.hasOwnProperty("__cdata")?(t.enableToStringFunc&&(a.__text||a.__cdata)&&(a.toString=function(){return(this.__text?this.__text:"")+(this.__cdata?this.__cdata:"")}),a):a.__cdata?a.__cdata:""}function c(e,o){return e.nodeType===n.DOCUMENT_NODE?function(e){for(var o={},i=e.childNodes,s=0;s<i.length;s++){var a=i.item(s);if(a.nodeType===n.ELEMENT_NODE){var u=r(a);t.ignoreRoot?o=c(a,u):o[u]=c(a,u)}}return o}(e):e.nodeType===n.ELEMENT_NODE?a(e,o):e.nodeType===n.TEXT_NODE||e.nodeType===n.CDATA_SECTION_NODE?e.nodeValue:null}function u(e,n,r,i){var s="<"+(e&&e.__prefix?e.__prefix+":":"")+n;if(r)for(var a=0;a<r.length;a++){var c=r[a],u=e[c];t.escapeMode&&(u=o(u)),s+=" "+c.substr(t.attributePrefix.length)+"=",t.useDoubleQuotes?s+='"'+u+'"':s+="'"+u+"'"}return s+=i?" />":">"}function l(e,t){return"</"+(e&&e.__prefix?e.__prefix+":":"")+t+">"}function m(e,n){return"property"===t.arrayAccessForm&&(r=n.toString(),o="_asArray",-1!==r.indexOf(o,r.length-o.length))||0===n.toString().indexOf(t.attributePrefix)||0===n.toString().indexOf("__")||e[n]instanceof Function;var r,o}function p(e){var t=0;if(e instanceof Object)for(var n in e)m(e,n)||t++;return t}function d(e){var n=[];if(e instanceof Object)for(var r in e)-1===r.toString().indexOf("__")&&0===r.toString().indexOf(t.attributePrefix)&&n.push(r);return n}function f(e){var n="";return e instanceof Object?n+=function(e){var n="";e.__cdata&&(n+="<![CDATA["+e.__cdata+"]]>");e.__text&&(t.escapeMode?n+=o(e.__text):n+=e.__text);return n}(e):null!==e&&(t.escapeMode?n+=o(e):n+=e),n}function g(e,n,r){var o="";if(t.jsAttributeFilter&&t.jsAttributeFilter.call(null,n,e))return o;if(t.jsAttributeConverter&&(e=t.jsAttributeConverter.call(null,n,e)),null!=e&&""!==e||!t.selfClosingElements)if("object"==typeof e)if("[object Array]"===Object.prototype.toString.call(e))o+=function(e,t,n){var r="";if(0===e.length)r+=u(e,t,n,!0);else for(var o=0;o<e.length;o++)r+=g(e[o],t,d(e[o]));return r}(e,n,r);else if(e instanceof Date)o+=u(e,n,r,!1),o+=t.jsDateUTC?e.toUTCString():e.toISOString(),o+=l(e,n);else{var i=p(e);i>0||e.__text||e.__cdata?(o+=u(e,n,r,!1),o+=y(e),o+=l(e,n)):t.selfClosingElements?o+=u(e,n,r,!0):(o+=u(e,n,r,!1),o+=l(e,n))}else o+=u(e,n,r,!1),o+=f(e),o+=l(e,n);else o+=u(e,n,r,!0);return o}function y(e){var t="",n=p(e);if(n>0)for(var r in e)if(!m(e,r)){var o=e[r],i=d(o);t+=g(o,r,i)}return t+=f(e)}function h(t){if(void 0===t)return null;if("string"!=typeof t)return null;var n=null,r=null;if(e)n=new e,r=n.parseFromString(t,"text/xml");else if(window&&window.DOMParser){n=new window.DOMParser;var o=null,i=window.ActiveXObject||"ActiveXObject"in window;if(!i)try{o=n.parseFromString("INVALID","text/xml").childNodes[0].namespaceURI}catch(e){o=null}try{r=n.parseFromString(t,"text/xml"),null!==o&&r.getElementsByTagNameNS(o,"parsererror").length>0&&(r=null)}catch(e){r=null}}else 0===t.indexOf("<?")&&(t=t.substr(t.indexOf("?>")+2)),(r=new ActiveXObject("Microsoft.XMLDOM")).async="false",r.loadXML(t);return r}this.asArray=function(e){return null==e?[]:e instanceof Array?e:[e]},this.toXmlDateTime=function(e){return e instanceof Date?e.toISOString():"number"==typeof e?new Date(e).toISOString():null},this.asDateTime=function(e){return"string"==typeof e?s(e):e},this.xml2dom=function(e){return h(e)},this.dom2js=function(e){return c(e,null)},this.js2dom=function(e){var t=this.js2xml(e);return h(t)},this.xml2js=function(e){var t=h(e);return null!=t?this.dom2js(t):null},this.js2xml=function(e){return y(e)},this.getVersion=function(){return"3.1.1"}}})?r.apply(t,o):r)||(e.exports=i)}()},function(e,t,n){"use strict";var r,o=n(11),i=(r=o)&&r.__esModule?r:{default:r};var s=n(53).fn,a=n(0),c=n(328),u=n(27),l=n(5),m=n(187),p=n(327),d=n(186);s.sendText=function(e){return this.processCallback(e),e.msg=new this.message.TextMessage(e),this.sendMsg(e)},s.previewFile=function(e){if(a.verifyOptions(e,"done","msg::previewFile"),e.type||(e.type="file"),a.verifyParamPresentJustOne(e,"dataURL blob fileInput filePath wxFilePath fileObject","msg::previewFile"),a.exist(e.maxSize)&&a.verifyParamType("maxSize",e.maxSize,"number","api::previewFile"),a.exist(e.commonUpload)&&a.verifyParamType("commonUpload",e.commonUpload,"boolean","api::previewFile"),e.nosSurvivalTime?(a.verifyParamType("nosSurvivalTime",e.nosSurvivalTime,"number","api::Base.getInstance"),a.verifyParamMin("nosSurvivalTime",e.nosSurvivalTime,86400,"api::Base.getInstance")):e.nosSurvivalTime=this.nosSurvivalTime,e.filePath=e.filePath||e.wxFilePath,delete e.wxFilePath,e.dataURL)e.blob=d.fromDataURL(e.dataURL);else if(e.blob);else if(e.fileInput){if(e.fileInput=a.verifyFileInput(e.fileInput,"msg::previewFile"),e.fileInput.files){if(!e.fileInput.files.length)return void e.done(u.newNoFileError("请选择"+e.type+"文件",{callFunc:"msg::previewFile",fileInput:e.fileInput}),e);e.fileSize=e.fileInput.files[0].size}e.fileInputName=a.getFileName(e.fileInput)}this.processCallback(e);var t=JSON.stringify(m.genResponseBody(e.type)||{}).replace(/"/gi,'\\"'),n=null,r=e.transcode?"getNosTokenTrans":"getNosToken";if(e.transcode){a.verifyOptions(e,"fileInput","msg::previewFile");var o=a.getFileInfo(e.fileInput);n={transToken:{name:o.name,type:o.transcodeType,transType:"png"===e.transcode?11:10,size:o.size,body:t}}}else n=t;this[r]({responseBody:n,nosToken:{nosScene:e.nosScene||this.nosScene,nosSurvivalTime:e.nosSurvivalTime},callback:function(t,n){t?e.done(t):(e.transcode?(e.nosToken=n.nosToken,e.docId=n.docId):e.nosToken=n,this._doPreviewFile(e))}.bind(this)})},s._doPreviewFile=function(e){var t,n=this,r=e.uploaddone,o=l.genUploadUrl(e.nosToken.bucket),s=l.chunkUploadUrl,c=l.lbsUrls;e.commonUpload||!s||l.isWeixinApp||l.isNodejs||l.isRN?(e.commonUpload=!0,t=o):(this.logger.info("use chunkUrl: ",s,c),t=s,e.lbsUrls=c,n.edgeList?e.edgeList=n.edgeList:e.updateNosEdgeList=function(e){n.edgeList=e});var d=this.assembleUploadParams(e.nosToken);function f(t,o,i){if(e.uploaddone=r,t)e.done(t,e.callback.options);else{if(o=m.parseResponse(o,n.options.exifOrientation),i||(o.url=l.genDownloadUrl(e.nosToken,d.Object),e.nosToken.shortUrl&&(o._url_safe=e.nosToken.shortUrl)),a.exist(e.fileInputName))o.name=e.fileInputName;else if(e.blob){var s=e.blob.name;if(o.name=s||"blob-"+o.md5,!s){var c=e.blob.type;o.ext=c.slice(c.lastIndexOf("/")+1)}}else e.filePath?o.name=e.filePath:e.fileObject&&(o.name=e.fileObject.fileName);if(!o.ext){var u=o.name.lastIndexOf(".");o.ext=-1===u?"unknown":o.name.slice(u+1)}o.size=o.size||0,e.done(null,a.copy(o))}}if(l.isWeixinApp)a.verifyOptions(e,"filePath","msg::_doPreviewFile"),n.fileQuickTransfer(e,f,function(){var r=wx.uploadFile({url:t,filePath:e.filePath,name:"file",formData:d,fail:function(e){f({code:"FAILED",msg:e}),n.protocol.logger.log("error:","api::msg:upload file failed",e)},success:function(e){if(200===e.statusCode)try{f(null,JSON.parse(e.data))}catch(t){n.protocol.logger.log("error:","parse wx upload file res error",t),f({code:"PARSE_WX_UPLOAD_FILE_RES_ERROR",str:e.data,msg:e.errMsg})}else f({code:e.statusCode,msg:e.errMsg})}});"function"==typeof e.uploadprogress&&r&&r.onProgressUpdate(function(t){e.uploadprogress({total:t.totalBytesExpectedToSend,loaded:t.totalBytesSent,percentage:t.progress,percentageText:t.progress+"%"})})});else if(l.isNodejs){var g={url:t,name:"file",formData:d,success:function(e){if(200===e.statusCode)try{f(null,JSON.parse(e.data))}catch(t){n.protocol.logger.log("error:","parse nodejs upload file res error",t),f({code:"PARSE_NODEJS_UPLOAD_FILE_RES_ERROR",str:e.data,msg:e.errMsg})}else f({code:e.statusCode,msg:e.errMsg})},fail:function(e){f({code:"FAILED",msg:e}),n.protocol.logger.log("error:","api::msg:upload file failed",e)}};if(e.filePath)g.filePath=e.filePath;else{if("object"!==(0,i.default)(e.fileObject))throw new u("Nodejs上传fileObject参数类型应如 {fileName:..,fileData:..} ");g.fileData=e.fileObject.fileData}n.fileQuickTransfer(e,f,function(){p.uploadFile(g)})}else if(l.isRN){var y={url:t,name:"file",formData:d,filePath:e.filePath,success:function(e){if(e.ok&&200===e.status)try{e.md5=e.headers.map&&e.headers.map.etag&&e.headers.map.etag[0]||"UNKNOWN",f(null,e)}catch(t){n.protocol.logger.log("error:","parse React Native upload file res error",t),f({code:"PARSE_React_Native_UPLOAD_FILE_RES_ERROR",res:e})}else f({code:e.status,msg:e.statusText})},fail:function(e){f({code:"FAILED",msg:e}),n.protocol.logger.log("error:","api::msg:upload file failed",e)}};n.fileQuickTransfer(e,f,function(){p.uploadFile(y)})}else e.uploaddone=f,e.url=t,e.params=d,e.fileName="file",n.fileQuickTransfer(e,f,function(){return new p(e)})},s.fileQuickTransfer=function(e,t,n){var r=this;e=e||{},t instanceof Function||(t=function(){}),n instanceof Function||(n=function(){});var o=e.fastPass;if(o)try{o=JSON.parse(o),e.fastPass=o}catch(e){r.protocol.logger.error("快传参数解析失败")}var i=e.fileInputName||e.name||e.blob&&e.blob.name||"",s=e.fileSize||e.size||e.blob&&e.blob.size||0,a=o?((o.md5||e.digest||"")+"").trim():"",c=e.type||e.blob&&e.blob.type;if(a&&s>=l.threshold){var u=!0,m={name:i,md5:a,ext:i.slice(i.lastIndexOf(".")+1),type:c};switch(c){case"image":o&&o.w&&o.h?(m.w=o.w,m.h=o.h):(u=!1,r.protocol.logger.error("快传 image 文件缺少参数 w 或 h"));break;case"video":o&&o.w&&o.h&&o.dur?(m.w=o.w,m.h=o.h,m.dur=o.dur):(u=!1,r.protocol.logger.error("快传 video 文件缺少参数 w 或 h 或 dur"));break;case"audio":o&&o.dur?m.dur=o.dur:(u=!1,r.protocol.logger.error("快传 audio 文件缺少参数 dur"))}if(!u)return void n();var p={fileQuickTransfer:{md5:a}};return s&&(p.fileQuickTransfer.size=s),this.protocol.sendCmd("fileQuickTransfer",p,function(e,o,i){!e&&i&&i.fileQuickTransfer&&i.fileQuickTransfer.url||(r.protocol.logger.error("misc::fileQuickTransfer: not found",e,o,i),n()),i&&i.fileQuickTransfer&&i.fileQuickTransfer.threshold&&(l.threshold=i.fileQuickTransfer.threshold||0),i&&i.fileQuickTransfer&&i.fileQuickTransfer.url&&(m.size=s||i.fileQuickTransfer.size,m.url=i.fileQuickTransfer.url,i.fileQuickTransfer._url_safe&&(m._url_safe=i.fileQuickTransfer._url_safe),t(e,m,!0))})}n()},s.sendFile=function(e){if(e.type||(e.type="file"),a.verifyParamPresentJustOne(e,"dataURL blob fileInput file filePath wxFilePath fileObject","msg::sendFile"),a.exist(e.maxSize)&&a.verifyParamType("maxSize",e.maxSize,"number","api::previewFile"),a.exist(e.commonUpload)&&a.verifyParamType("commonUpload",e.commonUpload,"boolean","api::previewFile"),this.processCallback(e),e.filePath=e.filePath||e.wxFilePath,delete e.wxFilePath,e.dataURL)this._previewAndSendFile(e);else if(e.blob)this._previewAndSendFile(e);else if(e.fileInput){if(e.fileInput=a.verifyFileInput(e.fileInput,"msg::sendFile"),e.fileInput.files&&!e.fileInput.files.length)return void e.done(u.newNoFileError("请选择"+e.type+"文件",{callFunc:"msg::sendFile",fileInput:e.fileInput}),e.callback.options);this._previewAndSendFile(e)}else if(e.filePath||e.fileObject)this._previewAndSendFile(e);else if(e.file){var t,n=e.file._url_safe;return n&&(t=e.file.url,e.file.url=n,delete e.file._url_safe),e.msg=new this.message.FileMessage(e),this.sendMsg(e,t)}},s._previewAndSendFile=function(e){var t=this;a.verifyCallback(e,"uploaddone beforesend","msg::_previewAndSendFile");var n=e.done;e.done=function(r,o){if(e.done=n,r)e.uploaddone(r,e.callback.options),e.done(r,e.callback.options);else{if(/chatroom/.test(e.scene))return;var i;e.uploaddone(null,a.copy(o));var s=o._url_safe;s&&(i=o.url,o.url=s,delete o._url_safe),e.file=o,e.msg=new t.message.FileMessage(e),e.beforesend(t.sendMsg(e,i))}},t.previewFile(e)},s.assembleUploadParams=function(e){return e?{Object:decodeURIComponent(e.objectName),"x-nos-token":e.token,"x-nos-entity-type":"json"}:null},s.deleteFile=function(e){a.verifyParamPresentJustOne(e,"docId","msg::deleteFile"),this.removeFile({docId:e.docId,callback:function(t,n){t?e.error&&e.error(t,n):e.success&&e.success(n)}})},s.getFile=function(e){a.verifyParamPresentJustOne(e,"docId","msg::getFile"),this.fetchFile({docId:e.docId,callback:function(t,n){t?e.error&&e.error(t,n):e.success&&e.success(n.info)}})},s.getFileList=function(e){var t=e.fromDocId,n=void 0===t?"":t,r=e.limit,o=void 0===r?10:r,i={limit:o};n&&(i.fromDocId=n),this.fetchFileList({fileListParam:i,callback:function(t,n){t?(o>30&&(t.message=t.message+"::文档条数超过限制:30"),e.error&&e.error(t,n)):e.success&&e.success(n)}})},s.sendGeo=function(e){return this.processCallback(e),e.msg=new this.message.GeoMessage(e),this.sendMsg(e)},s.sendTipMsg=function(e){return this.processCallback(e),e.msg=new this.message.TipMessage(e),this.sendMsg(e)},s.sendCustomMsg=function(e){return this.processCallback(e),e.msg=new this.message.CustomMessage(e),this.sendMsg(e)},s.sendRobotMsg=function(e){return this.processCallback(e),e.msg=new this.message.RobotMessage(e),this.sendMsg(e)},s.sendMsg=function(e,t){var n,r=this.protocol,o=e.msg,i={},s=!!e.isLocal;if(s&&(e.time&&(o.time=e.time),e.idClient&&(o.idClient=e.idClient),e.localFrom&&(n=e.localFrom+"")),e.resend&&("out"!==e.flow||"fail"!==e.status))return a.onError("只能重发发送失败的消息");e.callback.options.idClient=o.idClient,this.beforeSendMsg(e,i);var c=e.rtnMsg=this.formatReturnMsg(o,n);return t&&!this.options.keepNosSafeUrl&&c.file&&(c.file._url_safe=c.file.url,c.file.url=t,"audio"===c.type&&(c.file.mp3Url=t+(~t.indexOf("?")?"&":"?")+"audioTrans&type=mp3")),c.hasOwnProperty("chatroomId")&&!c.chatroomId?a.onError("聊天室未连接"):(s&&(c.status="success",c.isLocal=!0),r.storeSendMsg&&(i.promise=r.storeSendMsg(c)),e.cbaop=function(e){if(e)return 7101===e.code&&(c.isInBlackList=!0),"server"!==e.from?(c.status="fail",r.updateSendMsgError&&r.updateSendMsgError(c),c):void 0},s||(t&&!this.options.keepNosSafeUrl&&e.callback&&(e.callback.originUrl=t),i.msg=o,this.sendCmd(e.cmd,i,e.callback)),this.afterSendMsg(e),s&&setTimeout(function(){c=a.simpleClone(c),e.done(null,c)},0),a.copy(c))},s.beforeSendMsg=function(){},s.afterSendMsg=function(){},s.formatReturnMsg=function(e,t){return e=a.copy(e),this.protocol.completeMsg(e),e.status="sending",t&&(e.from=t),e=this.message.reverse(e)},s.resendMsg=function(e){return a.verifyOptions(e,"msg","msg::resendMsg"),this.trimMsgFlag(e),e.resend=!0,this._sendMsgByType(e)},s.forwardMsg=function(e){return a.verifyOptions(e,"msg","msg::forwardMsg"),this.trimMsgFlag(e),this.beforeForwardMsg(e),e.forward=!0,e.msg.idClient=a.guid(),this._sendMsgByType(e)},s.trimMsgFlag=function(e){e&&e.msg&&(e.msg=a.copy(e.msg),delete e.msg.resend,delete e.msg.forward)},s.beforeForwardMsg=function(){},s._sendMsgByType=function(e){switch(a.verifyOptions(e,"msg","msg::_sendMsgByType"),a.verifyParamValid("msg.type",e.msg.type,this.message.validTypes,"msg::_sendMsgByType"),a.merge(e,e.msg),e.type){case"text":return this.sendText(e);case"image":case"audio":case"video":case"file":return this.sendFile(e);case"geo":return this.sendGeo(e);case"custom":return this.sendCustomMsg(e);case"tip":return this.sendTipMsg(e);default:throw new u("不能发送类型为 "+e.type+" 的消息")}},s.parseRobotTemplate=function(e){if(/<template[^>\/]+\/>/.test(e))return{raw:e,json:[{type:"text",name:"",text:""}]};if(!/<template[^>\/]+>/.test(e))return{raw:e,json:[{type:"text",name:"",text:e}]};var t=new c({escapeMode:!1});e=e.replace(/<template [^>]+>/,"<template>");var n=t.xml2js(e);n=n.template.LinearLayout,Array.isArray(n)||(n=[n]);var r=[];return n=n.forEach(function(e){e.image&&(r=r.concat(i(e))),e.text&&(r=r.concat(o(e))),e.link&&(r=r.concat(function(e){if(e.link){var t=e.link;Array.isArray(t)||(t=[t]),t=t.map(function(e){return e.image&&(e.image=i(e)),e.text&&(e.text=o(e)),"url"===e._type?(e.type="url",e.style=e._style||"",e.target=e._target,delete e._target,delete e._style):"block"===e._type&&(e.type="block",e.style=e._style||"",e.params=e._params||"",e.target=e._target,delete e._params,delete e._target,delete e._style),delete e._type,e}),e.link=t}return e.link}(e)))}),{raw:e,json:r};function o(e){return Array.isArray(e.text)||(e.text=[e.text]),e.text=e.text.map(function(e){return{type:"text",name:e._name,text:e.__text}}),e.text}function i(e){return Array.isArray(e.image)||(e.image=[e.image]),e.image=e.image.map(function(e){return{type:"image",name:e._name,url:e._url}}),e.image}}},function(e,t,n){"use strict";var r=n(53).fn;r.isConnected=function(){return!!this.protocol&&this.protocol.isConnected()},r.connect=function(){this.protocol.appLogin=0,this.protocol.connect(!0)},r.disconnect=function(e){e=e||{},this.protocol.disconnect(e.done)}},function(e,t,n){"use strict";var r=n(19),o=n(0),i=n(5),s=n(39);function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};o.merge(this,{options:e,debug:!1,api:"log",style:"color:blue;",log:o.emptyFunc,info:o.emptyFunc,warn:o.emptyFunc,error:o.emptyFunc}),this.prefix=e.prefix||"",this.setDebug(e.debug)}var c=a.prototype,u=["Chrome","Safari","Firefox"];c.setDebug=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this;if(t.debug=e,e.style&&(t.style=e.style),o.exist(console))if(t.debug){var n=i.isRN?m:function(){},s=console;t.debug=function(){var e=t.formatArgs(arguments);-1!==u.indexOf(r.name)&&o.isString(e[0])&&(e[0]="%c"+e[0],e.splice(1,0,t.style)),t._log("debug",e)},t.log=function(){var e=t.formatArgs(arguments);-1!==u.indexOf(r.name)&&o.isString(e[0])&&(e[0]="%c"+e[0],e.splice(1,0,t.style)),t._log("log",e)},t.info=function(){var e=t.formatArgs(arguments);-1!==u.indexOf(r.name)&&o.isString(e[0])&&(e[0]="%c"+e[0],e.splice(1,0,t.style)),t._log("info",e)},t.warn=function(){var e=t.formatArgs(arguments);-1!==u.indexOf(r.name)&&o.isString(e[0])&&(e[0]="%c"+e[0],e.splice(1,0,t.style)),t._log("warn",e)},t.error=function(){var e=t.formatArgs(arguments);-1!==u.indexOf(r.name)&&o.isString(e[0])&&(e[0]="%c"+e[0],e.splice(1,0,t.style)),t._log("error",e)},t._log=function(e,r){/error|warn|info/.test(e)&&n(JSON.stringify(r)+"\r\n");var i=t.options.logFunc,a=null;if(i&&(i[e]&&(a=i[e]),o.isFunction(a)))a.apply(i,r);else if(s[e])try{s[e].apply?t.chrome(e,r):t.ie(e,r)}catch(e){}},t.chrome=function(e,n){-1!==u.indexOf(r.name)?s[e].apply(s,n):t.ie(e,n)},t.ie=function(e,t){t.forEach(function(t){s[e](JSON.stringify(t,null,4))})}}else i.isRN&&(t.info=function(){var e=t.formatArgs(arguments);m(JSON.stringify(e))},t.warn=function(){var e=t.formatArgs(arguments);m(JSON.stringify(e))},t.error=function(){var e=t.formatArgs(arguments);m(JSON.stringify(e))})},c.formatArgs=function(e){e=[].slice.call(e,0);var t=new Date,n="[NIM LOG "+(l(t.getMonth()+1)+"-"+l(t.getDate())+" "+l(t.getHours())+":"+l(t.getMinutes())+":"+l(t.getSeconds())+":"+l(t.getMilliseconds(),3))+" "+this.prefix.toUpperCase()+"]  ";return o.isString(e[0])?e[0]=n+e[0]:e.splice(0,0,n),e.forEach(function(t,n){(o.isArray(t)||o.isObject(t))&&(e[n]=o.simpleClone(t))}),e};var l=function(e,t){t=t||2;for(var n=""+e;n.length<t;)n="0"+n;return n};function m(e){if(i.isRN&&s.rnfs&&s.rnfs.writeFile&&s.rnfs.appendFile&&s.rnfs.DocumentDirectoryPath){var t=s.rnfs,n=void 0,r=t.size/2-256;t.nimPromise=t.nimPromise.then(function(){return n=o(t.nimIndex),t.exists(n)}).then(function(r){return r?t.appendFile(n,e):t.writeFile(n,e)}).then(function(){return t.stat(n)}).then(function(e){if(e.size>r)return t.nimIndex++,t.nimIndex>1&&(t.nimIndex=t.nimIndex%2),t.unlink(o(t.nimIndex)).catch(function(e){return Promise.resolve()})}).catch(function(e){console.error(e)})}function o(e){return s.rnfs.DocumentDirectoryPath+"/nimlog_"+e+".log"}}e.exports=a},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=n(137).fn,o=n(0);r.queueOffer=function(e){o.verifyOptions(e,"elementKey elementValue","msg::queueOffer"),e.transient?e.transient=!0:e.transient=!1,this.processCallback(e),this.sendCmd("queueOffer",e,e.callback)},r.queuePoll=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.elementKey=e.elementKey||"",this.processCallback(e),this.sendCmd("queuePoll",e,e.callback)},r.queueList=function(e){this.processCallback(e),this.sendCmd("queueList",e,e.callback)},r.peak=function(e){this.processCallback(e),this.sendCmd("peak",e,e.callback)},r.queueDrop=function(e){this.processCallback(e),this.sendCmd("queueDrop",e,e.callback)},r.queueChange=function(e){o.verifyOptions(e,"elementMap","msg::queueOffer"),e.needNotify?(e.needNotify=!0,o.verifyOptions(e,"notifyExt","msg::queueOffer")):e.needNotify=!1,this.processCallback(e),this.sendCmd("queueChange",e,e.callback)}},function(e,t,n){"use strict";var r=n(0),o=r.undef,i=r.verifyOptions,s=r.verifyParamType,a=n(202),c=n(137).fn;c.updateMyChatroomMemberInfo=function(e){i(e,"member needNotify","member::updateMyChatroomMemberInfo"),s("needNotify",e.needNotify,"boolean","member::updateMyChatroomMemberInfo"),e.needSave=e.needSave||!1,s("needSave",e.needSave,"boolean","member::updateMyChatroomMemberInfo"),this.processCustom(e),this.processCallback(e),e.chatroomMember=new a(e.member),this.sendCmd("updateMyChatroomMemberInfo",e)},c.getChatroomMembers=function(e){i(e,"guest","member::getChatroomMembers"),s("guest",e.guest,"boolean","member::getChatroomMembers"),o(e.time)?e.time=0:s("time",e.time,"number","member::getChatroomMembers"),o(e.limit)?e.limit=100:s("limit",e.limit,"number","member::getChatroomMembers"),this.processCallback(e),e.guest?e.type=!1===e.desc?3:1:e.type=e.onlyOnline?2:0,this.sendCmd("getChatroomMembers",e)},c.getChatroomMembersInfo=function(e){i(e,"accounts","member::getChatroomMembersInfo"),s("accounts",e.accounts,"array","member::getChatroomMembersInfo"),this.processCallback(e),this.sendCmd("getChatroomMembersInfo",e)},c.markChatroomIdentity=function(e){i(e,"identity","member::markChatroomIdentity"),e.type={manager:1,common:2,black:-1,mute:-2}[e.identity],delete e.identity,isNaN(e.type)?i(e,"identity",'member::markChatroomIdentity. The valid value of the identity is "manager" or "common" or "black" or "mute".'):this.markChatroomMember(e)},c.markChatroomManager=function(e){e.type=1,this.markChatroomMember(e)},c.markChatroomCommonMember=function(e){e.type=2,this.markChatroomMember(e)},c.markChatroomBlacklist=function(e){e.type=-1,this.markChatroomMember(e)},c.markChatroomGaglist=function(e){e.type=-2,this.markChatroomMember(e)},c.markChatroomMember=function(e){i(e,"account type isAdd","member::markChatroomMember"),s("isAdd",e.isAdd,"boolean","member::markChatroomMember"),o(e.level)?e.level=0:s("level",e.level,"number","member::markChatroomMember");this.processCustom(e),this.processCallback(e),this.sendCmd("markChatroomMember",e)},c.kickChatroomMember=function(e){i(e,"account","member::kickChatroomMember"),this.processCustom(e),this.processCallback(e),this.sendCmd("kickChatroomMember",e)},c.updateChatroomMemberTempMute=function(e){i(e,"account duration needNotify","member::updateChatroomMemberTempMute"),s("duration",e.duration,"number","member::updateChatroomMemberTempMute"),s("needNotify",e.needNotify,"boolean","member::updateChatroomMemberTempMute"),this.processCustom(e),this.processCallback(e),this.sendCmd("updateChatroomMemberTempMute",e)},c.getRobotList=function(e){o(e.timetag)&&(e.timetag=0),this.processCallback(e),this.sendCmd("syncRobot",e)}},function(e,t,n){"use strict";var r=n(0),o=r.undef,i=n(137).fn;i.beforeSendMsg=function(e){e.cmd="sendMsg"};var s={text:0,image:1,audio:2,video:3,geo:4,notification:5,file:6,tip:10,robot:11,custom:100};i.getHistoryMsgs=function(e){r.verifyOptions(e),o(e.timetag)?e.timetag=0:r.verifyParamType("timetag",e.timetag,"number","msg::getHistoryMsgs"),o(e.limit)?e.limit=100:r.verifyParamType("limit",e.limit,"number","msg::getHistoryMsgs"),o(e.reverse)?e.reverse=!1:r.verifyParamType("reverse",e.reverse,"boolean","msg::getHistoryMsgs"),o(e.msgTypes)?e.msgTypes=[]:Array.isArray(e.msgTypes)?(e.msgTypes=e.msgTypes.map(function(e){return s[e]}),e.msgTypes=e.msgTypes.filter(function(e){return"number"==typeof e})):"number"==typeof s[e.msgTypes]?e.msgTypes=[s[e.msgTypes]]:e.msgTypes=[];this.processCallback(e),this.sendCmd("getHistoryMsgs",e,function(t,n,r){Array.isArray(r)&&(r=r.map(function(e){return s[e.type]&&(e.type=s[e.type]),e})),e.callback(t,n,r)})}},function(e,t,n){"use strict";var r=n(79),o=n(0),i={welcome:"00",text:"01",link:"03"},s={"01":"text","02":"image","03":"answer",11:"template"};function a(e){o.verifyOptions(e,"content","msg::RobotMessage");var t=e.content;switch(t.type){case"welcome":o.undef(e.body)&&(this.body="欢迎消息");break;case"text":o.verifyOptions(t,"content","msg::RobotMessage"),o.undef(e.body)&&(this.body=t.content);break;case"link":o.verifyOptions(t,"target","msg::RobotMessage")}t.type&&(t.type=i[t.type]),t={param:t,robotAccid:e.robotAccid},this.attach=JSON.stringify(t),e.type="robot",r.call(this,e)}a.prototype=Object.create(r.prototype),a.reverse=function(e){var t=r.reverse(e);if("robot"===t.type){var n=JSON.parse(e.attach);if(n.param&&(n.param.type=s[n.param.type]||"unknown"),n.robotMsg){var i=(n=o.merge(n,n.robotMsg)).message;"bot"===n.flag?n.message=i.map(function(e){return e.type=s[e.type]||"unknown",e}):n.flag,delete n.robotMsg}t.content=n}return t},e.exports=a},function(e,t,n){"use strict";var r=n(79),o=n(0);function i(e){o.verifyOptions(e,"tip","msg::TipMessage"),e.type="tip",r.call(this,e),this.attach=e.tip}i.prototype=Object.create(r.prototype),i.reverse=function(e){var t=r.reverse(e);return t.tip=e.attach,t},e.exports=i},function(e,t,n){"use strict";var r=n(79),o=n(0);function i(e){o.verifyOptions(e,"content","msg::CustomMessage"),e.type="custom",r.call(this,e),"string"!=typeof e.content&&(e.content=JSON.stringify(e.content)),this.attach=e.content}i.prototype=Object.create(r.prototype),i.reverse=function(e){var t=r.reverse(e);return t.content=e.attach,t},e.exports=i},function(e,t,n){"use strict";var r=n(0).notundef,o=n(79),i={301:"memberEnter",302:"memberExit",303:"blackMember",304:"unblackMember",305:"gagMember",306:"ungagMember",307:"addManager",308:"removeManager",309:"addCommon",310:"removeCommon",311:"closeChatroom",312:"updateChatroom",313:"kickMember",314:"addTempMute",315:"removeTempMute",316:"updateMemberInfo",317:"updateQueue",318:"muteRoom",319:"unmuteRoom",320:"batchUpdateQueue"};function s(){}s.prototype=Object.create(o.prototype),s.reverse=function(e){var t=o.reverse(e);if(e.attach=e.attach?""+e.attach:"",e.attach){var n=JSON.parse(e.attach);if(t.attach={type:i[n.id]},r(n.data)){var s=n.data;if(r(s.operator)&&(t.attach.from=s.operator),r(s.opeNick)&&(t.attach.fromNick=s.opeNick),r(s.target)&&(t.attach.to=s.target),r(s.tarNick)&&(t.attach.toNick=s.tarNick),r(s.muteDuration)&&(t.attach.duration=parseInt(s.muteDuration,10)),"memberEnter"===t.attach.type&&(r(s.muted)?t.attach.gaged=1==+s.muted:t.attach.gaged=!1,r(s.tempMuted)?t.attach.tempMuted=1==+s.tempMuted:t.attach.tempMuted=!1,r(s.muteTtl)?t.attach.tempMuteDuration=+s.muteTtl:t.attach.tempMuteDuration=0),r(s.ext)&&(t.attach.custom=s.ext),r(s.queueChange)){var a=JSON.parse(s.queueChange);switch(a._e){case"OFFER":t.attach.queueChange={type:"OFFER",elementKey:a.key,elementValue:a.content};break;case"POLL":t.attach.queueChange={type:"POLL",elementKey:a.key,elementValue:a.content};break;case"DROP":t.attach.queueChange={type:"DROP"};break;case"PARTCLEAR":case"BATCH_UPDATE":t.attach.queueChange={type:a._e,elementKv:a.kvObject}}}}}else t.attach={};return t},e.exports=s},function(e,t,n){"use strict";var r=n(79),o=n(0);function i(e){e.type="geo",o.verifyOptions(e,"geo","msg::GeoMessage"),o.verifyOptions(e.geo,"lng lat title",!0,"geo.","msg::GeoMessage"),o.verifyParamType("geo.lng",e.geo.lng,"number","msg::GeoMessage"),o.verifyParamType("geo.lat",e.geo.lat,"number","msg::GeoMessage"),o.verifyParamType("geo.title",e.geo.title,"string","msg::GeoMessage"),r.call(this,e),this.attach=JSON.stringify(e.geo)}i.prototype=Object.create(r.prototype),i.reverse=function(e){var t=r.reverse(e);return e.attach=e.attach?""+e.attach:"",t.geo=e.attach?JSON.parse(e.attach):{},t},e.exports=i},function(e,t,n){"use strict";var r=n(136),o=n(0);function i(){}i.prototype=Object.create(r.prototype),i.verifyFile=function(e,t){o.verifyOptions(e,"dur w h",!0,"file.",t)},e.exports=i},function(e,t,n){"use strict";var r=n(136),o=n(0);function i(){}i.prototype=Object.create(r.prototype),i.verifyFile=function(e,t){o.verifyOptions(e,"dur",!0,"file.",t)},e.exports=i},function(e,t,n){"use strict";var r=n(0),o=n(136);function i(){}i.prototype=Object.create(o.prototype),i.verifyFile=function(e,t){r.verifyOptions(e,"w h",!0,"file.",t)},e.exports=i},function(e,t,n){"use strict";var r=n(79),o=n(0);function i(e){o.verifyOptions(e,"text","msg::TextMessage"),e.type="text",r.call(this,e),this.attach=e.text,this.body=""}i.prototype=Object.create(r.prototype),i.reverse=function(e){var t=r.reverse(e);return t.text=e.attach,t},e.exports=i},function(e,t,n){"use strict";var r=n(0),o=function(e){this.account=e.account},i=o.prototype,s=i.Message=n(79),a=i.TextMessage=n(415),c=i.FileMessage=n(136),u=i.GeoMessage=n(411),l=i.NotificationMessage=n(410),m=i.CustomMessage=n(409),p=i.TipMessage=n(408),d=i.RobotMessage=n(407);i.validTypes=s.validTypes,i.reverse=function(e){var t;switch(s.getType(e)){case"text":t=a.reverse(e);break;case"image":case"audio":case"video":case"file":t=c.reverse(e);break;case"geo":t=u.reverse(e);break;case"notification":t=l.reverse(e);break;case"custom":t=m.reverse(e);break;case"tip":t=p.reverse(e);break;case"robot":t=d.reverse(e);break;default:t=s.reverse(e)}return s.setExtra(t,this.account),t},i.reverseMsgs=function(e,t){var n,o,i=this;return e.map(function(e){return e=i.reverse(e),t&&((n=t.modifyObj)&&(e=r.merge(e,n)),o=t.mapper,r.isFunction(o)&&(e=o(e))),e})},e.exports=o},function(e,t,n){"use strict";var r=n(127).fn,o=n(202);r.onChatroomMembersInfo=r.onChatroomMembers=function(e){e.error||(e.obj.members=o.reverseMembers(e.content.members))},r.onMarkChatroomMember=function(e){e.error||(e.obj.member=o.reverse(e.content.chatroomMember))},r.onSyncRobot=function(e){!e.error&&this.options.onrobots?this.options.onrobots(null,e.content):this.ontions.onrobots(e.error,{})}},function(e,t,n){"use strict";var r=n(127).fn,o=n(0);r.completeMsg=function(e){e.chatroomId=this.chatroom&&this.chatroom.id,e.from=this.options.account,e.fromClientType="Web",e.time||(e.time=+new Date)},r.onMsg=function(e){var t=this.message.reverse(e.content.msg);this.checkMsgUnique(t)&&(this.msgBuffer.push(t),this.msgFlushTimer||this.startMsgFlushTimer())},r.startMsgFlushTimer=function(){var e=this,t=e.options;e.msgFlushTimer=setTimeout(function(){var n=e.msgBuffer.splice(0,t.msgBufferSize);e.options.onmsgs(n),e.msgBuffer.length?e.startMsgFlushTimer():delete e.msgFlushTimer},t.msgBufferInterval)},r.checkMsgUnique=o.genCheckUniqueFunc("idClient"),r.onSendMsg=function(e){var t=e.obj.msg;e.error?t.status="fail":(t=e.content.msg).status="success",t=this.message.reverse(t),e.obj=t},r.onHistoryMsgs=function(e){e.error||(e.obj||(e.obj={}),e.obj.msgs=this.message.reverseMsgs(e.content.msgs))}},function(e,t,n){"use strict";var r=n(127).fn,o=n(39),i=n(0);r.assembleLogin=function(){var e=this.options;this.sdkSession=this.genSessionKey();var t={appKey:e.appKey,account:e.account,deviceId:o.deviceId,chatroomId:e.chatroomId,session:this.sdkSession,appLogin:this.appLogin||0};return{type:1,login:t=i.merge(t,i.filterObj(e,"chatroomNick chatroomAvatar chatroomCustom chatroomEnterCustom isAnonymous")),imLogin:this.assembleIMLogin()}},r.afterLogin=function(e){var t=e.chatroom;this.chatroom=t,this.notifyLogin()},r.kickedReasons=["","chatroomClosed","managerKick","samePlatformKick","silentlyKick","blacked"],r.kickedMessages=["","聊天室关闭了","被房主或者管理员踢出","不允许同一个帐号在多个地方同时登录","悄悄被踢","被拉黑了"]},function(e,t,n){"use strict";n(127).fn.refreshSocketUrl=function(){this.socketUrlsBackup=this.socketUrlsBackup||[],this.socketUrls=this.socketUrlsBackup.slice(0),this.logger.info("link::refreshSocketUrl"),this.connectToUrl(this.getNextSocketUrl())}},function(e,t,n){"use strict";n(172);var r=n(137);n(203)(r),e.exports=r}])});