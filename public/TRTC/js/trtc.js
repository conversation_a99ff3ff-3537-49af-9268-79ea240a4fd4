!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).TRTC=t()}(this,function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=function(e){return e&&e.Math==Math&&e},r=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||Function("return this")(),i=function(e){try{return!!e()}catch(t){return!0}},o=!i(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),a={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,c={f:s&&!a.call({1:2},1)?function(e){var t=s(this,e);return!!t&&t.enumerable}:a},u=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},d={}.toString,l=function(e){return d.call(e).slice(8,-1)},p="".split,h=i(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return"String"==l(e)?p.call(e,""):Object(e)}:Object,f=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return h(f(e))},v=function(e){return"object"==typeof e?null!==e:"function"==typeof e},g=function(e,t){if(!v(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!v(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},_={}.hasOwnProperty,y=function(e,t){return _.call(e,t)},S=r.document,b=v(S)&&v(S.createElement),k=function(e){return b?S.createElement(e):{}},R=!o&&!i(function(){return 7!=Object.defineProperty(k("div"),"a",{get:function(){return 7}}).a}),w=Object.getOwnPropertyDescriptor,T={f:o?w:function(e,t){if(e=m(e),t=g(t,!0),R)try{return w(e,t)}catch(n){}if(y(e,t))return u(!c.f.call(e,t),e[t])}},E=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},C=Object.defineProperty,I={f:o?C:function(e,t,n){if(E(e),t=g(t,!0),E(n),R)try{return C(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},x=o?function(e,t,n){return I.f(e,t,u(1,n))}:function(e,t,n){return e[t]=n,e},P=function(e,t){try{x(r,e,t)}catch(n){r[e]=t}return t},A=r["__core-js_shared__"]||P("__core-js_shared__",{}),O=Function.toString;"function"!=typeof A.inspectSource&&(A.inspectSource=function(e){return O.call(e)});var D,N,M,L=A.inspectSource,U=r.WeakMap,V="function"==typeof U&&/native code/.test(L(U)),j=t(function(e){(e.exports=function(e,t){return A[e]||(A[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.1",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})}),F=0,B=Math.random(),G=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++F+B).toString(36)},W=j("keys"),H=function(e){return W[e]||(W[e]=G(e))},J={},z=r.WeakMap;if(V){var Q=new z,q=Q.get,K=Q.has,X=Q.set;D=function(e,t){return X.call(Q,e,t),t},N=function(e){return q.call(Q,e)||{}},M=function(e){return K.call(Q,e)}}else{var $=H("state");J[$]=!0,D=function(e,t){return x(e,$,t),t},N=function(e){return y(e,$)?e[$]:{}},M=function(e){return y(e,$)}}var Y,Z,ee={set:D,get:N,has:M,enforce:function(e){return M(e)?N(e):D(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=N(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},te=t(function(e){var t=ee.get,n=ee.enforce,i=String(String).split("String");(e.exports=function(e,t,o,a){var s=!!a&&!!a.unsafe,c=!!a&&!!a.enumerable,u=!!a&&!!a.noTargetGet;"function"==typeof o&&("string"!=typeof t||y(o,"name")||x(o,"name",t),n(o).source=i.join("string"==typeof t?t:"")),e!==r?(s?!u&&e[t]&&(c=!0):delete e[t],c?e[t]=o:x(e,t,o)):c?e[t]=o:P(t,o)})(Function.prototype,"toString",function(){return"function"==typeof this&&t(this).source||L(this)})}),ne=r,re=function(e){return"function"==typeof e?e:void 0},ie=function(e,t){return arguments.length<2?re(ne[e])||re(r[e]):ne[e]&&ne[e][t]||r[e]&&r[e][t]},oe=Math.ceil,ae=Math.floor,se=function(e){return isNaN(e=+e)?0:(e>0?ae:oe)(e)},ce=Math.min,ue=function(e){return e>0?ce(se(e),9007199254740991):0},de=Math.max,le=Math.min,pe=function(e,t){var n=se(e);return n<0?de(n+t,0):le(n,t)},he=function(e){return function(t,n,r){var i,o=m(t),a=ue(o.length),s=pe(r,a);if(e&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},fe={includes:he(!0),indexOf:he(!1)},me=fe.indexOf,ve=function(e,t){var n,r=m(e),i=0,o=[];for(n in r)!y(J,n)&&y(r,n)&&o.push(n);for(;t.length>i;)y(r,n=t[i++])&&(~me(o,n)||o.push(n));return o},ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_e=ge.concat("length","prototype"),ye={f:Object.getOwnPropertyNames||function(e){return ve(e,_e)}},Se={f:Object.getOwnPropertySymbols},be=ie("Reflect","ownKeys")||function(e){var t=ye.f(E(e)),n=Se.f;return n?t.concat(n(e)):t},ke=function(e,t){for(var n=be(t),r=I.f,i=T.f,o=0;o<n.length;o++){var a=n[o];y(e,a)||r(e,a,i(t,a))}},Re=/#|\.prototype\./,we=function(e,t){var n=Ee[Te(e)];return n==Ie||n!=Ce&&("function"==typeof t?i(t):!!t)},Te=we.normalize=function(e){return String(e).replace(Re,".").toLowerCase()},Ee=we.data={},Ce=we.NATIVE="N",Ie=we.POLYFILL="P",xe=we,Pe=T.f,Ae=function(e,t){var n,i,o,a,s,c=e.target,u=e.global,d=e.stat;if(n=u?r:d?r[c]||P(c,{}):(r[c]||{}).prototype)for(i in t){if(a=t[i],o=e.noTargetGet?(s=Pe(n,i))&&s.value:n[i],!xe(u?i:c+(d?".":"#")+i,e.forced)&&void 0!==o){if(typeof a==typeof o)continue;ke(a,o)}(e.sham||o&&o.sham)&&x(a,"sham",!0),te(n,i,a,e)}},Oe=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},De=function(e,t,n){if(Oe(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},Ne=function(e){return Object(f(e))},Me=Array.isArray||function(e){return"Array"==l(e)},Le=!!Object.getOwnPropertySymbols&&!i(function(){return!String(Symbol())}),Ue=Le&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ve=j("wks"),je=r.Symbol,Fe=Ue?je:je&&je.withoutSetter||G,Be=function(e){return y(Ve,e)||(Le&&y(je,e)?Ve[e]=je[e]:Ve[e]=Fe("Symbol."+e)),Ve[e]},Ge=Be("species"),We=function(e,t){var n;return Me(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!Me(n.prototype)?v(n)&&null===(n=n[Ge])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},He=[].push,Je=function(e){var t=1==e,n=2==e,r=3==e,i=4==e,o=6==e,a=5==e||o;return function(s,c,u,d){for(var l,p,f=Ne(s),m=h(f),v=De(c,u,3),g=ue(m.length),_=0,y=d||We,S=t?y(s,g):n?y(s,0):void 0;g>_;_++)if((a||_ in m)&&(p=v(l=m[_],_,f),e))if(t)S[_]=p;else if(p)switch(e){case 3:return!0;case 5:return l;case 6:return _;case 2:He.call(S,l)}else if(i)return!1;return o?-1:r||i?i:S}},ze={forEach:Je(0),map:Je(1),filter:Je(2),some:Je(3),every:Je(4),find:Je(5),findIndex:Je(6)},Qe=ie("navigator","userAgent")||"",qe=r.process,Ke=qe&&qe.versions,Xe=Ke&&Ke.v8;Xe?Z=(Y=Xe.split("."))[0]+Y[1]:Qe&&(!(Y=Qe.match(/Edge\/(\d+)/))||Y[1]>=74)&&(Y=Qe.match(/Chrome\/(\d+)/))&&(Z=Y[1]);var $e=Z&&+Z,Ye=Be("species"),Ze=function(e){return $e>=51||!i(function(){var t=[];return(t.constructor={})[Ye]=function(){return{foo:1}},1!==t[e](Boolean).foo})},et=ze.filter,tt=Ze("filter"),nt=tt&&!i(function(){[].filter.call({length:-1,0:1},function(e){throw e})});Ae({target:"Array",proto:!0,forced:!tt||!nt},{filter:function(e){return et(this,e,arguments.length>1?arguments[1]:void 0)}});var rt=ze.map,it=Ze("map"),ot=it&&!i(function(){[].map.call({length:-1,0:1},function(e){throw e})});Ae({target:"Array",proto:!0,forced:!it||!ot},{map:function(e){return rt(this,e,arguments.length>1?arguments[1]:void 0)}});var at="\t\n\v\f\r                　\u2028\u2029\ufeff",st="["+at+"]",ct=RegExp("^"+st+st+"*"),ut=RegExp(st+st+"*$"),dt=function(e){return function(t){var n=String(f(t));return 1&e&&(n=n.replace(ct,"")),2&e&&(n=n.replace(ut,"")),n}},lt={start:dt(1),end:dt(2),trim:dt(3)},pt=lt.trim;function ht(e){return(ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ft(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(u){return void n(u)}s.done?t(c):Promise.resolve(c).then(r,i)}function mt(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function a(e){ft(o,r,i,a,s,"next",e)}function s(e){ft(o,r,i,a,s,"throw",e)}a(void 0)})}}function vt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _t(e,t,n){return t&&gt(e.prototype,t),n&&gt(e,n),e}function yt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function St(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){yt(e,t,n[t])})}return e}function bt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Rt(e,t)}function kt(e){return(kt=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Rt(e,t){return(Rt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function wt(e,t,n){return(wt=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&Rt(i,n.prototype),i}).apply(null,arguments)}function Tt(e){var t="function"==typeof Map?new Map:void 0;return(Tt=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return wt(e,arguments,kt(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Rt(r,e)})(e)}function Et(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function Ct(e,t,n){return(Ct="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=kt(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}function It(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(c){i=!0,o=c}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function xt(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}Ae({target:"String",proto:!0,forced:function(e){return i(function(){return!!at[e]()||"​᠎"!="​᠎"[e]()||at[e].name!==e})}("trim")},{trim:function(){return pt(this)}});t(function(e){!function(t){var n,r=Object.prototype,i=r.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag",u=t.regeneratorRuntime;if(u)e.exports=u;else{(u=t.regeneratorRuntime=e.exports).wrap=y;var d="suspendedStart",l="suspendedYield",p="executing",h="completed",f={},m={};m[a]=function(){return this};var v=Object.getPrototypeOf,g=v&&v(v(P([])));g&&g!==r&&i.call(g,a)&&(m=g);var _=R.prototype=b.prototype=Object.create(m);k.prototype=_.constructor=R,R.constructor=k,R[c]=k.displayName="GeneratorFunction",u.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},u.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,R):(e.__proto__=R,c in e||(e[c]="GeneratorFunction")),e.prototype=Object.create(_),e},u.awrap=function(e){return{__await:e}},w(T.prototype),T.prototype[s]=function(){return this},u.AsyncIterator=T,u.async=function(e,t,n,r){var i=new T(y(e,t,n,r));return u.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},w(_),_[c]="Generator",_[a]=function(){return this},_.toString=function(){return"[object Generator]"},u.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},u.values=P,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(I),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,i){return s.type="throw",s.arg=e,t.next=r,i&&(t.method="next",t.arg=n),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),u=i.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),I(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;I(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),f}}}function y(e,t,n,r){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),a=new x(r||[]);return o._invoke=function(e,t,n){var r=d;return function(i,o){if(r===p)throw new Error("Generator is already running");if(r===h){if("throw"===i)throw o;return A()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=E(a,n);if(s){if(s===f)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===d)throw r=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=p;var c=S(e,t,n);if("normal"===c.type){if(r=n.done?h:l,c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=h,n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function S(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}function b(){}function k(){}function R(){}function w(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function T(e){var t;this._invoke=function(n,r){function o(){return new Promise(function(t,o){!function t(n,r,o,a){var s=S(e[n],e,r);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&i.call(u,"__await")?Promise.resolve(u.__await).then(function(e){t("next",e,o,a)},function(e){t("throw",e,o,a)}):Promise.resolve(u).then(function(e){c.value=e,o(c)},a)}a(s.arg)}(n,r,t,o)})}return t=t?t.then(o,o):o()}}function E(e,t){var r=e.iterator[t.method];if(r===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,E(e,t),"throw"===t.method))return f;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var i=S(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,f;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,f):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function P(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(i.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=n,t.done=!0,t};return o.next=o}}return{next:A}}function A(){return{value:n,done:!0}}}(function(){return this}()||Function("return this")())});let Pt=!0,At=!0;function Ot(e,t,n){const r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)}function Dt(e,t,n){if(!e.RTCPeerConnection)return;const r=e.RTCPeerConnection.prototype,i=r.addEventListener;r.addEventListener=function(e,r){if(e!==t)return i.apply(this,arguments);const o=e=>{const t=n(e);t&&r(t)};return this._eventMap=this._eventMap||{},this._eventMap[r]=o,i.apply(this,[e,o])};const o=r.removeEventListener;r.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[n])return o.apply(this,arguments);const r=this._eventMap[n];return delete this._eventMap[n],o.apply(this,[e,r])},Object.defineProperty(r,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function Nt(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(Pt=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function Mt(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(At=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Lt(){if("object"==typeof window){if(Pt)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function Ut(e,t){At&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Vt(e){const{navigator:t}=e,n={browser:null,version:null};if(void 0===e||!e.navigator)return n.browser="Not a browser.",n;if(t.mozGetUserMedia)n.browser="firefox",n.version=Ot(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)n.browser="chrome",n.version=Ot(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))n.browser="edge",n.version=Ot(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return n.browser="Not a supported browser.",n;n.browser="safari",n.version=Ot(t.userAgent,/AppleWebKit\/(\d+)\./,1),n.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return n}function jt(e){return"[object Object]"===Object.prototype.toString.call(e)}function Ft(e){return jt(e)?Object.keys(e).reduce(function(t,n){const r=jt(e[n]),i=r?Ft(e[n]):e[n],o=r&&!Object.keys(i).length;return void 0===i||o?t:Object.assign(t,{[n]:i})},{}):e}function Bt(e,t,n){const r=n?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;const o=[];return e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)}),o.forEach(t=>{e.forEach(n=>{n.type===r&&n.trackId===t.id&&function e(t,n,r){n&&!r.has(n.id)&&(r.set(n.id,n),Object.keys(n).forEach(i=>{i.endsWith("Id")?e(t,t.get(n[i]),r):i.endsWith("Ids")&&n[i].forEach(n=>{e(t,t.get(n),r)})}))}(e,n,i)})}),i}const Gt=Lt;function Wt(e){const t=e&&e.navigator;if(!t.mediaDevices)return;const n=Vt(e),r=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const r="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);const i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[i("min",n)]=r.ideal,t.optional.push(e),(e={})[i("max",n)]=r.ideal,t.optional.push(e)):(e[i("",n)]=r.ideal,t.optional.push(e))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",n)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,n)]=r[e])})}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},i=function(e,i){if(n.version>=61)return i(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=r(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=n.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!t.mediaDevices.getSupportedConstraints||!t.mediaDevices.getSupportedConstraints().facingMode||a)){let n;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?n=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(n=["front"]),n)return t.mediaDevices.enumerateDevices().then(t=>{let a=(t=t.filter(e=>"videoinput"===e.kind)).find(e=>n.some(t=>e.label.toLowerCase().includes(t)));return!a&&t.length&&n.includes("back")&&(a=t[t.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=r(e.video),Gt("chrome: "+JSON.stringify(e)),i(e)})}e.video=r(e.video)}return Gt("chrome: "+JSON.stringify(e)),i(e)},o=function(e){return n.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(t.getUserMedia=function(e,n,r){i(e,e=>{t.webkitGetUserMedia(e,n,e=>{r&&r(o(e))})})}.bind(t),t.mediaDevices.getUserMedia){const e=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(t){return i(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}function Ht(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Jt(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)Dt(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.track.id):{track:n.track};const i=new Event("track");i.track=n.track,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)}),t.stream.getTracks().forEach(n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.id):{track:n};const i=new Event("track");i.track=n,i.receiver=r,i.transceiver={receiver:r},i.streams=[t.stream],this.dispatchEvent(i)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function zt(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){let i=n.apply(this,arguments);return i||(i=t(this,e),this._senders.push(i)),i};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{const t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Qt(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,r]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const i=function(e){const t={};return e.result().forEach(e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{n[t]=e.stat(t)}),t[n.id]=n}),t},o=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};if(arguments.length>=2){const r=function(e){n(o(i(e)))};return t.apply(this,[r,e])}return new Promise((e,n)=>{t.apply(this,[function(t){e(o(i(t)))},n])}).then(n,r)}}function qt(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Bt(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),Dt(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>Bt(t,e.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,r;return this.getSenders().forEach(n=>{n.track===e&&(t?r=!0:t=n)}),this.getReceivers().forEach(t=>(t.track===e&&(n?r=!0:n=t),t.track===e)),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function Kt(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();n.apply(this,arguments);const r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)};const r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),i.apply(this,arguments)}}function Xt(e){if(!e.RTCPeerConnection)return;const t=Vt(e);if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return Kt(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}r.apply(this,[t])};const i=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(i.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:n})}function a(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{const r=e._reverseStreams[t],i=e._streams[r.id];n=n.replace(new RegExp(r.id,"g"),i.id)}),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const r=[].slice.call(arguments,1);if(1!==r.length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");const i=this.getSenders().find(e=>e.track===t);if(i)throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const o=this._streams[n.id];if(o)o.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const r=new e.MediaStream([t]);this._streams[n.id]=r,this._reverseStreams[r.id]=n,this.addStream(r)}return this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=o(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then(e=>o(this,e))}};e.RTCPeerConnection.prototype[t]=r[t]});const s=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=a(this,arguments[0]),s.apply(this,arguments)):s.apply(this,arguments)};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=c.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(n=>{this._streams[n].getTracks().find(t=>e.track===t)&&(t=this._streams[n])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function $t(e){const t=Vt(e);if(!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),!e.RTCPeerConnection)return;t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]});const n=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?t.version<78&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}function Yt(e){Dt(e,"negotiationneeded",e=>{if("stable"===e.target.signalingState)return e})}var Zt=Object.freeze({shimMediaStream:Ht,shimOnTrack:Jt,shimGetSendersWithDtmf:zt,shimGetStats:Qt,shimSenderReceiverGetStats:qt,shimAddTrackRemoveTrackWithNative:Kt,shimAddTrackRemoveTrack:Xt,shimPeerConnection:$t,fixNegotiationNeeded:Yt,shimGetUserMedia:Wt,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(t=>{const r=n.video&&n.video.width,i=n.video&&n.video.height,o=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},r&&(n.video.mandatory.maxWidth=r),i&&(n.video.mandatory.maxHeight=i),e.navigator.mediaDevices.getUserMedia(n)})}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}});var en=t(function(e){var t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},t.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"})},t.getDescription=function(e){var n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){var n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter(function(e){return 0===e.indexOf(n)})},t.parseCandidate=function(e){for(var t,n={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:n[t[r]]=t[r+1]}return n},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,n={},r=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<r.length;i++)n[(t=r[i].trim().split("="))[0].trim()]=t[1];return n},t.writeFmtp=function(e){var t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var r=[];Object.keys(e.parameters).forEach(function(t){e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)}),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(function(e){return parseInt(e,10)})}},t.getMid=function(e){var n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),n},t.getIceParameters=function(e,n){var r=t.splitLines(e);return{usernameFragment:(r=r.concat(t.splitLines(n))).filter(function(e){return 0===e.indexOf("a=ice-ufrag:")})[0].substr(12),password:r.filter(function(e){return 0===e.indexOf("a=ice-pwd:")})[0].substr(10)}},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" "),i=3;i<r.length;i++){var o=r[i],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){var s=t.parseRtpMap(a),c=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),n.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach(function(e){n.headerExtensions.push(t.parseExtmap(e))}),n},t.writeRtpDescription=function(e,n){var r="";r+="m="+e+" ",r+=n.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=n.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach(function(e){r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)});var i=0;return n.codecs.forEach(function(e){e.maxptime>i&&(i=e.maxptime)}),i>0&&(r+="a=maxptime:"+i+"\r\n"),r+="a=rtcp-mux\r\n",n.headerExtensions&&n.headerExtensions.forEach(function(e){r+=t.writeExtmap(e)}),r},t.parseRtpEncodingParameters=function(e){var n,r=[],i=t.parseRtpParameters(e),o=-1!==i.fecMechanisms.indexOf("RED"),a=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute}),c=s.length>0&&s[0].ssrc,u=t.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})});u.length>0&&u[0].length>1&&u[0][0]===c&&(n=u[0][1]),i.codecs.forEach(function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&n&&(t.rtx={ssrc:n}),r.push(t),o&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:a?"red+ulpfec":"red"},r.push(t))}}),0===r.length&&c&&r.push({ssrc:c});var d=t.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substr(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substr(5),10)*.95-16e3:void 0,r.forEach(function(e){e.maxBitrate=d})),r},t.parseRtcpParameters=function(e){var n={},r=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0];r&&(n.cname=r.value,n.ssrc=r.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=i.length>0,n.compound=0===i.length;var o=t.matchPrefix(e,"a=rtcp-mux");return n.mux=o.length>0,n},t.parseMsid=function(e){var n,r=t.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(n=r[0].substr(7).split(" "))[0],track:n[1]};var i=t.matchPrefix(e,"a=ssrc:").map(function(e){return t.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute});return i.length>0?{stream:(n=i[0].value.split(" "))[0],track:n[1]}:void 0},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,n,r){var i=void 0!==n?n:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,n,r,i){var o=t.writeRtpDescription(e.kind,n);if(o+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),o+="a=mid:"+e.mid+"\r\n",e.direction?o+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";o+="a="+a,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+a,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+a,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),o},t.getDirection=function(e,n){for(var r=t.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substr(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var n=t.splitLines(e)[0].substr(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){var n=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var n=t.splitLines(e),r=0;r<n.length;r++)if(n[r].length<2||"="!==n[r].charAt(1))return!1;return!0},e.exports=t});function tn(e,t,n,r,i){var o=en.writeRtpDescription(e.kind,t);if(o+=en.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=en.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":i||"active"),o+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=a;var s="msid:"+(r?r.id:"-")+" "+a+"\r\n";o+="a="+s,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+en.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+en.localCName+"\r\n"),o}function nn(e,t){var n={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(e,t){e=parseInt(e,10);for(var n=0;n<t.length;n++)if(t[n].payloadType===e||t[n].preferredPayloadType===e)return t[n]},i=function(e,t,n,i){var o=r(e.parameters.apt,n),a=r(t.parameters.apt,i);return o&&a&&o.name.toLowerCase()===a.name.toLowerCase()};return e.codecs.forEach(function(r){for(var o=0;o<t.codecs.length;o++){var a=t.codecs[o];if(r.name.toLowerCase()===a.name.toLowerCase()&&r.clockRate===a.clockRate){if("rtx"===r.name.toLowerCase()&&r.parameters&&a.parameters.apt&&!i(r,a,e.codecs,t.codecs))continue;(a=JSON.parse(JSON.stringify(a))).numChannels=Math.min(r.numChannels,a.numChannels),n.codecs.push(a),a.rtcpFeedback=a.rtcpFeedback.filter(function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}}),e.headerExtensions.forEach(function(e){for(var r=0;r<t.headerExtensions.length;r++){var i=t.headerExtensions[r];if(e.uri===i.uri){n.headerExtensions.push(i);break}}}),n}function rn(e,t,n){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(n)}function on(e,t){var n=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return n||e.addRemoteCandidate(t),!n}function an(e,t){var n=new Error(t);return n.name=e,n.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],n}var sn=function(e,t){function n(t,n){n.addTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function r(t,n,r,i){var o=new Event("track");o.track=n,o.receiver=r,o.transceiver={receiver:r},o.streams=i,e.setTimeout(function(){t._dispatchEvent("track",o)})}var i=function(n){var r=this,i=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){r[e]=i[e].bind(i)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",n=JSON.parse(JSON.stringify(n||{})),this.usingBundle="max-bundle"===n.bundlePolicy,"negotiate"===n.rtcpMuxPolicy)throw an("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(n.rtcpMuxPolicy||(n.rtcpMuxPolicy="require"),n.iceTransportPolicy){case"all":case"relay":break;default:n.iceTransportPolicy="all"}switch(n.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:n.bundlePolicy="balanced"}if(n.iceServers=function(e,t){var n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(function(e){if(e&&(e.urls||e.url)){var r=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var i="string"==typeof r;return i&&(r=[r]),r=r.filter(function(e){return 0===e.indexOf("turn:")&&-1!==e.indexOf("transport=udp")&&-1===e.indexOf("turn:[")&&!n?(n=!0,!0):0===e.indexOf("stun:")&&t>=14393&&-1===e.indexOf("?transport=udp")}),delete e.url,e.urls=i?r[0]:r,!!r.length}})}(n.iceServers||[],t),this._iceGatherers=[],n.iceCandidatePoolSize)for(var o=n.iceCandidatePoolSize;o>0;o--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:n.iceServers,gatherPolicy:n.iceTransportPolicy}));else n.iceCandidatePoolSize=0;this._config=n,this.transceivers=[],this._sdpSessionId=en.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};Object.defineProperty(i.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(i.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),i.prototype.onicecandidate=null,i.prototype.onaddstream=null,i.prototype.ontrack=null,i.prototype.onremovestream=null,i.prototype.onsignalingstatechange=null,i.prototype.oniceconnectionstatechange=null,i.prototype.onconnectionstatechange=null,i.prototype.onicegatheringstatechange=null,i.prototype.onnegotiationneeded=null,i.prototype.ondatachannel=null,i.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},i.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},i.prototype.getConfiguration=function(){return this._config},i.prototype.getLocalStreams=function(){return this.localStreams},i.prototype.getRemoteStreams=function(){return this.remoteStreams},i.prototype._createTransceiver=function(e,t){var n=this.transceivers.length>0,r={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&n)r.iceTransport=this.transceivers[0].iceTransport,r.dtlsTransport=this.transceivers[0].dtlsTransport;else{var i=this._createIceAndDtlsTransports();r.iceTransport=i.iceTransport,r.dtlsTransport=i.dtlsTransport}return t||this.transceivers.push(r),r},i.prototype.addTrack=function(t,n){if(this._isClosed)throw an("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find(function(e){return e.track===t}))throw an("InvalidAccessError","Track already exists.");for(var i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(r=this.transceivers[i]);return r||(r=this._createTransceiver(t.kind)),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(n)&&this.localStreams.push(n),r.track=t,r.stream=n,r.rtpSender=new e.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},i.prototype.addStream=function(e){var n=this;if(t>=15025)e.getTracks().forEach(function(t){n.addTrack(t,e)});else{var r=e.clone();e.getTracks().forEach(function(e,t){var n=r.getTracks()[t];e.addEventListener("enabled",function(e){n.enabled=e.enabled})}),r.getTracks().forEach(function(e){n.addTrack(e,r)})}},i.prototype.removeTrack=function(t){if(this._isClosed)throw an("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var n=this.transceivers.find(function(e){return e.rtpSender===t});if(!n)throw an("InvalidAccessError","Sender was not created by this connection.");var r=n.stream;n.rtpSender.stop(),n.rtpSender=null,n.track=null,n.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(r)&&this.localStreams.indexOf(r)>-1&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},i.prototype.removeStream=function(e){var t=this;e.getTracks().forEach(function(e){var n=t.getSenders().find(function(t){return t.track===e});n&&t.removeTrack(n)})},i.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},i.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},i.prototype._createIceGatherer=function(t,n){var r=this;if(n&&t>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var i=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var n=!e.candidate||0===Object.keys(e.candidate).length;i.state=n?"completed":"gathering",null!==r.transceivers[t].bufferedCandidateEvents&&r.transceivers[t].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),i},i.prototype._gather=function(t,n){var r=this,i=this.transceivers[n].iceGatherer;if(!i.onlocalcandidate){var o=this.transceivers[n].bufferedCandidateEvents;this.transceivers[n].bufferedCandidateEvents=null,i.removeEventListener("localcandidate",this.transceivers[n].bufferCandidates),i.onlocalcandidate=function(e){if(!(r.usingBundle&&n>0)){var o=new Event("icecandidate");o.candidate={sdpMid:t,sdpMLineIndex:n};var a=e.candidate,s=!a||0===Object.keys(a).length;if(s)"new"!==i.state&&"gathering"!==i.state||(i.state="completed");else{"new"===i.state&&(i.state="gathering"),a.component=1,a.ufrag=i.getLocalParameters().usernameFragment;var c=en.writeCandidate(a);o.candidate=Object.assign(o.candidate,en.parseCandidate(c)),o.candidate.candidate=c,o.candidate.toJSON=function(){return{candidate:o.candidate.candidate,sdpMid:o.candidate.sdpMid,sdpMLineIndex:o.candidate.sdpMLineIndex,usernameFragment:o.candidate.usernameFragment}}}var u=en.getMediaSections(r._localDescription.sdp);u[o.candidate.sdpMLineIndex]+=s?"a=end-of-candidates\r\n":"a="+o.candidate.candidate+"\r\n",r._localDescription.sdp=en.getDescription(r._localDescription.sdp)+u.join("");var d=r.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state});"gathering"!==r.iceGatheringState&&(r.iceGatheringState="gathering",r._emitGatheringStateChange()),s||r._dispatchEvent("icecandidate",o),d&&(r._dispatchEvent("icecandidate",new Event("icecandidate")),r.iceGatheringState="complete",r._emitGatheringStateChange())}},e.setTimeout(function(){o.forEach(function(e){i.onlocalcandidate(e)})},0)}},i.prototype._createIceAndDtlsTransports=function(){var t=this,n=new e.RTCIceTransport(null);n.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()};var r=new e.RTCDtlsTransport(n);return r.ondtlsstatechange=function(){t._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:n,dtlsTransport:r}},i.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var n=this.transceivers[e].iceTransport;n&&(delete n.onicestatechange,delete this.transceivers[e].iceTransport);var r=this.transceivers[e].dtlsTransport;r&&(delete r.ondtlsstatechange,delete r.onerror,delete this.transceivers[e].dtlsTransport)},i.prototype._transceive=function(e,n,r){var i=nn(e.localCapabilities,e.remoteCapabilities);n&&e.rtpSender&&(i.encodings=e.sendEncodingParameters,i.rtcp={cname:en.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(i.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(i)),r&&e.rtpReceiver&&i.codecs.length>0&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?i.encodings=e.recvEncodingParameters:i.encodings=[{}],i.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(i.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(i.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(i))},i.prototype.setLocalDescription=function(e){var t,n,r=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(an("TypeError",'Unsupported type "'+e.type+'"'));if(!rn("setLocalDescription",e.type,r.signalingState)||r._isClosed)return Promise.reject(an("InvalidStateError","Can not set local "+e.type+" in state "+r.signalingState));if("offer"===e.type)t=en.splitSections(e.sdp),n=t.shift(),t.forEach(function(e,t){var n=en.parseRtpParameters(e);r.transceivers[t].localCapabilities=n}),r.transceivers.forEach(function(e,t){r._gather(e.mid,t)});else if("answer"===e.type){t=en.splitSections(r._remoteDescription.sdp),n=t.shift();var i=en.matchPrefix(n,"a=ice-lite").length>0;t.forEach(function(e,t){var o=r.transceivers[t],a=o.iceGatherer,s=o.iceTransport,c=o.dtlsTransport,u=o.localCapabilities,d=o.remoteCapabilities;if(!(en.isRejected(e)&&0===en.matchPrefix(e,"a=bundle-only").length)&&!o.rejected){var l=en.getIceParameters(e,n),p=en.getDtlsParameters(e,n);i&&(p.role="server"),r.usingBundle&&0!==t||(r._gather(o.mid,t),"new"===s.state&&s.start(a,l,i?"controlling":"controlled"),"new"===c.state&&c.start(p));var h=nn(u,d);r._transceive(o,h.codecs.length>0,!1)}})}return r._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?r._updateSignalingState("have-local-offer"):r._updateSignalingState("stable"),Promise.resolve()},i.prototype.setRemoteDescription=function(i){var o=this;if(-1===["offer","answer"].indexOf(i.type))return Promise.reject(an("TypeError",'Unsupported type "'+i.type+'"'));if(!rn("setRemoteDescription",i.type,o.signalingState)||o._isClosed)return Promise.reject(an("InvalidStateError","Can not set remote "+i.type+" in state "+o.signalingState));var a={};o.remoteStreams.forEach(function(e){a[e.id]=e});var s=[],c=en.splitSections(i.sdp),u=c.shift(),d=en.matchPrefix(u,"a=ice-lite").length>0,l=en.matchPrefix(u,"a=group:BUNDLE ").length>0;o.usingBundle=l;var p=en.matchPrefix(u,"a=ice-options:")[0];return o.canTrickleIceCandidates=!!p&&p.substr(14).split(" ").indexOf("trickle")>=0,c.forEach(function(r,c){var p=en.splitLines(r),h=en.getKind(r),f=en.isRejected(r)&&0===en.matchPrefix(r,"a=bundle-only").length,m=p[0].substr(2).split(" ")[2],v=en.getDirection(r,u),g=en.parseMsid(r),_=en.getMid(r)||en.generateIdentifier();if(f||"application"===h&&("DTLS/SCTP"===m||"UDP/DTLS/SCTP"===m))o.transceivers[c]={mid:_,kind:h,protocol:m,rejected:!0};else{var y,S,b,k,R,w,T,E,C;!f&&o.transceivers[c]&&o.transceivers[c].rejected&&(o.transceivers[c]=o._createTransceiver(h,!0));var I,x,P=en.parseRtpParameters(r);f||(I=en.getIceParameters(r,u),(x=en.getDtlsParameters(r,u)).role="client"),T=en.parseRtpEncodingParameters(r);var A=en.parseRtcpParameters(r),O=en.matchPrefix(r,"a=end-of-candidates",u).length>0,D=en.matchPrefix(r,"a=candidate:").map(function(e){return en.parseCandidate(e)}).filter(function(e){return 1===e.component});if(("offer"===i.type||"answer"===i.type)&&!f&&l&&c>0&&o.transceivers[c]&&(o._disposeIceAndDtlsTransports(c),o.transceivers[c].iceGatherer=o.transceivers[0].iceGatherer,o.transceivers[c].iceTransport=o.transceivers[0].iceTransport,o.transceivers[c].dtlsTransport=o.transceivers[0].dtlsTransport,o.transceivers[c].rtpSender&&o.transceivers[c].rtpSender.setTransport(o.transceivers[0].dtlsTransport),o.transceivers[c].rtpReceiver&&o.transceivers[c].rtpReceiver.setTransport(o.transceivers[0].dtlsTransport)),"offer"!==i.type||f){if("answer"===i.type&&!f){S=(y=o.transceivers[c]).iceGatherer,b=y.iceTransport,k=y.dtlsTransport,R=y.rtpReceiver,w=y.sendEncodingParameters,E=y.localCapabilities,o.transceivers[c].recvEncodingParameters=T,o.transceivers[c].remoteCapabilities=P,o.transceivers[c].rtcpParameters=A,D.length&&"new"===b.state&&(!d&&!O||l&&0!==c?D.forEach(function(e){on(y.iceTransport,e)}):b.setRemoteCandidates(D)),l&&0!==c||("new"===b.state&&b.start(S,I,"controlling"),"new"===k.state&&k.start(x)),!nn(y.localCapabilities,y.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&y.sendEncodingParameters[0].rtx&&delete y.sendEncodingParameters[0].rtx,o._transceive(y,"sendrecv"===v||"recvonly"===v,"sendrecv"===v||"sendonly"===v),!R||"sendrecv"!==v&&"sendonly"!==v?delete y.rtpReceiver:(C=R.track,g?(a[g.stream]||(a[g.stream]=new e.MediaStream),n(C,a[g.stream]),s.push([C,R,a[g.stream]])):(a.default||(a.default=new e.MediaStream),n(C,a.default),s.push([C,R,a.default])))}}else{(y=o.transceivers[c]||o._createTransceiver(h)).mid=_,y.iceGatherer||(y.iceGatherer=o._createIceGatherer(c,l)),D.length&&"new"===y.iceTransport.state&&(!O||l&&0!==c?D.forEach(function(e){on(y.iceTransport,e)}):y.iceTransport.setRemoteCandidates(D)),E=e.RTCRtpReceiver.getCapabilities(h),t<15019&&(E.codecs=E.codecs.filter(function(e){return"rtx"!==e.name})),w=y.sendEncodingParameters||[{ssrc:1001*(2*c+2)}];var N,M=!1;if("sendrecv"===v||"sendonly"===v){if(M=!y.rtpReceiver,R=y.rtpReceiver||new e.RTCRtpReceiver(y.dtlsTransport,h),M)C=R.track,g&&"-"===g.stream||(g?(a[g.stream]||(a[g.stream]=new e.MediaStream,Object.defineProperty(a[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(C,"id",{get:function(){return g.track}}),N=a[g.stream]):(a.default||(a.default=new e.MediaStream),N=a.default)),N&&(n(C,N),y.associatedRemoteMediaStreams.push(N)),s.push([C,R,N])}else y.rtpReceiver&&y.rtpReceiver.track&&(y.associatedRemoteMediaStreams.forEach(function(t){var n=t.getTracks().find(function(e){return e.id===y.rtpReceiver.track.id});n&&function(t,n){n.removeTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:t}))}(n,t)}),y.associatedRemoteMediaStreams=[]);y.localCapabilities=E,y.remoteCapabilities=P,y.rtpReceiver=R,y.rtcpParameters=A,y.sendEncodingParameters=w,y.recvEncodingParameters=T,o._transceive(o.transceivers[c],!1,M)}}}),void 0===o._dtlsRole&&(o._dtlsRole="offer"===i.type?"active":"passive"),o._remoteDescription={type:i.type,sdp:i.sdp},"offer"===i.type?o._updateSignalingState("have-remote-offer"):o._updateSignalingState("stable"),Object.keys(a).forEach(function(t){var n=a[t];if(n.getTracks().length){if(-1===o.remoteStreams.indexOf(n)){o.remoteStreams.push(n);var i=new Event("addstream");i.stream=n,e.setTimeout(function(){o._dispatchEvent("addstream",i)})}s.forEach(function(e){var t=e[0],i=e[1];n.id===e[2].id&&r(o,t,i,[n])})}}),s.forEach(function(e){e[2]||r(o,e[0],e[1],[])}),e.setTimeout(function(){o&&o.transceivers&&o.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))})},4e3),Promise.resolve()},i.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},i.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},i.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout(function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}},0))},i.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",t.failed>0?e="failed":t.checking>0?e="checking":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0?e="connected":t.completed>0&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var n=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",n)}},i.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":t.connected>0&&(e="connected"),e!==this.connectionState){this.connectionState=e;var n=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",n)}},i.prototype.createOffer=function(){var n=this;if(n._isClosed)return Promise.reject(an("InvalidStateError","Can not call createOffer after close"));var r=n.transceivers.filter(function(e){return"audio"===e.kind}).length,i=n.transceivers.filter(function(e){return"video"===e.kind}).length,o=arguments[0];if(o){if(o.mandatory||o.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==o.offerToReceiveAudio&&(r=!0===o.offerToReceiveAudio?1:!1===o.offerToReceiveAudio?0:o.offerToReceiveAudio),void 0!==o.offerToReceiveVideo&&(i=!0===o.offerToReceiveVideo?1:!1===o.offerToReceiveVideo?0:o.offerToReceiveVideo)}for(n.transceivers.forEach(function(e){"audio"===e.kind?--r<0&&(e.wantReceive=!1):"video"===e.kind&&--i<0&&(e.wantReceive=!1)});r>0||i>0;)r>0&&(n._createTransceiver("audio"),r--),i>0&&(n._createTransceiver("video"),i--);var a=en.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.transceivers.forEach(function(r,i){var o=r.track,a=r.kind,s=r.mid||en.generateIdentifier();r.mid=s,r.iceGatherer||(r.iceGatherer=n._createIceGatherer(i,n.usingBundle));var c=e.RTCRtpSender.getCapabilities(a);t<15019&&(c.codecs=c.codecs.filter(function(e){return"rtx"!==e.name})),c.codecs.forEach(function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),r.remoteCapabilities&&r.remoteCapabilities.codecs&&r.remoteCapabilities.codecs.forEach(function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)})}),c.headerExtensions.forEach(function(e){(r.remoteCapabilities&&r.remoteCapabilities.headerExtensions||[]).forEach(function(t){e.uri===t.uri&&(e.id=t.id)})});var u=r.sendEncodingParameters||[{ssrc:1001*(2*i+1)}];o&&t>=15019&&"video"===a&&!u[0].rtx&&(u[0].rtx={ssrc:u[0].ssrc+1}),r.wantReceive&&(r.rtpReceiver=new e.RTCRtpReceiver(r.dtlsTransport,a)),r.localCapabilities=c,r.sendEncodingParameters=u}),"max-compat"!==n._config.bundlePolicy&&(a+="a=group:BUNDLE "+n.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),a+="a=ice-options:trickle\r\n",n.transceivers.forEach(function(e,t){a+=tn(e,e.localCapabilities,"offer",e.stream,n._dtlsRole),a+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===n.iceGatheringState||0!==t&&n.usingBundle||(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,a+="a="+en.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(a+="a=end-of-candidates\r\n"))});var s=new e.RTCSessionDescription({type:"offer",sdp:a});return Promise.resolve(s)},i.prototype.createAnswer=function(){var n=this;if(n._isClosed)return Promise.reject(an("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==n.signalingState&&"have-local-pranswer"!==n.signalingState)return Promise.reject(an("InvalidStateError","Can not call createAnswer in signalingState "+n.signalingState));var r=en.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++);n.usingBundle&&(r+="a=group:BUNDLE "+n.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),r+="a=ice-options:trickle\r\n";var i=en.getMediaSections(n._remoteDescription.sdp).length;n.transceivers.forEach(function(e,o){if(!(o+1>i)){if(e.rejected)return"application"===e.kind?"DTLS/SCTP"===e.protocol?r+="m=application 0 DTLS/SCTP 5000\r\n":r+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?r+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(r+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),void(r+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n");var a;if(e.stream)"audio"===e.kind?a=e.stream.getAudioTracks()[0]:"video"===e.kind&&(a=e.stream.getVideoTracks()[0]),a&&t>=15019&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1});var s=nn(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,r+=tn(e,s,"answer",e.stream,n._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(r+="a=rtcp-rsize\r\n")}});var o=new e.RTCSessionDescription({type:"answer",sdp:r});return Promise.resolve(o)},i.prototype.addIceCandidate=function(e){var t,n=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(r,i){if(!n._remoteDescription)return i(an("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var o=e.sdpMLineIndex;if(e.sdpMid)for(var a=0;a<n.transceivers.length;a++)if(n.transceivers[a].mid===e.sdpMid){o=a;break}var s=n.transceivers[o];if(!s)return i(an("OperationError","Can not add ICE candidate"));if(s.rejected)return r();var c=Object.keys(e.candidate).length>0?en.parseCandidate(e.candidate):{};if("tcp"===c.protocol&&(0===c.port||9===c.port))return r();if(c.component&&1!==c.component)return r();if((0===o||o>0&&s.iceTransport!==n.transceivers[0].iceTransport)&&!on(s.iceTransport,c))return i(an("OperationError","Can not add ICE candidate"));var u=e.candidate.trim();0===u.indexOf("a=")&&(u=u.substr(2)),(t=en.getMediaSections(n._remoteDescription.sdp))[o]+="a="+(c.type?u:"end-of-candidates")+"\r\n",n._remoteDescription.sdp=en.getDescription(n._remoteDescription.sdp)+t.join("")}else for(var d=0;d<n.transceivers.length&&(n.transceivers[d].rejected||(n.transceivers[d].iceTransport.addRemoteCandidate({}),(t=en.getMediaSections(n._remoteDescription.sdp))[d]+="a=end-of-candidates\r\n",n._remoteDescription.sdp=en.getDescription(n._remoteDescription.sdp)+t.join(""),!n.usingBundle));d++);r()})},i.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var n=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?n=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(n=e.rtpReceiver)}),!n)throw an("InvalidAccessError","Invalid selector.");return n.getStats()}var r=[];return this.transceivers.forEach(function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(t){e[t]&&r.push(e[t].getStats())})}),Promise.all(r).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(t){var n=e[t];if(n&&n.prototype&&n.prototype.getStats){var r=n.prototype.getStats;n.prototype.getStats=function(){return r.apply(this).then(function(e){var t=new Map;return Object.keys(e).forEach(function(n){var r;e[n].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(r=e[n]).type]||r.type,t.set(n,e[n])}),t})}}});var o=["createOffer","createAnswer"];return o.forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then(function(t){"function"==typeof e[0]&&e[0].apply(null,[t])},function(t){"function"==typeof e[1]&&e[1].apply(null,[t])}):t.apply(this,arguments)}}),(o=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)},function(t){"function"==typeof e[2]&&e[2].apply(null,[t])}):t.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=i.prototype[e];i.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),i};function cn(e){const t=e&&e.navigator,n=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return n(e).catch(e=>Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString(){return this.name}}}(e)))}}function un(e){"getDisplayMedia"in e.navigator&&e.navigator.mediaDevices&&(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}function dn(e){const t=Vt(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){const t=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set(e){t.set.call(this,e);const n=new Event("enabled");n.enabled=e,this.dispatchEvent(n)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);const n=sn(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=function(e,t){let n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(e=>{if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&Ut("RTCIceServer.url","RTCIceServer.urls");const r="string"==typeof t;return r&&(t=[t]),t=t.filter(e=>{if(0===e.indexOf("stun:"))return!1;const t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!n?(n=!0,!0):t&&!n}),delete e.url,e.urls=r?t[0]:t,!!t.length}})}(e.iceServers,t.version),Lt("ICE servers after filtering:",e.iceServers)),new n(e)},e.RTCPeerConnection.prototype=n.prototype}function ln(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}var pn=Object.freeze({shimPeerConnection:dn,shimReplaceTrack:ln,shimGetUserMedia:cn,shimGetDisplayMedia:un});function hn(e){const t=Vt(e),n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){Ut("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,r)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},r&&r.prototype.getSettings){const t=r.prototype.getSettings;r.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(r&&r.prototype.applyConstraints){const t=r.prototype.applyConstraints;r.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function fn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function mn(e){const t=Vt(e);if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t],r={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=r[t]});const n=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?t.version<68&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())};const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,o]=arguments;return i.apply(this,[e||null]).then(e=>{if(t.version<53&&!n)try{e.forEach(e=>{e.type=r[e.type]||e.type})}catch(i){if("TypeError"!==i.name)throw i;e.forEach((t,n)=>{e.set(n,Object.assign({},t,{type:r[t.type]||t.type}))})}return e}).then(n,o)}}function vn(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function gn(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),Dt(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function _n(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){Ut("removeStream","removeTrack"),this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function yn(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}var Sn=Object.freeze({shimOnTrack:fn,shimPeerConnection:mn,shimSenderGetStats:vn,shimReceiverGetStats:gn,shimRemoveStream:_n,shimRTCDataChannel:yn,shimGetUserMedia:hn,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}});function bn(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(n=>t.call(this,n,e)),e.getVideoTracks().forEach(n=>t.call(this,n,e))},e.RTCPeerConnection.prototype.addTrack=function(e){const n=arguments[1];return n&&(this._localStreams?this._localStreams.includes(n)||this._localStreams.push(n):this._localStreams=[n]),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})})}}function kn(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach(e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)})}),t.apply(e,arguments)}}}function Rn(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){const r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};let s=function(e,t,n){const r=i.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=s,s=function(e,t,n){const r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=s,s=function(e,t,n){const r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=s}function wn(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(Tn(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function Tn(e){return e&&void 0!==e.video?Object.assign({},e,{video:Ft(e.video)}):e}function En(e){const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let r=e.iceServers[n];!r.hasOwnProperty("urls")&&r.hasOwnProperty("url")?(Ut("RTCIceServer.url","RTCIceServer.urls"),(r=JSON.parse(JSON.stringify(r))).urls=r.url,delete r.url,t.push(r)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in e.RTCPeerConnection&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function Cn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function In(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind);!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find(e=>"video"===e.receiver.track.kind);!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video")}return t.apply(this,arguments)}}var xn=Object.freeze({shimLocalStreamsAPI:bn,shimRemoteStreamsAPI:kn,shimCallbacksAPI:Rn,shimGetUserMedia:wn,shimConstraints:Tn,shimRTCIceServerUrls:En,shimTrackEventTransceiver:Cn,shimCreateOfferLegacy:In});function Pn(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){const n=new t(e),r=en.parseCandidate(e.candidate),i=Object.assign(n,r);return i.toJSON=function(){return{candidate:i.candidate,sdpMid:i.sdpMid,sdpMLineIndex:i.sdpMLineIndex,usernameFragment:i.usernameFragment}},i}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,Dt(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}function An(e){if(!e.RTCPeerConnection)return;const t=Vt(e);"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=function(e){if(!e||!e.sdp)return!1;const t=en.splitSections(e.sdp);return t.shift(),t.some(e=>{const t=en.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})},r=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n},i=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n},o=function(e,n){let r=65536;"firefox"===t.browser&&57===t.version&&(r=65535);const i=en.matchPrefix(e.sdp,"a=max-message-size:");return i.length>0?r=parseInt(i[0].substr(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r},a=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(n(arguments[0])){const e=r(arguments[0]),t=i(e),n=o(arguments[0],e);let a;a=0===t&&0===n?Number.POSITIVE_INFINITY:0===t||0===n?Math.max(t,n):Math.min(t,n);const s={};Object.defineProperty(s,"maxMessageSize",{get:()=>a}),this._sctp=s}return a.apply(this,arguments)}}function On(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const r=arguments[0],i=r.length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&i>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},Dt(e,"datachannel",e=>(t(e.channel,e.target),e))}function Dn(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function Nn(e){if(!e.RTCPeerConnection)return;const t=Vt(e);if("chrome"===t.browser&&t.version>=71)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(e){return e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")&&(e.sdp=e.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n")),n.apply(this,arguments)}}var Mn=Object.freeze({shimRTCIceCandidate:Pn,shimMaxMessageSize:An,shimSendThrowTypeError:On,shimConnectionState:Dn,removeAllowExtmapMixed:Nn});!function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0}){const n=Lt,r=Vt(e),i={browserDetails:r,commonShim:Mn,extractVersion:Ot,disableLog:Nt,disableWarnings:Mt};switch(r.browser){case"chrome":if(!Zt||!$t||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),i;n("adapter.js shimming chrome."),i.browserShim=Zt,Wt(e),Ht(e),$t(e),Jt(e),Xt(e),zt(e),Qt(e),qt(e),Yt(e),Pn(e),Dn(e),An(e),On(e),Nn(e);break;case"firefox":if(!Sn||!mn||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),i;n("adapter.js shimming firefox."),i.browserShim=Sn,hn(e),mn(e),fn(e),_n(e),vn(e),gn(e),yn(e),Pn(e),Dn(e),An(e),On(e);break;case"edge":if(!pn||!dn||!t.shimEdge)return n("MS edge shim is not included in this adapter release."),i;n("adapter.js shimming edge."),i.browserShim=pn,cn(e),un(e),dn(e),ln(e),An(e),On(e);break;case"safari":if(!xn||!t.shimSafari)return n("Safari shim is not included in this adapter release."),i;n("adapter.js shimming safari."),i.browserShim=xn,En(e),In(e),Rn(e),bn(e),kn(e),Cn(e),wn(e),Pn(e),An(e),On(e),Nn(e);break;default:n("Unsupported browser!")}}({window:window});var Ln=[].slice,Un={},Vn=function(e,t,n){if(!(t in Un)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";Un[t]=Function("C,a","return new C("+r.join(",")+")")}return Un[t](e,n)},jn=Function.bind||function(e){var t=Oe(this),n=Ln.call(arguments,1),r=function(){var i=n.concat(Ln.call(arguments));return this instanceof r?Vn(t,i.length,i):t.apply(e,i)};return v(t.prototype)&&(r.prototype=t.prototype),r};Ae({target:"Function",proto:!0},{bind:jn});var Fn=I.f,Bn=Function.prototype,Gn=Bn.toString,Wn=/^\s*function ([^ (]*)/;!o||"name"in Bn||Fn(Bn,"name",{configurable:!0,get:function(){try{return Gn.call(this).match(Wn)[1]}catch(e){return""}}});var Hn=function(){var e=function(){},t="undefined",n=["trace","debug","info","warn","error"];function r(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(r){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function i(e,t){for(var r=0;r<n.length;r++){var i=n[r];this[i]=this.methodFactory(i,e,t)}this.log=this.debug}function o(e,n,r){return function(){("undefined"==typeof console?"undefined":ht(console))!==t&&(i.call(this,n,r),this[e].apply(this,arguments))}}function a(n,i,a){return function(n){return"debug"===n&&(n="log"),("undefined"==typeof console?"undefined":ht(console))!==t&&(void 0!==console[n]?r(console,n):void 0!==console.log?r(console,"log"):e)}(n)||o.apply(this,arguments)}function s(e,r,o){var s,c=this,u="loglevel";function d(){var e;if(("undefined"==typeof window?"undefined":ht(window))!==t){try{e=window.localStorage[u]}catch(n){}return void 0===c.levels[e]&&(e=void 0),e}}e&&(u+=":"+e),c.name=e,c.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},c.methodFactory=o||a,c.getLevel=function(){return s},c.setLevel=function(r,o){if("string"==typeof r&&void 0!==c.levels[r.toUpperCase()]&&(r=c.levels[r.toUpperCase()]),!("number"==typeof r&&r>=0&&r<=c.levels.SILENT))throw"log.setLevel() called with invalid level: "+r;if(s=r,!1!==o&&function(e){var r=(n[e]||"silent").toUpperCase();if(("undefined"==typeof window?"undefined":ht(window))!==t)try{window.localStorage[u]=r}catch(i){}}(r),i.call(c,r,e),("undefined"==typeof console?"undefined":ht(console))===t&&r<c.levels.SILENT)return"No console available for logging"},c.setDefaultLevel=function(e){d()||c.setLevel(e,!1)},c.enableAll=function(e){c.setLevel(c.levels.TRACE,e)},c.disableAll=function(e){c.setLevel(c.levels.SILENT,e)};var l=d();null==l&&(l=null==r?"WARN":r),c.setLevel(l,!1)}var c=new s,u={};c.getLogger=function(e){if("string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=u[e];return t||(t=u[e]=new s(e,c.getLevel(),c.methodFactory)),t};var d=("undefined"==typeof window?"undefined":ht(window))!==t?window.log:void 0;return c.noConflict=function(){return("undefined"==typeof window?"undefined":ht(window))!==t&&window.log===c&&(window.log=d),c},c.getLoggers=function(){return u},c}(),Jn=function(e,t,n){var r=g(t);r in e?I.f(e,r,u(0,n)):e[r]=n},zn=Be("isConcatSpreadable"),Qn=$e>=51||!i(function(){var e=[];return e[zn]=!1,e.concat()[0]!==e}),qn=Ze("concat"),Kn=function(e){if(!v(e))return!1;var t=e[zn];return void 0!==t?!!t:Me(e)};Ae({target:"Array",proto:!0,forced:!Qn||!qn},{concat:function(e){var t,n,r,i,o,a=Ne(this),s=We(a,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(o=-1===t?a:arguments[t],Kn(o)){if(c+(i=ue(o.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<i;n++,c++)n in o&&Jn(s,c,o[n])}else{if(c>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Jn(s,c++,o)}return s.length=c,s}}),Ae({target:"Array",stat:!0},{isArray:Me});var Xn=function(e,t){var n=[][e];return!n||!i(function(){n.call(null,t||function(){throw 1},1)})},$n=[].join,Yn=h!=Object,Zn=Xn("join",",");Ae({target:"Array",proto:!0,forced:Yn||Zn},{join:function(e){return $n.call(m(this),void 0===e?",":e)}});var er=Be("species"),tr=[].slice,nr=Math.max;Ae({target:"Array",proto:!0,forced:!Ze("slice")},{slice:function(e,t){var n,r,i,o=m(this),a=ue(o.length),s=pe(e,a),c=pe(void 0===t?a:t,a);if(Me(o)&&("function"!=typeof(n=o.constructor)||n!==Array&&!Me(n.prototype)?v(n)&&null===(n=n[er])&&(n=void 0):n=void 0,n===Array||void 0===n))return tr.call(o,s,c);for(r=new(void 0===n?Array:n)(nr(c-s,0)),i=0;s<c;s++,i++)s in o&&Jn(r,i,o[s]);return r.length=i,r}});var rr=ze.some;Ae({target:"Array",proto:!0,forced:Xn("some")},{some:function(e){return rr(this,e,arguments.length>1?arguments[1]:void 0)}});var ir=Math.max,or=Math.min;Ae({target:"Array",proto:!0,forced:!Ze("splice")},{splice:function(e,t){var n,r,i,o,a,s,c=Ne(this),u=ue(c.length),d=pe(e,u),l=arguments.length;if(0===l?n=r=0:1===l?(n=0,r=u-d):(n=l-2,r=or(ir(se(t),0),u-d)),u+n-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(i=We(c,r),o=0;o<r;o++)(a=d+o)in c&&Jn(i,o,c[a]);if(i.length=r,n<r){for(o=d;o<u-r;o++)s=o+n,(a=o+r)in c?c[s]=c[a]:delete c[s];for(o=u;o>u-r+n;o--)delete c[o-1]}else if(n>r)for(o=u-r;o>d;o--)s=o+n-1,(a=o+r-1)in c?c[s]=c[a]:delete c[s];for(o=0;o<n;o++)c[o+d]=arguments[o+2];return c.length=u-r+n,i}});var ar="".repeat||function(e){var t=String(f(this)),n="",r=se(e);if(r<0||Infinity==r)throw RangeError("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},sr=Math.ceil,cr=function(e){return function(t,n,r){var i,o,a=String(f(t)),s=a.length,c=void 0===r?" ":String(r),u=ue(n);return u<=s||""==c?a:(i=u-s,(o=ar.call(c,sr(i/c.length))).length>i&&(o=o.slice(0,i)),e?a+o:o+a)}},ur={start:cr(!1),end:cr(!0)}.start,dr=Math.abs,lr=Date.prototype,pr=lr.getTime,hr=lr.toISOString,fr=i(function(){return"0385-07-25T07:06:39.999Z"!=hr.call(new Date(-5e13-1))})||!i(function(){hr.call(new Date(NaN))})?function(){if(!isFinite(pr.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+ur(dr(e),n?6:4,0)+"-"+ur(this.getUTCMonth()+1,2,0)+"-"+ur(this.getUTCDate(),2,0)+"T"+ur(this.getUTCHours(),2,0)+":"+ur(this.getUTCMinutes(),2,0)+":"+ur(this.getUTCSeconds(),2,0)+"."+ur(t,3,0)+"Z"}:hr;Ae({target:"Date",proto:!0,forced:Date.prototype.toISOString!==fr},{toISOString:fr});var mr=Date.prototype,vr=mr.toString,gr=mr.getTime;new Date(NaN)+""!="Invalid Date"&&te(mr,"toString",function(){var e=gr.call(this);return e==e?vr.call(this):"Invalid Date"});var _r=T.f,yr=i(function(){_r(1)});Ae({target:"Object",stat:!0,forced:!o||yr,sham:!o},{getOwnPropertyDescriptor:function(e,t){return _r(m(e),t)}});var Sr=!i(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),br=H("IE_PROTO"),kr=Object.prototype,Rr=Sr?Object.getPrototypeOf:function(e){return e=Ne(e),y(e,br)?e[br]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?kr:null},wr=i(function(){Rr(1)});Ae({target:"Object",stat:!0,forced:wr,sham:!Sr},{getPrototypeOf:function(e){return Rr(Ne(e))}});var Tr={};Tr[Be("toStringTag")]="z";var Er="[object z]"===String(Tr),Cr=Be("toStringTag"),Ir="Arguments"==l(function(){return arguments}()),xr=Er?l:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),Cr))?n:Ir?l(t):"Object"==(r=l(t))&&"function"==typeof t.callee?"Arguments":r},Pr=Er?{}.toString:function(){return"[object "+xr(this)+"]"};Er||te(Object.prototype,"toString",Pr,{unsafe:!0});var Ar=function(){var e=E(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function Or(e,t){return RegExp(e,t)}var Dr={UNSUPPORTED_Y:i(function(){var e=Or("a","y");return e.lastIndex=2,null!=e.exec("abcd")}),BROKEN_CARET:i(function(){var e=Or("^r","gy");return e.lastIndex=2,null!=e.exec("str")})},Nr=RegExp.prototype.exec,Mr=String.prototype.replace,Lr=Nr,Ur=function(){var e=/a/,t=/b*/g;return Nr.call(e,"a"),Nr.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),Vr=Dr.UNSUPPORTED_Y||Dr.BROKEN_CARET,jr=void 0!==/()??/.exec("")[1];(Ur||jr||Vr)&&(Lr=function(e){var t,n,r,i,o=this,a=Vr&&o.sticky,s=Ar.call(o),c=o.source,u=0,d=e;return a&&(-1===(s=s.replace("y","")).indexOf("g")&&(s+="g"),d=String(e).slice(o.lastIndex),o.lastIndex>0&&(!o.multiline||o.multiline&&"\n"!==e[o.lastIndex-1])&&(c="(?: "+c+")",d=" "+d,u++),n=new RegExp("^(?:"+c+")",s)),jr&&(n=new RegExp("^"+c+"$(?!\\s)",s)),Ur&&(t=o.lastIndex),r=Nr.call(a?n:o,d),a?r?(r.input=r.input.slice(u),r[0]=r[0].slice(u),r.index=o.lastIndex,o.lastIndex+=r[0].length):o.lastIndex=0:Ur&&r&&(o.lastIndex=o.global?r.index+r[0].length:t),jr&&r&&r.length>1&&Mr.call(r[0],n,function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(r[i]=void 0)}),r});var Fr=Lr;Ae({target:"RegExp",proto:!0,forced:/./.exec!==Fr},{exec:Fr});var Br=RegExp.prototype,Gr=Br.toString,Wr=i(function(){return"/a/b"!=Gr.call({source:"a",flags:"b"})}),Hr="toString"!=Gr.name;(Wr||Hr)&&te(RegExp.prototype,"toString",function(){var e=E(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in Br)?Ar.call(e):n)},{unsafe:!0});var Jr=Be("species"),zr=!i(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}),Qr="$0"==="a".replace(/./,"$0"),qr=!i(function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}),Kr=function(e,t,n,r){var o=Be(e),a=!i(function(){var t={};return t[o]=function(){return 7},7!=""[e](t)}),s=a&&!i(function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[Jr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return t=!0,null},n[o](""),!t});if(!a||!s||"replace"===e&&(!zr||!Qr)||"split"===e&&!qr){var c=/./[o],u=n(o,""[e],function(e,t,n,r,i){return t.exec===Fr?a&&!i?{done:!0,value:c.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}},{REPLACE_KEEPS_$0:Qr}),d=u[0],l=u[1];te(String.prototype,e,d),te(RegExp.prototype,o,2==t?function(e,t){return l.call(e,this,t)}:function(e){return l.call(e,this)})}r&&x(RegExp.prototype[o],"sham",!0)},Xr=function(e){return function(t,n){var r,i,o=String(f(t)),a=se(n),s=o.length;return a<0||a>=s?e?"":void 0:(r=o.charCodeAt(a))<55296||r>56319||a+1===s||(i=o.charCodeAt(a+1))<56320||i>57343?e?o.charAt(a):r:e?o.slice(a,a+2):i-56320+(r-55296<<10)+65536}},$r={codeAt:Xr(!1),charAt:Xr(!0)},Yr=$r.charAt,Zr=function(e,t,n){return t+(n?Yr(e,t).length:1)},ei=function(e,t){var n=e.exec;if("function"==typeof n){var r=n.call(e,t);if("object"!=typeof r)throw TypeError("RegExp exec method returned something other than an Object or null");return r}if("RegExp"!==l(e))throw TypeError("RegExp#exec called on incompatible receiver");return Fr.call(e,t)},ti=Math.max,ni=Math.min,ri=Math.floor,ii=/\$([$&'`]|\d\d?|<[^>]*>)/g,oi=/\$([$&'`]|\d\d?)/g;Kr("replace",2,function(e,t,n,r){return[function(n,r){var i=f(this),o=null==n?void 0:n[e];return void 0!==o?o.call(n,i,r):t.call(String(i),n,r)},function(e,o){if(r.REPLACE_KEEPS_$0||"string"==typeof o&&-1===o.indexOf("$0")){var a=n(t,e,this,o);if(a.done)return a.value}var s=E(e),c=String(this),u="function"==typeof o;u||(o=String(o));var d=s.global;if(d){var l=s.unicode;s.lastIndex=0}for(var p=[];;){var h=ei(s,c);if(null===h)break;if(p.push(h),!d)break;""===String(h[0])&&(s.lastIndex=Zr(c,ue(s.lastIndex),l))}for(var f,m="",v=0,g=0;g<p.length;g++){h=p[g];for(var _=String(h[0]),y=ti(ni(se(h.index),c.length),0),S=[],b=1;b<h.length;b++)S.push(void 0===(f=h[b])?f:String(f));var k=h.groups;if(u){var R=[_].concat(S,y,c);void 0!==k&&R.push(k);var w=String(o.apply(void 0,R))}else w=i(_,c,y,S,k,o);y>=v&&(m+=c.slice(v,y)+w,v=y+_.length)}return m+c.slice(v)}];function i(e,n,r,i,o,a){var s=r+e.length,c=i.length,u=oi;return void 0!==o&&(o=Ne(o),u=ii),t.call(a,u,function(t,a){var u;switch(a.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,r);case"'":return n.slice(s);case"<":u=o[a.slice(1,-1)];break;default:var d=+a;if(0===d)return t;if(d>c){var l=ri(d/10);return 0===l?t:l<=c?void 0===i[l-1]?a.charAt(1):i[l-1]+a.charAt(1):t}u=i[d-1]}return void 0===u?"":u})}});var ai=Be("match"),si=function(e){var t;return v(e)&&(void 0!==(t=e[ai])?!!t:"RegExp"==l(e))},ci=Be("species"),ui=function(e,t){var n,r=E(e).constructor;return void 0===r||null==(n=E(r)[ci])?t:Oe(n)},di=[].push,li=Math.min,pi=!i(function(){return!RegExp(4294967295,"y")});Kr("split",2,function(e,t,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var r=String(f(this)),i=void 0===n?4294967295:n>>>0;if(0===i)return[];if(void 0===e)return[r];if(!si(e))return t.call(r,e,i);for(var o,a,s,c=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),d=0,l=new RegExp(e.source,u+"g");(o=Fr.call(l,r))&&!((a=l.lastIndex)>d&&(c.push(r.slice(d,o.index)),o.length>1&&o.index<r.length&&di.apply(c,o.slice(1)),s=o[0].length,d=a,c.length>=i));)l.lastIndex===o.index&&l.lastIndex++;return d===r.length?!s&&l.test("")||c.push(""):c.push(r.slice(d)),c.length>i?c.slice(0,i):c}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var i=f(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,i,n):r.call(String(i),t,n)},function(e,i){var o=n(r,e,this,i,r!==t);if(o.done)return o.value;var a=E(e),s=String(this),c=ui(a,RegExp),u=a.unicode,d=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(pi?"y":"g"),l=new c(pi?a:"^(?:"+a.source+")",d),p=void 0===i?4294967295:i>>>0;if(0===p)return[];if(0===s.length)return null===ei(l,s)?[s]:[];for(var h=0,f=0,m=[];f<s.length;){l.lastIndex=pi?f:0;var v,g=ei(l,pi?s:s.slice(f));if(null===g||(v=li(ue(l.lastIndex+(pi?0:f)),s.length))===h)f=Zr(s,f,u);else{if(m.push(s.slice(h,f)),m.length===p)return m;for(var _=1;_<=g.length-1;_++)if(m.push(g[_]),m.length===p)return m;f=h=v}}return m.push(s.slice(h)),m}]},!pi);var hi=[].slice,fi=/MSIE .\./.test(Qe),mi=function(e){return function(t,n){var r=arguments.length>2,i=r?hi.call(arguments,2):void 0;return e(r?function(){("function"==typeof t?t:Function(t)).apply(this,i)}:t,n)}};Ae({global:!0,bind:!0,forced:fi},{setTimeout:mi(r.setTimeout),setInterval:mi(r.setInterval)});var vi=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},gi=Object.prototype.toString;function _i(e){return"[object Array]"===gi.call(e)}function yi(e){return null!==e&&"object"==typeof e}function Si(e){return"[object Function]"===gi.call(e)}function bi(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),_i(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var ki={isArray:_i,isArrayBuffer:function(e){return"[object ArrayBuffer]"===gi.call(e)},isBuffer:function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:yi,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===gi.call(e)},isFile:function(e){return"[object File]"===gi.call(e)},isBlob:function(e){return"[object Blob]"===gi.call(e)},isFunction:Si,isStream:function(e){return yi(e)&&Si(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:bi,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,i=arguments.length;r<i;r++)bi(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]="object"==typeof n?e({},n):n}for(var r=0,i=arguments.length;r<i;r++)bi(arguments[r],n);return t},extend:function(e,t,n){return bi(t,function(t,r){e[r]=n&&"function"==typeof t?vi(t,n):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}};function Ri(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var wi=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(ki.isURLSearchParams(t))r=t.toString();else{var i=[];ki.forEach(t,function(e,t){null!=e&&(ki.isArray(e)?t+="[]":e=[e],ki.forEach(e,function(e){ki.isDate(e)?e=e.toISOString():ki.isObject(e)&&(e=JSON.stringify(e)),i.push(Ri(t)+"="+Ri(e))}))}),r=i.join("&")}if(r){var o=e.indexOf("#");-1!==o&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function Ti(){this.handlers=[]}Ti.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Ti.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ti.prototype.forEach=function(e){ki.forEach(this.handlers,function(t){null!==t&&e(t)})};var Ei=Ti,Ci=function(e,t,n){return ki.forEach(n,function(n){e=n(e,t)}),e},Ii=function(e){return!(!e||!e.__CANCEL__)},xi="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Pi(){throw new Error("setTimeout has not been defined")}function Ai(){throw new Error("clearTimeout has not been defined")}var Oi=Pi,Di=Ai;function Ni(e){if(Oi===setTimeout)return setTimeout(e,0);if((Oi===Pi||!Oi)&&setTimeout)return Oi=setTimeout,setTimeout(e,0);try{return Oi(e,0)}catch(t){try{return Oi.call(null,e,0)}catch(t){return Oi.call(this,e,0)}}}"function"==typeof xi.setTimeout&&(Oi=setTimeout),"function"==typeof xi.clearTimeout&&(Di=clearTimeout);var Mi,Li=[],Ui=!1,Vi=-1;function ji(){Ui&&Mi&&(Ui=!1,Mi.length?Li=Mi.concat(Li):Vi=-1,Li.length&&Fi())}function Fi(){if(!Ui){var e=Ni(ji);Ui=!0;for(var t=Li.length;t;){for(Mi=Li,Li=[];++Vi<t;)Mi&&Mi[Vi].run();Vi=-1,t=Li.length}Mi=null,Ui=!1,function(e){if(Di===clearTimeout)return clearTimeout(e);if((Di===Ai||!Di)&&clearTimeout)return Di=clearTimeout,clearTimeout(e);try{Di(e)}catch(t){try{return Di.call(null,e)}catch(t){return Di.call(this,e)}}}(e)}}function Bi(e,t){this.fun=e,this.array=t}Bi.prototype.run=function(){this.fun.apply(null,this.array)};function Gi(){}var Wi=Gi,Hi=Gi,Ji=Gi,zi=Gi,Qi=Gi,qi=Gi,Ki=Gi;var Xi=xi.performance||{},$i=Xi.now||Xi.mozNow||Xi.msNow||Xi.oNow||Xi.webkitNow||function(){return(new Date).getTime()};var Yi=new Date;var Zi={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];Li.push(new Bi(e,t)),1!==Li.length||Ui||Ni(Fi)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:Wi,addListener:Hi,once:Ji,off:zi,removeListener:Qi,removeAllListeners:qi,emit:Ki,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*$i.call(Xi),n=Math.floor(t),r=Math.floor(t%1*1e9);return e&&(n-=e[0],(r-=e[1])<0&&(n--,r+=1e9)),[n,r]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-Yi)/1e3}},eo=function(e,t){ki.forEach(e,function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])})},to=function(e,t,n,r,i){return function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}(new Error(e),t,n,r,i)},no=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],ro=ki.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=ki.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},io=ki.isStandardBrowserEnv()?{write:function(e,t,n,r,i,o){var a=[];a.push(e+"="+encodeURIComponent(t)),ki.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),ki.isString(r)&&a.push("path="+r),ki.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},oo=function(e){return new Promise(function(t,n){var r=e.data,i=e.headers;ki.isFormData(r)&&delete i["Content-Type"];var o=new XMLHttpRequest;if(e.auth){var a=e.auth.username||"",s=e.auth.password||"";i.Authorization="Basic "+btoa(a+":"+s)}if(o.open(e.method.toUpperCase(),wi(e.url,e.params,e.paramsSerializer),!0),o.timeout=e.timeout,o.onreadystatechange=function(){if(o&&4===o.readyState&&(0!==o.status||o.responseURL&&0===o.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in o?function(e){var t,n,r,i={};return e?(ki.forEach(e.split("\n"),function(e){if(r=e.indexOf(":"),t=ki.trim(e.substr(0,r)).toLowerCase(),n=ki.trim(e.substr(r+1)),t){if(i[t]&&no.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([n]):i[t]?i[t]+", "+n:n}}),i):i}(o.getAllResponseHeaders()):null,i={data:e.responseType&&"text"!==e.responseType?o.response:o.responseText,status:o.status,statusText:o.statusText,headers:r,config:e,request:o};!function(e,t,n){var r=n.config.validateStatus;!r||r(n.status)?e(n):t(to("Request failed with status code "+n.status,n.config,null,n.request,n))}(t,n,i),o=null}},o.onabort=function(){o&&(n(to("Request aborted",e,"ECONNABORTED",o)),o=null)},o.onerror=function(){n(to("Network Error",e,null,o)),o=null},o.ontimeout=function(){n(to("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",o)),o=null},ki.isStandardBrowserEnv()){var c=io,u=(e.withCredentials||ro(e.url))&&e.xsrfCookieName?c.read(e.xsrfCookieName):void 0;u&&(i[e.xsrfHeaderName]=u)}if("setRequestHeader"in o&&ki.forEach(i,function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete i[t]:o.setRequestHeader(t,e)}),e.withCredentials&&(o.withCredentials=!0),e.responseType)try{o.responseType=e.responseType}catch(d){if("json"!==e.responseType)throw d}"function"==typeof e.onDownloadProgress&&o.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&o.upload&&o.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){o&&(o.abort(),n(e),o=null)}),void 0===r&&(r=null),o.send(r)})},ao={"Content-Type":"application/x-www-form-urlencoded"};function so(e,t){!ki.isUndefined(e)&&ki.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var co={adapter:function(){var e;return void 0!==Zi&&"[object process]"===Object.prototype.toString.call(Zi)?e=oo:"undefined"!=typeof XMLHttpRequest&&(e=oo),e}(),transformRequest:[function(e,t){return eo(t,"Accept"),eo(t,"Content-Type"),ki.isFormData(e)||ki.isArrayBuffer(e)||ki.isBuffer(e)||ki.isStream(e)||ki.isFile(e)||ki.isBlob(e)?e:ki.isArrayBufferView(e)?e.buffer:ki.isURLSearchParams(e)?(so(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):ki.isObject(e)?(so(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};ki.forEach(["delete","get","head"],function(e){co.headers[e]={}}),ki.forEach(["post","put","patch"],function(e){co.headers[e]=ki.merge(ao)});var uo=co;function lo(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var po=function(e){var t,n,r;return lo(e),e.baseURL&&(r=e.url,!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(r))&&(e.url=(t=e.baseURL,(n=e.url)?t.replace(/\/+$/,"")+"/"+n.replace(/^\/+/,""):t)),e.headers=e.headers||{},e.data=Ci(e.data,e.headers,e.transformRequest),e.headers=ki.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),ki.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||uo.adapter)(e).then(function(t){return lo(e),t.data=Ci(t.data,t.headers,e.transformResponse),t},function(t){return Ii(t)||(lo(e),t&&t.response&&(t.response.data=Ci(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})},ho=function(e,t){t=t||{};var n={};return ki.forEach(["url","method","params","data"],function(e){void 0!==t[e]&&(n[e]=t[e])}),ki.forEach(["headers","auth","proxy"],function(r){ki.isObject(t[r])?n[r]=ki.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:ki.isObject(e[r])?n[r]=ki.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])}),ki.forEach(["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"],function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}),n};function fo(e){this.defaults=e,this.interceptors={request:new Ei,response:new Ei}}fo.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=ho(this.defaults,e)).method=e.method?e.method.toLowerCase():"get";var t=[po,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)n=n.then(t.shift(),t.shift());return n},fo.prototype.getUri=function(e){return e=ho(this.defaults,e),wi(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},ki.forEach(["delete","get","head","options"],function(e){fo.prototype[e]=function(t,n){return this.request(ki.merge(n||{},{method:e,url:t}))}}),ki.forEach(["post","put","patch"],function(e){fo.prototype[e]=function(t,n,r){return this.request(ki.merge(r||{},{method:e,url:t,data:n}))}});var mo=fo;function vo(e){this.message=e}vo.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},vo.prototype.__CANCEL__=!0;var go=vo;function _o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new go(e),t(n.reason))})}_o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},_o.source=function(){var e;return{token:new _o(function(t){e=t}),cancel:e}};var yo=_o;function So(e){var t=new mo(e),n=vi(mo.prototype.request,t);return ki.extend(n,mo.prototype,t),ki.extend(n,t),n}var bo=So(uo);bo.Axios=mo,bo.create=function(e){return So(ho(bo.defaults,e))},bo.Cancel=go,bo.CancelToken=yo,bo.isCancel=Ii,bo.all=function(e){return Promise.all(e)},bo.spread=function(e){return function(t){return e.apply(null,t)}};var ko=bo,Ro=bo;ko.default=Ro;var wo=ko,To=((new Date).getTime(),0),Eo=function(){return(new Date).getTime()+To},Co=function(){var e=new Date;return e.setTime(Eo()),e.toLocaleString()},Io=fe.indexOf,xo=[].indexOf,Po=!!xo&&1/[1].indexOf(1,-0)<0,Ao=Xn("indexOf");Ae({target:"Array",proto:!0,forced:Po||Ao},{indexOf:function(e){return Po?xo.apply(this,arguments)||0:Io(this,e,arguments.length>1?arguments[1]:void 0)}});var Oo=ie("Reflect","apply"),Do=Function.apply,No=!i(function(){Oo(function(){})});Ae({target:"Reflect",stat:!0,forced:No},{apply:function(e,t,n){return Oe(e),E(n),Oo?Oo(e,t,n):Do.call(e,t,n)}});var Mo=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(r){}return function(n,r){return E(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(r),t?e.call(n,r):n.__proto__=r,n}}():void 0),Lo=function(e,t,n){var r,i;return Mo&&"function"==typeof(r=t.constructor)&&r!==n&&v(i=r.prototype)&&i!==n.prototype&&Mo(e,i),e},Uo=Be("species"),Vo=function(e){var t=ie(e),n=I.f;o&&t&&!t[Uo]&&n(t,Uo,{configurable:!0,get:function(){return this}})},jo=I.f,Fo=ye.f,Bo=ee.set,Go=Be("match"),Wo=r.RegExp,Ho=Wo.prototype,Jo=/a/g,zo=/a/g,Qo=new Wo(Jo)!==Jo,qo=Dr.UNSUPPORTED_Y;if(o&&xe("RegExp",!Qo||qo||i(function(){return zo[Go]=!1,Wo(Jo)!=Jo||Wo(zo)==zo||"/a/i"!=Wo(Jo,"i")}))){for(var Ko=function(e,t){var n,r=this instanceof Ko,i=si(e),o=void 0===t;if(!r&&i&&e.constructor===Ko&&o)return e;Qo?i&&!o&&(e=e.source):e instanceof Ko&&(o&&(t=Ar.call(e)),e=e.source),qo&&(n=!!t&&t.indexOf("y")>-1)&&(t=t.replace(/y/g,""));var a=Lo(Qo?new Wo(e,t):Wo(e,t),r?this:Ho,Ko);return qo&&n&&Bo(a,{sticky:n}),a},Xo=function(e){e in Ko||jo(Ko,e,{configurable:!0,get:function(){return Wo[e]},set:function(t){Wo[e]=t}})},$o=Fo(Wo),Yo=0;$o.length>Yo;)Xo($o[Yo++]);Ho.constructor=Ko,Ko.prototype=Ho,te(r,"RegExp",Ko)}Vo("RegExp"),Kr("match",1,function(e,t,n){return[function(t){var n=f(this),r=null==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=E(e),o=String(this);if(!i.global)return ei(i,o);var a=i.unicode;i.lastIndex=0;for(var s,c=[],u=0;null!==(s=ei(i,o));){var d=String(s[0]);c[u]=d,""===d&&(i.lastIndex=Zr(o,ue(i.lastIndex),a)),u++}return 0===u?null:c}]});var Zo=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};Kr("search",1,function(e,t,n){return[function(t){var n=f(this),r=null==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var i=E(e),o=String(this),a=i.lastIndex;Zo(a,0)||(i.lastIndex=0);var s=ei(i,o);return Zo(i.lastIndex,a)||(i.lastIndex=a),null===s?-1:s.index}]});var ea=lt.trim,ta=r.parseFloat,na=1/ta(at+"-0")!=-Infinity?function(e){var t=ea(String(e)),n=ta(t);return 0===n&&"-"==t.charAt(0)?-0:n}:ta;Ae({global:!0,forced:parseFloat!=na},{parseFloat:na});var ra="undefined"!=typeof window,ia="undefined"!=typeof wx,oa=window.navigator&&window.navigator.userAgent||"",aa=/AppleWebKit\/([\d.]+)/i.exec(oa),sa=aa?parseFloat(aa.pop()):null,ca=/iPad/i.test(oa),ua=/iPhone/i.test(oa)&&!ca,da=/iPod/i.test(oa),la=ua||ca||da,pa=la&&function(){var e=oa.match(/OS (\d+)_/i);return e&&e[1]?e[1]:null}(),ha=/Android/i.test(oa),fa=ha&&function(){var e=oa.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);return t&&n?parseFloat(e[1]+"."+e[2]):t||null}(),ma=ha&&/webkit/i.test(oa)&&fa<2.3,va=ha&&fa<5&&sa<537,ga=/Firefox/i.test(oa),_a=ga&&function(){var e=oa.match(/Firefox\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),ya=/Edge\//i.test(oa),Sa=ya&&function(){var e=oa.match(/Edge\/(\d+)/i);if(e&&e[1])return e[1]}(),ba=/Edg\//i.test(oa),ka=ba&&function(){var e=oa.match(/Edg\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Ra=/SogouMobileBrowser\//i.test(oa),wa=Ra&&function(){var e=oa.match(/SogouMobileBrowser\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Ta=/MetaSr\s/i.test(oa),Ea=Ta&&function(){var e=oa.match(/MetaSr(\s\d+(\.\d+)+)/);return e&&e[1]?parseFloat(e[1]):null}(),Ca=/TBS\/\d+/i.test(oa),Ia=Ca&&function(){var e=oa.match(/TBS\/(\d+)/i);if(e&&e[1])return e[1]}(),xa=/XWEB\/\d+/i.test(oa),Pa=xa&&function(){var e=oa.match(/XWEB\/(\d+)/i);if(e&&e[1])return e[1]}(),Aa=/MSIE\s8\.0/.test(oa),Oa=/MSIE\/\d+/i.test(oa),Da=Oa&&function(){var e=/MSIE\s(\d+)\.\d/.exec(oa),t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(oa)&&/rv:11.0/.test(oa)&&(t=11),t}(),Na=/(micromessenger|webbrowser)/i.test(oa),Ma=Na&&function(){var e=oa.match(/MicroMessenger\/(\d+)/i);if(e&&e[1])return e[1]}(),La=!Ca&&/MQQBrowser\/\d+/i.test(oa)&&/COVC\/\d+/i.test(oa),Ua=!Ca&&/MQQBrowser\/\d+/i.test(oa)&&!/COVC\/\d+/i.test(oa),Va=(Ua||La)&&function(){var e=oa.match(/ MQQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),ja=!Ca&&/ QQBrowser\/\d+/i.test(oa),Fa=ja&&function(){var e=oa.match(/ QQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Ba=!Ca&&/QQBrowserLite\/\d+/i.test(oa),Ga=Ba&&function(){var e=oa.match(/QQBrowserLite\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Wa=!Ca&&/MQBHD\/\d+/i.test(oa),Ha=Wa&&function(){var e=oa.match(/MQBHD\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Ja=/Windows/i.test(oa),za=!la&&/MAC OS X/i.test(oa),Qa=!ha&&/Linux/i.test(oa),qa=/MicroMessenger/i.test(oa),Ka=/UCBrowser/i.test(oa),Xa=/Electron/i.test(oa),$a=/MiuiBrowser/i.test(oa),Ya=$a&&function(){var e=oa.match(/MiuiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Za=/HuaweiBrowser/i.test(oa),es=Za&&function(){var e=oa.match(/HuaweiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),ts=/SamsungBrowser/i.test(oa),ns=ts&&function(){var e=oa.match(/SamsungBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),rs=/HeyTapBrowser/i.test(oa),is=rs&&function(){var e=oa.match(/HeyTapBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),os=/VivoBrowser/i.test(oa),as=os&&function(){var e=oa.match(/VivoBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),ss=/Chrome/i.test(oa),cs=!ya&&!Ta&&!Ra&&!Ca&&!xa&&!ba&&!ja&&!$a&&!Za&&!ts&&!rs&&!os&&/Chrome/i.test(oa),us=cs&&function(){var e=oa.match(/Chrome\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),ds=cs&&function(){var e=oa.match(/Chrome\/([\d.]+)/);return e&&e[1]?e[1]:null}(),ls=!ss&&!Ua&&!La&&!Ba&&!Wa&&/Safari/i.test(oa),ps=ls||la,hs=ls&&function(){var e=oa.match(/Version\/([\d.]+)/);return e&&e[1]?e[1]:null}(),fs=cs?"Chrome/"+ds:ls?"Safari/"+hs:"NotSupportedBrowser",ms="file:"===location.protocol||"localhost"===location.hostname||/^\d+\.\d+\.\d+\.\d+$/.test(location.hostname),vs=Object.freeze({IN_Browser:ra,IN_WXMiniProgram:ia,USER_AGENT:oa,IS_IPAD:ca,IS_IPHONE:ua,IS_IPOD:da,IS_IOS:la,IOS_VERSION:pa,IS_ANDROID:ha,ANDROID_VERSION:fa,IS_OLD_ANDROID:ma,IS_NATIVE_ANDROID:va,IS_FIREFOX:ga,FIREFOX_VERSION:_a,IS_EDGE:ya,EDGE_VERSION:Sa,IS_EDG:ba,EDG_VERSION:ka,IS_SOGOUM:Ra,SOGOUM_VERSION:wa,IS_SOGOU:Ta,SOGOU_VERSION:Ea,IS_TBS:Ca,TBS_VERSION:Ia,IS_XWEB:xa,XWEB_VERSION:Pa,IS_IE8:Aa,IS_IE:Oa,IE_VERSION:Da,IS_WECHAT:Na,WECHAT_VERSION:Ma,IS_X5MQQB:La,IS_MQQB:Ua,MQQB_VERSION:Va,IS_WQQB:ja,WQQB_VERSION:Fa,IS_MACQQB:Ba,MACQQB_VERSION:Ga,IS_IPADQQB:Wa,IPADQQB_VERSION:Ha,IS_WIN:Ja,IS_MAC:za,IS_LINUX:Qa,IS_WX:qa,IS_UCBROWSER:Ka,IS_ELECTRON:Xa,IS_MIBROWSER:$a,MI_VERSION:Ya,IS_HUAWEIBROWSER:Za,HUAWEI_VERSION:es,IS_SAMSUNGBROWSER:ts,SAMSUNG_VERSION:ns,IS_OPPOBROWSER:rs,OPPO_VERSION:is,IS_VIVOBROWSER:os,VIVO_VERSION:as,IS_CHROME_ONLY:ss,IS_CHROME:cs,CHROME_MAJOR_VERSION:us,CHROME_VERSION:ds,IS_SAFARI:ls,IS_ANY_SAFARI:ps,SAFARI_VERSION:hs,BROWSER_VERSION:fs,IS_LOCAL:ms}),gs="wss://bk.rtc.qq.com",_s="wss://trtc.rtc.qq.com",ys="wss://webrtc.qq.com",Ss="".concat("wss://qcloud.rtc.qq.com",":8687"),bs="".concat(gs,":8687"),ks="",Rs=function(e){return ks=e},ws=1,Ts=2,Es=20,Cs=21,Is="anchor",xs="5Y2wZK8nANNAoVw6dSAHVjNxrD1ObBM2kBPV",Ps="224d130c-7b5c-415b-aaa2-79c2eb5a6df2",As=2,Os=0,Ds={CONNECTING:"connecting",CONNECTED:"connected",DISCONNECTED:"disconnected"},Ns="join",Ms="delta-join",Ls="rejoin",Us="leave",Vs="delta-leave",js="publish",Fs="delta-publish",Bs="unpublish",Gs="subscribe",Ws="unsubscribe",Hs="uplink-connection",Js="uplink-reconnection",zs="downlink-connection",Qs="downlink-reconnection",qs="setLocalDescription",Ks="setRemoteDescription",Xs="iceConnectionState",$s="stream-initialize",Ys="websocketConnectionState",Zs="websocketReconnectionState",ec="update-stream",tc="recover-subscription",nc="unsubscribe",rc="subscribe_change",ic=0,oc=4,ac="String",sc="Number",cc="Boolean",uc="Array",dc="audio",lc="video",pc=function(e){var t=window.location.search.match(new RegExp("(\\?|&)"+e+"=([^&]*)(&|$)"));return t?decodeURIComponent(t[2]):""},hc=function(){return pc("trtc_env")},fc=function(e){var t=e,n=pc("trtc_env");return n&&(t="wss://"+n+".rtc.qq.com:8687"),t},mc=function(){return"".concat(ks||"https://yun.tim.qq.com","/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_log")};var vc=function(e){if(!e||"object"!==ht(e)||"[object Object]"!=Object.prototype.toString.call(e))return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Function.prototype.toString.call(n)===Function.prototype.toString.call(Object)};function gc(e){var t=Math.round(e/2)+1;return t>6?13e3:1e3*function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return t<=1?r:e(t-1,r,n+r)}(t)}var _c,yc=function(e){return"function"==typeof e},Sc=function(e){return void 0===e},bc=function(e){return"string"==typeof e};function kc(e){return Reflect.apply(Object.prototype.toString,e,[]).replace(/^\[object\s(\w+)\]$/,"$1").toLowerCase()}function Rc(e,t){for(var n in e)if(t[n]&&"undefined"!==kc(e[n])&&kc(e[n])!==t[n].toLowerCase())return{ret:!1,message:"".concat(n," should be ").concat(t[n])};return{ret:!0}}function wc(e){var t={};return t.urls="turn:".concat(e.url),Sc(e.username)||Sc(e.credential)||(t.username=e.username,t.credential=e.credential,t.credentialType="password",Sc(e.credentialType)||(t.credentialType=e.credentialType)),t}function Tc(e){try{return JSON.stringify(e)}catch(n){if(!_c)try{var t={};t.a=t,JSON.stringify(t)}catch(r){_c=r.message}if(n.message===_c)return"[Circular]";throw n}}function Ec(e){var t="",n=0;return e.length>1&&"string"==typeof e[0]&&(t=(t=e[0].replace(/(%?)(%([sdjo]))/g,function(t,r,i,o){if(!r){var a=e[n+=1],s="";switch(o){case"s":s+=a;break;case"d":s+=+a;break;case"j":s=Tc(a);break;case"o":var c=Tc(a);"{"!==c[0]&&"["!==c[0]&&(c="<".concat(c,">")),s=function(e){if(!Object.getOwnPropertyDescriptor||!Object.getPrototypeOf)return Object.prototype.toString.call(e).slice(8,-1);for(;e;){var t=Object.getOwnPropertyDescriptor(e,"constructor");if(void 0!==t&&"function"==typeof t.value&&""!==t.value.name)return t.value.name;e=Object.getPrototypeOf(e)}return""}(a)+c}return s}return t})).replace(/%{2,2}/g,"%"),n+=1),e.length>n&&(t&&(t+=" "),t+=e.slice(n).join(" ")),t}var Cc=Object.prototype.hasOwnProperty;function Ic(){try{throw new Error}catch(e){return e.stack}}function xc(e){var t=this,n=[],r=[];this.length=function(){return n.length},this.sent=function(){return r.length},this.push=function(t){n.push(t),n.length>e&&n.shift()},this.send=function(){return r.length||(r=n,n=[]),r},this.confirm=function(){r=[],t.content=""},this.fail=function(){n=r.concat(n),t.confirm();var i=1+n.length+r.length-e;i>0&&(r.splice(0,i),n=r.concat(n),t.confirm())}}var Pc,Ac,Oc,Dc=!!Ic();function Nc(e){return"[".concat(e.timestamp,"] <").concat(e.level.label.toUpperCase(),">").concat(e.logger?" (".concat(e.logger,")"):"",": ").concat(e.message).concat(e.stacktrace?"\n".concat(e.stacktrace):"")}var Mc={interval:1e3,level:"trace",capacity:0,stacktrace:{levels:["trace","warn","error"],depth:3,excess:0},timestamp:function(){return(new Date).toISOString()},format:Nc},Lc=-1,Uc=!1,Vc="",jc="",Fc="",Bc=function(e){Uc||(Vc="".concat(e.sdkAppId),jc="".concat(e.userId),Fc="".concat(e.version),Uc=!0)},Gc=function(e,t){if(!e||!e.getLogger)throw new TypeError("Argument is not a root loglevel object");if(Pc)throw new Error("You can assign a plugin only one time");Pc=e;var n=function e(){for(var t={},n=0;n<arguments.length;n+=1){var r=Object(arguments[n]);for(var i in r)Cc.call(r,i)&&(t[i]="object"!==ht(r[i])||Array.isArray(r[i])?r[i]:e(t[i],r[i]))}return t}(Mc,t);n.capacity=n.capacity||500;var r,i=n.interval;Lc=setInterval(function(){if(!Uc)return;if(!o.sent()){if(!o.length())return;var e=o.send();o.content=r?'{"logs":['.concat(e.join(","),"]}"):e.join("\n"),function(e){if(!Uc)return;var t=JSON.stringify({timestamp:Co(),sdkAppId:Vc,userId:jc,version:Fc,log:e});wo.post(mc(),t).then(function(){o.confirm()}).catch(function(){o.fail()})}(o.content)}},i);var o=new xc(n.capacity);return Ac=e.methodFactory,Oc=function(e,t,i){var a=Ac(e,t,i),s=Dc&&n.stacktrace.levels.some(function(t){return t===e}),c=Pc.levels[e.toUpperCase()];return function(){for(var u=arguments.length,d=new Array(u),l=0;l<u;l++)d[l]=arguments[l];var p=Ec(d),h=c>=t;if(h){var f=new Date;f.setTime(Eo());var m=f.toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1"),v="["+m+"] <"+e.toUpperCase()+"> "+p;a.apply(void 0,[v])}var g=Co(),_=s?Ic():"";if(_){var y=_.split("\n");y.splice(0,n.stacktrace.excess+3);var S=n.stacktrace.depth;if(S&&y.length!==S+1){var b=y.splice(0,S);_=b.join("\n"),y.length&&(_+="\n    and ".concat(y.length," more"))}else _=y.join("\n")}var k=n.format({message:p,level:{label:e,value:c},logger:i||"",timestamp:g,stacktrace:_});void 0===r&&(r="string"!=typeof k);var R="";if(r)try{R+=JSON.stringify(k)}catch(w){return a.apply(void 0,d),void Pc.getLogger("logger").error(w)}else R+=k;o.push(R)}},e.methodFactory=Oc,e.setLevel(e.getLevel()),e},Wc=function(){if(!Pc)throw new Error("You can't disable a not appled plugin");if(Oc!==Pc.methodFactory)throw new Error("You can't disable a plugin after appling another plugin");Pc.methodFactory=Ac,Pc.setLevel(Pc.getLevel()),Pc=void 0,clearInterval(Lc)},Hc=!1;Hn.setConfig=function(e){Bc(e)},Hn.setLogLevel=function(e){Hn.setLevel(e)},Hn.getLogLevel=function(e){return Hn.getLevel(e)},Hn.enableUploadLog=function(){Hc||(Gc(Hn),Hc=!0)},Hn.disableUploadLog=function(){Hc&&(Hn.warn("disable upload log! Without log we are difficult to help you triage the issue you might run into!"),Wc(),Hc=!1)},Hn.enableUploadLog(),Hn.setLevel("INFO");var Jc,zc=!0,Qc=Object.keys||function(e){return ve(e,ge)},qc=o?Object.defineProperties:function(e,t){E(e);for(var n,r=Qc(t),i=r.length,o=0;i>o;)I.f(e,n=r[o++],t[n]);return e},Kc=ie("document","documentElement"),Xc=H("IE_PROTO"),$c=function(){},Yc=function(e){return"<script>"+e+"<\/script>"},Zc=function(){try{Jc=document.domain&&new ActiveXObject("htmlfile")}catch(r){}var e,t;Zc=Jc?function(e){e.write(Yc("")),e.close();var t=e.parentWindow.Object;return e=null,t}(Jc):((t=k("iframe")).style.display="none",Kc.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Yc("document.F=Object")),e.close(),e.F);for(var n=ge.length;n--;)delete Zc.prototype[ge[n]];return Zc()};J[Xc]=!0;var eu=Object.create||function(e,t){var n;return null!==e?($c.prototype=E(e),n=new $c,$c.prototype=null,n[Xc]=e):n=Zc(),void 0===t?n:qc(n,t)},tu=ye.f,nu={}.toString,ru="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],iu={f:function(e){return ru&&"[object Window]"==nu.call(e)?function(e){try{return tu(e)}catch(t){return ru.slice()}}(e):tu(m(e))}},ou={f:Be},au=I.f,su=function(e){var t=ne.Symbol||(ne.Symbol={});y(t,e)||au(t,e,{value:ou.f(e)})},cu=I.f,uu=Be("toStringTag"),du=function(e,t,n){e&&!y(e=n?e:e.prototype,uu)&&cu(e,uu,{configurable:!0,value:t})},lu=ze.forEach,pu=H("hidden"),hu=Be("toPrimitive"),fu=ee.set,mu=ee.getterFor("Symbol"),vu=Object.prototype,gu=r.Symbol,_u=ie("JSON","stringify"),yu=T.f,Su=I.f,bu=iu.f,ku=c.f,Ru=j("symbols"),wu=j("op-symbols"),Tu=j("string-to-symbol-registry"),Eu=j("symbol-to-string-registry"),Cu=j("wks"),Iu=r.QObject,xu=!Iu||!Iu.prototype||!Iu.prototype.findChild,Pu=o&&i(function(){return 7!=eu(Su({},"a",{get:function(){return Su(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=yu(vu,t);r&&delete vu[t],Su(e,t,n),r&&e!==vu&&Su(vu,t,r)}:Su,Au=function(e,t){var n=Ru[e]=eu(gu.prototype);return fu(n,{type:"Symbol",tag:e,description:t}),o||(n.description=t),n},Ou=Ue?function(e){return"symbol"==typeof e}:function(e){return Object(e)instanceof gu},Du=function(e,t,n){e===vu&&Du(wu,t,n),E(e);var r=g(t,!0);return E(n),y(Ru,r)?(n.enumerable?(y(e,pu)&&e[pu][r]&&(e[pu][r]=!1),n=eu(n,{enumerable:u(0,!1)})):(y(e,pu)||Su(e,pu,u(1,{})),e[pu][r]=!0),Pu(e,r,n)):Su(e,r,n)},Nu=function(e,t){E(e);var n=m(t),r=Qc(n).concat(Vu(n));return lu(r,function(t){o&&!Mu.call(n,t)||Du(e,t,n[t])}),e},Mu=function(e){var t=g(e,!0),n=ku.call(this,t);return!(this===vu&&y(Ru,t)&&!y(wu,t))&&(!(n||!y(this,t)||!y(Ru,t)||y(this,pu)&&this[pu][t])||n)},Lu=function(e,t){var n=m(e),r=g(t,!0);if(n!==vu||!y(Ru,r)||y(wu,r)){var i=yu(n,r);return!i||!y(Ru,r)||y(n,pu)&&n[pu][r]||(i.enumerable=!0),i}},Uu=function(e){var t=bu(m(e)),n=[];return lu(t,function(e){y(Ru,e)||y(J,e)||n.push(e)}),n},Vu=function(e){var t=e===vu,n=bu(t?wu:m(e)),r=[];return lu(n,function(e){!y(Ru,e)||t&&!y(vu,e)||r.push(Ru[e])}),r};if(Le||(te((gu=function(){if(this instanceof gu)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=G(e),n=function(e){this===vu&&n.call(wu,e),y(this,pu)&&y(this[pu],t)&&(this[pu][t]=!1),Pu(this,t,u(1,e))};return o&&xu&&Pu(vu,t,{configurable:!0,set:n}),Au(t,e)}).prototype,"toString",function(){return mu(this).tag}),te(gu,"withoutSetter",function(e){return Au(G(e),e)}),c.f=Mu,I.f=Du,T.f=Lu,ye.f=iu.f=Uu,Se.f=Vu,ou.f=function(e){return Au(Be(e),e)},o&&(Su(gu.prototype,"description",{configurable:!0,get:function(){return mu(this).description}}),te(vu,"propertyIsEnumerable",Mu,{unsafe:!0}))),Ae({global:!0,wrap:!0,forced:!Le,sham:!Le},{Symbol:gu}),lu(Qc(Cu),function(e){su(e)}),Ae({target:"Symbol",stat:!0,forced:!Le},{for:function(e){var t=String(e);if(y(Tu,t))return Tu[t];var n=gu(t);return Tu[t]=n,Eu[n]=t,n},keyFor:function(e){if(!Ou(e))throw TypeError(e+" is not a symbol");if(y(Eu,e))return Eu[e]},useSetter:function(){xu=!0},useSimple:function(){xu=!1}}),Ae({target:"Object",stat:!0,forced:!Le,sham:!o},{create:function(e,t){return void 0===t?eu(e):Nu(eu(e),t)},defineProperty:Du,defineProperties:Nu,getOwnPropertyDescriptor:Lu}),Ae({target:"Object",stat:!0,forced:!Le},{getOwnPropertyNames:Uu,getOwnPropertySymbols:Vu}),Ae({target:"Object",stat:!0,forced:i(function(){Se.f(1)})},{getOwnPropertySymbols:function(e){return Se.f(Ne(e))}}),_u){var ju=!Le||i(function(){var e=gu();return"[null]"!=_u([e])||"{}"!=_u({a:e})||"{}"!=_u(Object(e))});Ae({target:"JSON",stat:!0,forced:ju},{stringify:function(e,t,n){for(var r,i=[e],o=1;arguments.length>o;)i.push(arguments[o++]);if(r=t,(v(t)||void 0!==e)&&!Ou(e))return Me(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!Ou(t))return t}),i[1]=t,_u.apply(null,i)}})}gu.prototype[hu]||x(gu.prototype,hu,gu.prototype.valueOf),du(gu,"Symbol"),J[pu]=!0;var Fu=I.f,Bu=r.Symbol;if(o&&"function"==typeof Bu&&(!("description"in Bu.prototype)||void 0!==Bu().description)){var Gu={},Wu=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),t=this instanceof Wu?new Bu(e):void 0===e?Bu():Bu(e);return""===e&&(Gu[t]=!0),t};ke(Wu,Bu);var Hu=Wu.prototype=Bu.prototype;Hu.constructor=Wu;var Ju=Hu.toString,zu="Symbol(test)"==String(Bu("test")),Qu=/^Symbol\((.*)\)[^)]+$/;Fu(Hu,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=Ju.call(e);if(y(Gu,e))return"";var n=zu?t.slice(7,-1):t.replace(Qu,"$1");return""===n?void 0:n}}),Ae({global:!0,forced:!0},{Symbol:Wu})}su("iterator");var qu=Be("unscopables"),Ku=Array.prototype;null==Ku[qu]&&I.f(Ku,qu,{configurable:!0,value:eu(null)});var Xu=function(e){Ku[qu][e]=!0},$u=ze.findIndex,Yu=!0;"findIndex"in[]&&Array(1).findIndex(function(){Yu=!1}),Ae({target:"Array",proto:!0,forced:Yu},{findIndex:function(e){return $u(this,e,arguments.length>1?arguments[1]:void 0)}}),Xu("findIndex");var Zu=ze.forEach,ed=Xn("forEach")?function(e){return Zu(this,e,arguments.length>1?arguments[1]:void 0)}:[].forEach;Ae({target:"Array",proto:!0,forced:[].forEach!=ed},{forEach:ed});var td,nd,rd,id={},od=Be("iterator"),ad=!1;[].keys&&("next"in(rd=[].keys())?(nd=Rr(Rr(rd)))!==Object.prototype&&(td=nd):ad=!0),null==td&&(td={}),y(td,od)||x(td,od,function(){return this});var sd={IteratorPrototype:td,BUGGY_SAFARI_ITERATORS:ad},cd=sd.IteratorPrototype,ud=function(){return this},dd=sd.IteratorPrototype,ld=sd.BUGGY_SAFARI_ITERATORS,pd=Be("iterator"),hd=function(){return this},fd=function(e,t,n,r,i,o,a){!function(e,t,n){var r=t+" Iterator";e.prototype=eu(cd,{next:u(1,n)}),du(e,r,!1),id[r]=ud}(n,t,r);var s,c,d,l=function(e){if(e===i&&v)return v;if(!ld&&e in f)return f[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},p=t+" Iterator",h=!1,f=e.prototype,m=f[pd]||f["@@iterator"]||i&&f[i],v=!ld&&m||l(i),g="Array"==t&&f.entries||m;if(g&&(s=Rr(g.call(new e)),dd!==Object.prototype&&s.next&&(Rr(s)!==dd&&(Mo?Mo(s,dd):"function"!=typeof s[pd]&&x(s,pd,hd)),du(s,p,!0))),"values"==i&&m&&"values"!==m.name&&(h=!0,v=function(){return m.call(this)}),f[pd]!==v&&x(f,pd,v),id[t]=v,i)if(c={values:l("values"),keys:o?v:l("keys"),entries:l("entries")},a)for(d in c)!ld&&!h&&d in f||te(f,d,c[d]);else Ae({target:t,proto:!0,forced:ld||h},c);return c},md=ee.set,vd=ee.getterFor("Array Iterator"),gd=fd(Array,"Array",function(e,t){md(this,{type:"Array Iterator",target:m(e),index:0,kind:t})},function(){var e=vd(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}},"values");id.Arguments=id.Array,Xu("keys"),Xu("values"),Xu("entries"),Ae({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var _d=!i(function(){return Object.isExtensible(Object.preventExtensions({}))}),yd=t(function(e){var t=I.f,n=G("meta"),r=0,i=Object.isExtensible||function(){return!0},o=function(e){t(e,n,{value:{objectID:"O"+ ++r,weakData:{}}})},a=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!y(e,n)){if(!i(e))return"F";if(!t)return"E";o(e)}return e[n].objectID},getWeakData:function(e,t){if(!y(e,n)){if(!i(e))return!0;if(!t)return!1;o(e)}return e[n].weakData},onFreeze:function(e){return _d&&a.REQUIRED&&i(e)&&!y(e,n)&&o(e),e}};J[n]=!0}),Sd=(yd.REQUIRED,yd.fastKey,yd.getWeakData,yd.onFreeze,Be("iterator")),bd=Array.prototype,kd=Be("iterator"),Rd=function(e,t,n,r){try{return r?t(E(n)[0],n[1]):t(n)}catch(o){var i=e.return;throw void 0!==i&&E(i.call(e)),o}},wd=t(function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,r,i,o){var a,s,c,u,d,l,p,h,f=De(n,r,i?2:1);if(o)a=e;else{if("function"!=typeof(s=function(e){if(null!=e)return e[kd]||e["@@iterator"]||id[xr(e)]}(e)))throw TypeError("Target is not iterable");if(void 0!==(h=s)&&(id.Array===h||bd[Sd]===h)){for(c=0,u=ue(e.length);u>c;c++)if((d=i?f(E(p=e[c])[0],p[1]):f(e[c]))&&d instanceof t)return d;return new t(!1)}a=s.call(e)}for(l=a.next;!(p=l.call(a)).done;)if("object"==typeof(d=Rd(a,f,p.value,i))&&d&&d instanceof t)return d;return new t(!1)}).stop=function(e){return new t(!0,e)}}),Td=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Ed=Be("iterator"),Cd=!1;try{var Id=0,xd={next:function(){return{done:!!Id++}},return:function(){Cd=!0}};xd[Ed]=function(){return this},Array.from(xd,function(){throw 2})}catch(wv){}var Pd=function(e,t){if(!t&&!Cd)return!1;var n=!1;try{var r={};r[Ed]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(wv){}return n},Ad=function(e,t,n){var o=-1!==e.indexOf("Map"),a=-1!==e.indexOf("Weak"),s=o?"set":"add",c=r[e],u=c&&c.prototype,d=c,l={},p=function(e){var t=u[e];te(u,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(a&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return a&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(a&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(xe(e,"function"!=typeof c||!(a||u.forEach&&!i(function(){(new c).entries().next()}))))d=n.getConstructor(t,e,o,s),yd.REQUIRED=!0;else if(xe(e,!0)){var h=new d,f=h[s](a?{}:-0,1)!=h,m=i(function(){h.has(1)}),g=Pd(function(e){new c(e)}),_=!a&&i(function(){for(var e=new c,t=5;t--;)e[s](t,t);return!e.has(-0)});g||((d=t(function(t,n){Td(t,d,e);var r=Lo(new c,t,d);return null!=n&&wd(n,r[s],r,o),r})).prototype=u,u.constructor=d),(m||_)&&(p("delete"),p("has"),o&&p("get")),(_||f)&&p(s),a&&u.clear&&delete u.clear}return l[e]=d,Ae({global:!0,forced:d!=c},l),du(d,e),a||n.setStrong(d,e,o),d},Od=function(e,t,n){for(var r in t)te(e,r,t[r],n);return e},Dd=I.f,Nd=yd.fastKey,Md=ee.set,Ld=ee.getterFor,Ud={getConstructor:function(e,t,n,r){var i=e(function(e,a){Td(e,i,t),Md(e,{type:t,index:eu(null),first:void 0,last:void 0,size:0}),o||(e.size=0),null!=a&&wd(a,e[r],e,n)}),a=Ld(t),s=function(e,t,n){var r,i,s=a(e),u=c(e,t);return u?u.value=n:(s.last=u={index:i=Nd(t,!0),key:t,value:n,previous:r=s.last,next:void 0,removed:!1},s.first||(s.first=u),r&&(r.next=u),o?s.size++:e.size++,"F"!==i&&(s.index[i]=u)),e},c=function(e,t){var n,r=a(e),i=Nd(t);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key==t)return n};return Od(i.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,o?e.size=0:this.size=0},delete:function(e){var t=a(this),n=c(this,e);if(n){var r=n.next,i=n.previous;delete t.index[n.index],n.removed=!0,i&&(i.next=r),r&&(r.previous=i),t.first==n&&(t.first=r),t.last==n&&(t.last=i),o?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),r=De(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),Od(i.prototype,n?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),o&&Dd(i.prototype,"size",{get:function(){return a(this).size}}),i},setStrong:function(e,t,n){var r=t+" Iterator",i=Ld(t),o=Ld(r);fd(e,t,function(e,t){Md(this,{type:r,target:e,state:i(e),kind:t,last:void 0})},function(){for(var e=o(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})},n?"entries":"values",!n,!0),Vo(t)}},Vd=(Ad("Map",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},Ud),ye.f),jd=T.f,Fd=I.f,Bd=lt.trim,Gd=r.Number,Wd=Gd.prototype,Hd="Number"==l(eu(Wd)),Jd=function(e){var t,n,r,i,o,a,s,c,u=g(e,!1);if("string"==typeof u&&u.length>2)if(43===(t=(u=Bd(u)).charCodeAt(0))||45===t){if(88===(n=u.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(u.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+u}for(a=(o=u.slice(2)).length,s=0;s<a;s++)if((c=o.charCodeAt(s))<48||c>i)return NaN;return parseInt(o,r)}return+u};if(xe("Number",!Gd(" 0o1")||!Gd("0b1")||Gd("+0x1"))){for(var zd,Qd=function(e){var t=arguments.length<1?0:e,n=this;return n instanceof Qd&&(Hd?i(function(){Wd.valueOf.call(n)}):"Number"!=l(n))?Lo(new Gd(Jd(t)),n,Qd):Jd(t)},qd=o?Vd(Gd):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),Kd=0;qd.length>Kd;Kd++)y(Gd,zd=qd[Kd])&&!y(Qd,zd)&&Fd(Qd,zd,jd(Gd,zd));Qd.prototype=Wd,Wd.constructor=Qd,te(r,"Number",Qd)}var Xd=Math.floor;Ae({target:"Number",stat:!0},{isInteger:function(e){return!v(e)&&isFinite(e)&&Xd(e)===e}});var $d=lt.trim,Yd=r.parseInt,Zd=/^[+-]?0[Xx]/,el=8!==Yd(at+"08")||22!==Yd(at+"0x16")?function(e,t){var n=$d(String(e));return Yd(n,t>>>0||(Zd.test(n)?16:10))}:Yd;Ae({global:!0,forced:parseInt!=el},{parseInt:el});var tl,nl,rl,il=r.Promise,ol=/(iphone|ipod|ipad).*applewebkit/i.test(Qe),al=r.location,sl=r.setImmediate,cl=r.clearImmediate,ul=r.process,dl=r.MessageChannel,ll=r.Dispatch,pl=0,hl={},fl=function(e){if(hl.hasOwnProperty(e)){var t=hl[e];delete hl[e],t()}},ml=function(e){return function(){fl(e)}},vl=function(e){fl(e.data)},gl=function(e){r.postMessage(e+"",al.protocol+"//"+al.host)};sl&&cl||(sl=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return hl[++pl]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},tl(pl),pl},cl=function(e){delete hl[e]},"process"==l(ul)?tl=function(e){ul.nextTick(ml(e))}:ll&&ll.now?tl=function(e){ll.now(ml(e))}:dl&&!ol?(rl=(nl=new dl).port2,nl.port1.onmessage=vl,tl=De(rl.postMessage,rl,1)):!r.addEventListener||"function"!=typeof postMessage||r.importScripts||i(gl)?tl="onreadystatechange"in k("script")?function(e){Kc.appendChild(k("script")).onreadystatechange=function(){Kc.removeChild(this),fl(e)}}:function(e){setTimeout(ml(e),0)}:(tl=gl,r.addEventListener("message",vl,!1)));var _l,yl,Sl,bl,kl,Rl,wl,Tl,El={set:sl,clear:cl},Cl=T.f,Il=El.set,xl=r.MutationObserver||r.WebKitMutationObserver,Pl=r.process,Al=r.Promise,Ol="process"==l(Pl),Dl=Cl(r,"queueMicrotask"),Nl=Dl&&Dl.value;Nl||(_l=function(){var e,t;for(Ol&&(e=Pl.domain)&&e.exit();yl;){t=yl.fn,yl=yl.next;try{t()}catch(wv){throw yl?bl():Sl=void 0,wv}}Sl=void 0,e&&e.enter()},Ol?bl=function(){Pl.nextTick(_l)}:xl&&!ol?(kl=!0,Rl=document.createTextNode(""),new xl(_l).observe(Rl,{characterData:!0}),bl=function(){Rl.data=kl=!kl}):Al&&Al.resolve?(wl=Al.resolve(void 0),Tl=wl.then,bl=function(){Tl.call(wl,_l)}):bl=function(){Il.call(r,_l)});var Ml,Ll,Ul,Vl,jl=Nl||function(e){var t={fn:e,next:void 0};Sl&&(Sl.next=t),yl||(yl=t,bl()),Sl=t},Fl=function(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=Oe(t),this.reject=Oe(n)},Bl={f:function(e){return new Fl(e)}},Gl=function(e,t){if(E(e),v(t)&&t.constructor===e)return t;var n=Bl.f(e);return(0,n.resolve)(t),n.promise},Wl=function(e){try{return{error:!1,value:e()}}catch(wv){return{error:!0,value:wv}}},Hl=El.set,Jl=Be("species"),zl="Promise",Ql=ee.get,ql=ee.set,Kl=ee.getterFor(zl),Xl=il,$l=r.TypeError,Yl=r.document,Zl=r.process,ep=ie("fetch"),tp=Bl.f,np=tp,rp="process"==l(Zl),ip=!!(Yl&&Yl.createEvent&&r.dispatchEvent),op=xe(zl,function(){if(!(L(Xl)!==String(Xl))){if(66===$e)return!0;if(!rp&&"function"!=typeof PromiseRejectionEvent)return!0}if($e>=51&&/native code/.test(Xl))return!1;var e=Xl.resolve(1),t=function(e){e(function(){},function(){})};return(e.constructor={})[Jl]=t,!(e.then(function(){})instanceof t)}),ap=op||!Pd(function(e){Xl.all(e).catch(function(){})}),sp=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},cp=function(e,t,n){if(!t.notified){t.notified=!0;var r=t.reactions;jl(function(){for(var i=t.value,o=1==t.state,a=0;r.length>a;){var s,c,u,d=r[a++],l=o?d.ok:d.fail,p=d.resolve,h=d.reject,f=d.domain;try{l?(o||(2===t.rejection&&pp(e,t),t.rejection=1),!0===l?s=i:(f&&f.enter(),s=l(i),f&&(f.exit(),u=!0)),s===d.promise?h($l("Promise-chain cycle")):(c=sp(s))?c.call(s,p,h):p(s)):h(i)}catch(wv){f&&!u&&f.exit(),h(wv)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&dp(e,t)})}},up=function(e,t,n){var i,o;ip?((i=Yl.createEvent("Event")).promise=t,i.reason=n,i.initEvent(e,!1,!0),r.dispatchEvent(i)):i={promise:t,reason:n},(o=r["on"+e])?o(i):"unhandledrejection"===e&&function(e,t){var n=r.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},dp=function(e,t){Hl.call(r,function(){var n,r=t.value;if(lp(t)&&(n=Wl(function(){rp?Zl.emit("unhandledRejection",r,e):up("unhandledrejection",e,r)}),t.rejection=rp||lp(t)?2:1,n.error))throw n.value})},lp=function(e){return 1!==e.rejection&&!e.parent},pp=function(e,t){Hl.call(r,function(){rp?Zl.emit("rejectionHandled",e):up("rejectionhandled",e,t.value)})},hp=function(e,t,n,r){return function(i){e(t,n,i,r)}},fp=function(e,t,n,r){t.done||(t.done=!0,r&&(t=r),t.value=n,t.state=2,cp(e,t,!0))},mp=function(e,t,n,r){if(!t.done){t.done=!0,r&&(t=r);try{if(e===n)throw $l("Promise can't be resolved itself");var i=sp(n);i?jl(function(){var r={done:!1};try{i.call(n,hp(mp,e,r,t),hp(fp,e,r,t))}catch(wv){fp(e,r,wv,t)}}):(t.value=n,t.state=1,cp(e,t,!1))}catch(wv){fp(e,{done:!1},wv,t)}}};op&&(Xl=function(e){Td(this,Xl,zl),Oe(e),Ml.call(this);var t=Ql(this);try{e(hp(mp,this,t),hp(fp,this,t))}catch(wv){fp(this,t,wv)}},(Ml=function(e){ql(this,{type:zl,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Od(Xl.prototype,{then:function(e,t){var n=Kl(this),r=tp(ui(this,Xl));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=rp?Zl.domain:void 0,n.parent=!0,n.reactions.push(r),0!=n.state&&cp(this,n,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),Ll=function(){var e=new Ml,t=Ql(e);this.promise=e,this.resolve=hp(mp,e,t),this.reject=hp(fp,e,t)},Bl.f=tp=function(e){return e===Xl||e===Ul?new Ll(e):np(e)},"function"==typeof il&&(Vl=il.prototype.then,te(il.prototype,"then",function(e,t){var n=this;return new Xl(function(e,t){Vl.call(n,e,t)}).then(e,t)},{unsafe:!0}),"function"==typeof ep&&Ae({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return Gl(Xl,ep.apply(r,arguments))}}))),Ae({global:!0,wrap:!0,forced:op},{Promise:Xl}),du(Xl,zl,!1),Vo(zl),Ul=ie(zl),Ae({target:zl,stat:!0,forced:op},{reject:function(e){var t=tp(this);return t.reject.call(void 0,e),t.promise}}),Ae({target:zl,stat:!0,forced:op},{resolve:function(e){return Gl(this,e)}}),Ae({target:zl,stat:!0,forced:ap},{all:function(e){var t=this,n=tp(t),r=n.resolve,i=n.reject,o=Wl(function(){var n=Oe(t.resolve),o=[],a=0,s=1;wd(e,function(e){var c=a++,u=!1;o.push(void 0),s++,n.call(t,e).then(function(e){u||(u=!0,o[c]=e,--s||r(o))},i)}),--s||r(o)});return o.error&&i(o.value),n.promise},race:function(e){var t=this,n=tp(t),r=n.reject,i=Wl(function(){var i=Oe(t.resolve);wd(e,function(e){i.call(t,e).then(n.resolve,r)})});return i.error&&r(i.value),n.promise}});Ad("Set",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},Ud);var vp=$r.charAt,gp=ee.set,_p=ee.getterFor("String Iterator");fd(String,"String",function(e){gp(this,{type:"String Iterator",string:String(e),index:0})},function(){var e,t=_p(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=vp(n,r),t.index+=e.length,{value:e,done:!1})});var yp,Sp=function(e){if(si(e))throw TypeError("The method doesn't accept regular expressions");return e},bp=Be("match"),kp=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[bp]=!1,"/./"[e](t)}catch(r){}}return!1},Rp=T.f,wp="".startsWith,Tp=Math.min,Ep=kp("startsWith"),Cp=!(Ep||(yp=Rp(String.prototype,"startsWith"),!yp||yp.writable));Ae({target:"String",proto:!0,forced:!Cp&&!Ep},{startsWith:function(e){var t=String(f(this));Sp(e);var n=ue(Tp(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return wp?wp.call(t,r,n):t.slice(n,n+r.length)===r}});var Ip={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var xp in Ip){var Pp=r[xp],Ap=Pp&&Pp.prototype;if(Ap&&Ap.forEach!==ed)try{x(Ap,"forEach",ed)}catch(wv){Ap.forEach=ed}}var Op=Be("iterator"),Dp=Be("toStringTag"),Np=gd.values;for(var Mp in Ip){var Lp=r[Mp],Up=Lp&&Lp.prototype;if(Up){if(Up[Op]!==Np)try{x(Up,Op,Np)}catch(wv){Up[Op]=Np}if(Up[Dp]||x(Up,Dp,Mp),Ip[Mp])for(var Vp in gd)if(Up[Vp]!==gd[Vp])try{x(Up,Vp,gd[Vp])}catch(wv){Up[Vp]=gd[Vp]}}}var jp=i(function(){Qc(1)});Ae({target:"Object",stat:!0,forced:jp},{keys:function(e){return Qc(Ne(e))}});var Fp=c.f,Bp=function(e){return function(t){for(var n,r=m(t),i=Qc(r),a=i.length,s=0,c=[];a>s;)n=i[s++],o&&!Fp.call(r,n)||c.push(e?[n,r[n]]:r[n]);return c}},Gp={entries:Bp(!0),values:Bp(!1)}.values;Ae({target:"Object",stat:!0},{values:function(e){return Gp(e)}});var Wp=t(function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function o(e,t,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||e,a),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,o=r.length,a=new Array(o);i<o;i++)a[i]=r[i].fn;return a},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,o,a){var s=n?n+e:e;if(!this._events[s])return!1;var c,u,d=this._events[s],l=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),l){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,r),!0;case 4:return d.fn.call(d.context,t,r,i),!0;case 5:return d.fn.call(d.context,t,r,i,o),!0;case 6:return d.fn.call(d.context,t,r,i,o,a),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];d.fn.apply(d.context,c)}else{var p,h=d.length;for(u=0;u<h;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),l){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,r);break;case 4:d[u].fn.call(d[u].context,t,r,i);break;default:if(!c)for(p=1,c=new Array(l-1);p<l;p++)c[p-1]=arguments[p];d[u].fn.apply(d[u].context,c)}}return!0},s.prototype.on=function(e,t,n){return o(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return o(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var o=n?n+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||a(this,o);else{for(var c=0,u=[],d=s.length;c<d;c++)(s[c].fn!==t||i&&!s[c].once||r&&s[c].context!==r)&&u.push(s[c]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s}),Hp="connection-state-changed",Jp="connected",zp="error",Qp={DISCONNECTED:"DISCONNECTED",CONNECTING:"CONNECTING",RECONNECTING:"RECONNECTING",CONNECTED:"CONNECTED"},qp={NEW_ICE_CANDIDATE:4,CLINET_BANNED:8,CHANNEL_SETUP_SUCCESS:19,CHANNEL_SETUP_FAILED:80,REBUILD_SESSION_RESULT:514,JOIN_ROOM_RESULT:20,PEER_JOIN:4134,PEER_LEAVE:4135,STREAM_ADDED:16,STREAM_REMOVED:18,UPDATE_REMOTE_SDP:48,UPDATE_AUDIO_SSRC:50,UPDATE_VIDEO_SSRC:52,UPLINK_NETWORK_STATS:22,UPDATE_REMOTE_MUTE_STAT:23,CLOSE_PEER_ACK:10,SUBSCRIBE_ACK:26,PONG:775,PUBLISH_RESULT:4098,UNPUBLISH_RESULT:4100,SUBSCRIBE_RESULT:4102,UNSUBSCRIBE_RESULT:4104,SUBSCRIBE_CHANGE_RESULT:4106,REMOTE_ANSWER:4128,REMOTE_STREAM_UPDATE:4130,START_PUSH_CDN_RES:777,STOP_PUSH_CDN_RES:779,START_MIX_TRANSCODE_RES:781,STOP_MIX_TRANSCODE_RES:783,USER_LIST_RES:4137},Kp={NEW_ICE_CANDIDATE:"new-ice-candidate",CLINET_BANNED:"client-banned",CHANNEL_SETUP_SUCCESS:"channel-setup-success",CHANNEL_SETUP_FAILED:"channel-setup-failed",REBUILD_SESSION_RESULT:"channel-reconnect-result",JOIN_ROOM_RESULT:"join-room-result",PEER_JOIN:"peer-join",PEER_LEAVE:"peer-leave",STREAM_ADDED:"stream-added",STREAM_REMOVED:"stream-removed",UPDATE_REMOTE_SDP:"update-remote-sdp",UPDATE_AUDIO_SSRC:"update-audio-ssrc",UPDATE_VIDEO_SSRC:"update-video-ssrc",UPLINK_NETWORK_STATS:"uplink-network-stats",UPDATE_REMOTE_MUTE_STAT:"update-remote-mute-stat",CLOSE_PEER_ACK:"close-peer-ack",SUBSCRIBE_ACK:"subscribe-ack",REQUEST_REBUILD_SESSION:"request-rebuild-session",CLIENT_REJOIN:"client-rejoin",PONG:"pong",PUBLISH_RESULT:"publish-result",UNPUBLISH_RESULT:"unpublish-result",SUBSCRIBE_RESULT:"subscribe-result",SUBSCRIBE_CHANGE_RESULT:"subscribe-change-result",UNSUBSCRIBE_RESULT:"unsubscribe-result",REMOTE_ANSWER:"remote-answer",REMOTE_STREAM_UPDATE:"remote-stream-update",START_PUSH_CDN_RES:"start-push-cdn-res",STOP_PUSH_CDN_RES:"stop-push-cdn-res",START_MIX_TRANSCODE_RES:"start-mix-transcode-res",STOP_MIX_TRANSCODE_RES:"stop-mix-transcode-res",USER_LIST_RES:"user-list-res"},Xp="on_update_track",$p="on_create_room",Yp="on_quit_room",Zp="on_quality_report",eh="on_rebuild_session",th="on_mute_uplink",nh="on_constraints_config",rh="ping",ih="on_publish",oh="on_unpublish",ah="on_sub",sh="on_unsub",ch="on_sub_change",uh="on_start_push_user_cdn",dh="on_stop_push_user_cdn",lh="on_start_mcu_mix",ph="on_stop_mcu_mix",hh="on_get_user_list",fh={INVALID_PARAMETER:4096,INVALID_OPERATION:4097,NOT_SUPPORTED:4098,DEVICE_NOT_FOUND:4099,SIGNAL_CHANNEL_SETUP_FAILED:16385,SIGNAL_CHANNEL_ERROR:16386,ICE_TRANSPORT_ERROR:16387,JOIN_ROOM_FAILED:16388,CREATE_OFFER_FAILED:16389,SIGNAL_CHANNEL_RECONNECTION_FAILED:16390,UPLINK_RECONNECTION_FAILED:16391,DOWNLINK_RECONNECTION_FAILED:16392,CLIENT_BANNED:16448,SERVER_TIMEOUT:16449,SUBSCRIPTION_TIMEOUT:16450,PLAY_NOT_ALLOWED:16451,DEVICE_AUTO_RECOVER_FAILED:16452,START_PUBLISH_CDN_FAILED:16453,STOP_PUBLISH_CDN_FAILED:16454,START_MIX_TRANSCODE_FAILED:16455,STOP_MIX_TRANSCODE_FAILED:16456,UNKNOWN:65535},mh=function(e){for(var t in fh)if(fh[t]===e)return t;return"UNKNOWN"},vh=function(e){function t(e){var n,r=e.message,i=e.code,o=void 0===i?fh.UNKNOWN:i,a=e.extraCode,s=void 0===a?0:a;return vt(this,t),(n=Et(this,kt(t).call(this,r+" <".concat(mh(o)," 0x").concat(o.toString(16),"> https://trtc-1252463788.file.myqcloud.com/web/docs/module-ErrorCode.html")))).code_=o,n.extraCode_=s,n.name="RtcError",n.message_=r,n}return bt(t,Tt(Error)),_t(t,[{key:"getCode",value:function(){return this.code_}},{key:"getExtraCode",value:function(){return this.extraCode_}}]),t}(),gh=32768,_h=32769,yh=32770,Sh=32771,bh=32772,kh=32773,Rh=32774,wh=32775,Th=32777,Eh=32778,Ch=32779,Ih=32780,xh=32781,Ph=32782,Ah=32783,Oh=32784,Dh=32785,Nh=32786,Mh=32787,Lh=32788,Uh=32789,Vh=32790,jh=32791,Fh=32792,Bh=32793,Gh=32794,Wh=32795,Hh=32796,Jh=32797,zh=32798,Qh=32799,qh=32800,Kh=32801,Xh=32802,$h=new Map,Yh=function(e,t){var n=$h.get(e);n||($h.set(e,[]),n=$h.get(e)),n.push(t)},Zh=function(e){var t=$h.get(e);return t?$h.delete(e):t=[],t},ef={sdkAppId:"",userId:"",version:"",env:"qcloud",browserVersion:"",ua:""},tf=function(e){ef.sdkAppId="".concat(e.sdkAppId),ef.version="".concat(e.version),ef.env=e.env,ef.userId=e.userId,ef.browserVersion=e.browserVersion,ef.ua=e.ua},nf=function(e,t){var n={timestamp:Co(),sdkAppId:ef.sdkAppId,userId:ef.userId,version:ef.version,log:e};t&&(n.errorInfo=t.message),wo.post(mc(),JSON.stringify(n)).catch(function(){})},rf=function(e){var t="stat-".concat(e.eventType,"-").concat(e.result);"delta-join"!==e.eventType&&"delta-leave"!==e.eventType&&"delta-publish"!==e.eventType||(t="".concat(e.eventType,":").concat(e.delta)),nf(t),"failed"===e.result&&(t="stat-".concat(e.eventType,"-").concat(e.result,"-").concat(e.code),nf(t,e.error))},of=function(e){if(!ms){var t=St({},e,ef);void 0===t.code&&(t.code="failed"===t.result?fh.UNKNOWN:0),wo.post("".concat(ks||"https://yun.tim.qq.com","/v5/AVQualityReportSvc/C2S?sdkappid=1&cmdtype=jssdk_event"),JSON.stringify(t)).catch(function(){})}},af=function(e){ms||(of(St({},e,{result:"success"})),"qcloud"===ef.env&&rf(St({},e,{result:"success"})))},sf=function(e){if(!ms){var t=e.eventType,n=e.code,r=e.error,i={eventType:t,result:"failed",code:n||(r instanceof vh?r.getExtraCode()||r.getCode():fh.UNKNOWN)};of(i),"qcloud"===ef.env&&rf(St({},i,{error:r}))}},cf=Object.prototype.hasOwnProperty;function uf(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(vc(e))switch(Object.prototype.toString.call(e)){case"[object File]":case"[object Map]":case"[object Set]":return 0===e.size;case"[object Object]":for(var t in e)if(cf.call(e,t))return!1;return!0}return!1}var df,lf=function(){function e(t){vt(this,e),this.sdkAppId_=t.sdkAppId,this.userId_=t.userId,this.userSig_=t.userSig,this.url_=t.url,this.backupUrl_=t.backupUrl,this.version_=t.version,this.urlWithParam_="".concat(this.url_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.backupUrlWithParam_="".concat(this.backupUrl_,"?sdkAppid=").concat(this.sdkAppId_,"&identifier=").concat(this.userId_,"&userSig=").concat(this.userSig_),this.isConnected_=!1,this.isConnecting_=!1,this.socketInUse_=null,this.socket_=null,this.backupSocket_=null,this.backupTimer_=-1,this.signalInfo_={},this.currentState_=Qp.DISCONNECTED,this.reconnectionCount_=0,this.reconnectionTimer_=-1,this.pingPongTimeoutId_=-1,this.pingTimeoutId_=-1,this.emitter_=new Wp}var t;return _t(e,[{key:"connect",value:function(){var e=this;Hn.info("connect to url: ".concat(this.urlWithParam_)),this.emitter_.emit(Hp,{prevState:this.currentState_,state:Qp.CONNECTING}),this.currentState_=Qp.CONNECTING,this.socket_=new WebSocket(this.urlWithParam_),this.bindSocket(this.socket_),this.backupTimer_=setTimeout(function(){e.isConnected_||(Hn.info("trying to connect to backupUrl"),e.tryConnectBackup())},5e3)}},{key:"tryConnectBackup",value:function(){this.backupSocket_||(this.unbindAndCloseSocket("main"),Hn.debug("try to connect to url: ".concat(this.backupUrlWithParam_)),this.backupSocket_=new WebSocket(this.backupUrlWithParam_),this.bindSocket(this.backupSocket_))}},{key:"bindSocket",value:function(e){e.onopen=this.onopen.bind(this),e.onclose=this.onclose.bind(this),e.onerror=this.onerror.bind(this),e.onmessage=this.onmessage.bind(this)}},{key:"unbindSocket",value:function(e){e.onopen=function(){},e.onclose=function(){},e.onerror=function(){},e.onmessage=function(){}}},{key:"unbindAndCloseSocket",value:function(e){if("main"===e){if(this.socket_){this.unbindSocket(this.socket_);try{this.socket_.close(1e3)}catch(wv){}this.socket_=null}}else if(this.backupSocket_){this.unbindSocket(this.backupSocket_);try{this.backupSocket_.close(1e3)}catch(wv){}this.backupSocket_=null}}},{key:"clearBackupTimer",value:function(){-1!==this.backupTimer_&&(clearTimeout(this.backupTimer_),this.backupTimer_=-1)}},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"onopen",value:function(e){if(!this.isConnected_){this.isConnected_=!0,this.isConnecting_=!1,this.clearBackupTimer(),e.target===this.socket_?(this.unbindAndCloseSocket("backup"),this.socketInUse_=this.socket_):(this.unbindAndCloseSocket("main"),this.socketInUse_=this.backupSocket_);var t=e.target.url;Hn.info("[".concat(this.userId_,"] websocket[").concat(t,"] is connected")),this.emitter_.emit(Hp,{prevState:this.currentState_,state:Qp.CONNECTED}),this.currentState_===Qp.CONNECTING?this.addSignalEvent(jh,"signal channel is connected"):this.currentState_===Qp.RECONNECTING&&this.addSignalEvent(Wh,"signal channel reconnect success"),this.currentState_=Qp.CONNECTED,this.emitter_.emit(Jp)}}},{key:"onclose",value:function(e){var t=e.target.url,n=e.target===this.socketInUse_;Hn.info("[".concat(this.userId_,"] websocket[").concat(t," InUse: ").concat(n,"] is closed with code: ").concat(e.code)),e.target===this.socketInUse_&&(this.isConnected_=!1,e.wasClean&&1e3===e.code?(this.emitter_.emit(Hp,{prevState:this.currentState_,state:Qp.DISCONNECTED}),this.currentState_=Qp.DISCONNECTED,this.addSignalEvent(Vh,"signal channel is disconnected")):(Hn.warn("[".concat(this.userId_,"] onclose code:").concat(e.code," reason:").concat(e.reason)),Hn.warn("close current websocket and schedule a reconnect timeout"),this.socketInUse_.onclose=function(){},this.socketInUse_.close(4011),this.socket_=this.backupSocket_=this.socketInUse_=null,this.reconnect("main")))}},{key:"onerror",value:function(e){var t=e.target.url;Hn.debug("[".concat(this.userId_,"] websocket[").concat(t,"] error observed")),this.isConnected_?e.target===this.socketInUse_&&(this.isConnected_=!1,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup"),this.socketInUse_=null,this.reconnect("main")):(this.isReconnecting_||sf({eventType:Ys,code:fh.UNKNOWN}),e.target==this.socket_?(this.unbindAndCloseSocket("main"),this.reconnect("backup")):(this.unbindAndCloseSocket("backup"),this.reconnect("main"))),this.isConnecting_=!1,this.isConnected_=!1}},{key:"onmessage",value:function(e){var t=this;if(this.isConnected_){var n=JSON.parse(e.data),r=n.cmd,i=n.content,o=Object.values(qp),a=Object.keys(qp)[o.indexOf(r)],s=Kp[a];if(r!==qp.UPDATE_REMOTE_MUTE_STAT&&r!==qp.UPLINK_NETWORK_STATS&&r!==qp.PONG){var c=e.target==this.socket_?this.url_:this.backupUrl_;if(Hn.debug("[".concat(this.userId_,"] websocket[").concat(c,"] received message: ").concat(e.data)),Hn.info("[".concat(this.userId_,"] Received event: [ ").concat(s||"unknown cmd: "+r," ]")),(s===Kp.UPDATE_REMOTE_SDP||s===Kp.UPDATE_AUDIO_SSRC||s===Kp.UPDATE_VIDEO_SSRC)&&i.offersdp)try{var u=JSON.parse(i.offersdp),d=u.audiossrc,l=u.videossrc,p=u.rtxssrc;Hn.info("[".concat(this.userId_,"] ssrc info in offersdp: [ audiossrc: ").concat(d," videossrc: ").concat(l," rtxssrc: ").concat(p," ]"))}catch(m){}}switch(r){case qp.CHANNEL_SETUP_SUCCESS:this.signalInfo_.relayIp=i.relayip,this.signalInfo_.relayInnerIp=i.innerip,this.signalInfo_.signalIp=i.signalip,this.signalInfo_.localIp=i.localip,this.signalInfo_.dataPort=i.dataport,this.signalInfo_.stunPort=i.stunport,this.signalInfo_.checkSigSeq=i.checkSigSeq,this.signalInfo_.socketId=i.socketid,this.signalInfo_.tinyId=i.tinyid,this.signalInfo_.openId=i.openid,this.signalInfo_.stunPortList=i.stunportList,!i.stunportList||i.stunportList.length<=0?this.signalInfo_.stunServers="stun:"+i.relayip+":"+i.stunport:(this.signalInfo_.stunServers=[],i.stunportList.forEach(function(e){var n="stun:"+i.relayip+":"+e;t.signalInfo_.stunServers.push(n)})),i.cgiurl&&(this.signalInfo_.logCgiUrl=i.cgiurl),i.svrTime&&function(e){To=e-(new Date).getTime();var t=new Date;t.setTime(e),Hn.info("baseTime from server: "+t+" offset: "+To)}(i.svrTime),Hn.info("ChannelSetup Success: signalIp:".concat(i.signalip," relayIp:").concat(i.relayip," clientIp:").concat(i.localip," checkSigSeq:").concat(i.checkSigSeq)),Hn.info("start ping pong"),this.startPingPong(),this.isReconnecting_&&(this.reconnectionCount_=0,this.clearReconnectionTimer(),1===i.rc&&this.emitter_.emit(Kp.REQUEST_REBUILD_SESSION,{signalInfo:this.signalInfo_})),this.emitter_.emit(s,{signalInfo:this.signalInfo_});break;case qp.REBUILD_SESSION_RESULT:-1===i.result&&this.emitter_.emit(Kp.CLIENT_REJOIN);break;case qp.CHANNEL_SETUP_FAILED:if(!this.isReconnecting_){var h="sdkAppId invalid",f="";void 0!==i.errorCode&&(h=i.errorCode,f=i.errorMsg);var m=new vh({code:fh.SIGNAL_CHANNEL_SETUP_FAILED,message:"SignalChannel setup failure: ('errorCode': ".concat(h,", 'errorMsg': ").concat(f," })")});sf({eventType:Ys,error:m}),this.emitter_.emit(zp,m)}break;default:this.emitter_.emit(s,{data:n})}}}},{key:"addSignalEvent",value:function(e,t){Yh(this.userId_,{eventId:e,eventDesc:t,timestamp:Eo(),userId:this.userId_,tinyId:this.signalInfo_.tinyId})}},{key:"reconnect",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"main";if(this.isConnecting_||-1!==this.reconnectionTimer_)Hn.info("signal channel is reconnecting, ignoring current reconnection");else{if(this.reconnectionCount_>=30){Hn.warn("SDK has tried reconnect signal channel for ".concat(30," times, but all failed. please check your network"));var n=new vh({code:fh.SIGNAL_CHANNEL_RECONNECTION_FAILED,message:"signal channel reconnection failed, please check your network"});return sf({eventType:Zs,error:n}),this.addSignalEvent(Hh,"signal channel reconnect fail"),void this.emitter_.emit(zp,n)}this.isConnecting_=!0,this.reconnectionCount_++,this.currentState_!==Qp.RECONNECTING&&(this.emitter_.emit(Hp,{prevState:this.currentState_,state:Qp.RECONNECTING}),this.currentState_=Qp.RECONNECTING,this.addSignalEvent(Gh,"signal channel is reconnecting")),Hn.warn("reconnecting to ".concat(t," signal channel [").concat(this.reconnectionCount_,"/").concat(30,"]"));var r=this.getReconnectionUrl(t);"main"===t?(this.socket_=new WebSocket(r),this.bindSocket(this.socket_)):(this.backupSocket_=new WebSocket(r),this.bindSocket(this.backupSocket_));var i=gc(this.reconnectionCount_);this.reconnectionTimer_=setTimeout(function(){Hn.warn("reconnect ".concat(t," signal channel timeout(").concat(i/1e3,"s), close and try again")),e.isConnecting_=!1,e.clearReconnectionTimer(),e.unbindAndCloseSocket(t),e.reconnect("main"===t?"backup":"main")},i)}}},{key:"isConnected",value:function(){return this.isConnected_}},{key:"getReconnectionUrl",value:function(e){var t="main"===e?this.urlWithParam_:this.backupUrlWithParam_;return uf(this.signalInfo_)||-1!==t.indexOf("&rc=1")||(t+="&iip="+this.signalInfo_.relayInnerIp+"&dp="+this.signalInfo_.dataPort+"&oip="+this.signalInfo_.relayIp+"&sp="+this.signalInfo_.stunPort+"&rc=1"),t}},{key:"send",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);r.data=t,void 0!==n&&(r.srctinyid=n),this.socketInUse_.send(JSON.stringify(r))}}},{key:"sendWithoutUA",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e,!1);r.data=t,void 0!==n&&(r.srctinyid=n),this.socketInUse_.send(JSON.stringify(r))}}},{key:"sendWithReport",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0;if(this.isConnected_){var r=this.createSendMessage(e);r.data=t,r.report=n,this.socketInUse_.send(JSON.stringify(r))}}},{key:"startPingPong",value:(t=mt(regeneratorRuntime.mark(function e(){var t=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,-1===this.pingPongTimeoutId_){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,this.ping();case 5:this.pingPongTimeoutId_=setTimeout(function(){t.pingPongTimeoutId_=-1,t.startPingPong()},1e4),e.next=13;break;case 8:e.prev=8,e.t0=e.catch(0),Hn.warn("ping-pong failed, start signal reconnection"),this.close(),this.reconnect("main");case 13:case"end":return e.stop()}},e,this,[[0,8]])})),function(){return t.apply(this,arguments)})},{key:"stopPingPong",value:function(){Hn.info("stop ping pong"),clearTimeout(this.pingTimeoutId_),clearTimeout(this.pingPongTimeoutId_),this.pingTimeoutId_=-1,this.pingPongTimeoutId_=-1}},{key:"ping",value:function(){var e=this;return new Promise(function(t,n){if(-1!==e.pingTimeoutId_)return t();e.sendWithoutUA(rh),e.once(Kp.PONG,function(){clearTimeout(e.pingTimeoutId_),e.pingTimeoutId_=-1,t()}),e.pingTimeoutId_=setTimeout(function(){e.pingTimeoutId_=-1,n()},1e4)})}},{key:"createSendMessage",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n={tag_key:e,data:"",openid:this.userId_,tinyid:this.signalInfo_.tinyId,version:this.version_};return t&&(n.ua=navigator.userAgent),n}},{key:"getCurrentState",value:function(){return this.currentState_}},{key:"close",value:function(){Hn.info("close SignalChannel"),this.clearBackupTimer(),this.clearReconnectionTimer(),this.stopPingPong(),this.isConnecting_=!1,this.isConnected_=!1,this.socketInUse_=null,this.unbindAndCloseSocket("main"),this.unbindAndCloseSocket("backup")}},{key:"on",value:function(e,t,n){this.emitter_.on(e,t,n)}},{key:"removeListener",value:function(e,t,n){this.emitter_.removeListener(e,t,n)}},{key:"once",value:function(e,t,n){this.emitter_.once(e,t,n)}},{key:"off",value:function(e,t,n){this.emitter_.off(e,t,n)}},{key:"isReconnecting_",get:function(){return-1!==this.reconnectionTimer_}}]),e}();window.addEventListener("message",function(e){e.origin==window.location.origin&&function(e){if("PermissionDeniedError"==e){if(df)return df("PermissionDeniedError");throw new Error("PermissionDeniedError")}e.sourceId&&df&&df(e.sourceId,!0===e.canRequestAudioTrack)}(e.data)});window.InstallTrigger;var pf=!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0,hf=(window.chrome,{result:!1,detail:{isBrowserSupported:!1,isWebRTCSupported:!1,isMediaDevicesSupported:!1,isH264Supported:!1}}),ff=new Map([[ga,["Firefox",_a]],[ba,["Edg",ka]],[cs,["Chrome",ds]],[ls,["Safari",hs]],[Ca,["TBS",Ia]],[xa,["XWEB",Pa]],[Na&&ua,["WeChat",Ma]],[ja,["QQ(Win)",Fa]],[Ua,["QQ(Mobile)",Va]],[La,["QQ(Mobile X5)",Va]],[Ba,["QQ(Mac)",Ga]],[Wa,["QQ(iPad)",Ha]],[$a,["MI",Ya]],[Za,["HW",es]],[ts,["Samsung",ns]],[rs,["OPPO",is]],[os,["VIVO",as]],[ya,["EDGE",Sa]],[Ra,["SogouMobile",wa]],[Ta,["Sogou",Ea]]]);function mf(){var e="unknown",t="unknown";return ff.get(!0)&&(e=ff.get(!0)[0],t=ff.get(!0)[1]),{browserName:e,browserVersion:t}}var vf=function(){return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter(function(e){return e in window}).length>0},gf=function(){if(!navigator.mediaDevices)return!1;var e=["getUserMedia","enumerateDevices"];return e.filter(function(e){return e in navigator.mediaDevices}).length===e.length},_f=function(){var e=mt(regeneratorRuntime.mark(function e(){var t,n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!hf.detail.isH264Supported){e.next=2;break}return e.abrupt("return",hf.detail.isH264Supported);case 2:return"",t=!1,e.prev=4,n=new RTCPeerConnection,(r=document.createElement("canvas")).getContext("2d"),i=r.captureStream(0),n.addTrack(i.getVideoTracks()[0],i),e.next=12,n.createOffer();case 12:return-1!==e.sent.sdp.toLowerCase().indexOf("h264")&&(t=!0),n.close(),hf.detail.isH264Supported=t,e.abrupt("return",t);case 19:return e.prev=19,e.t0=e.catch(4),e.abrupt("return",!1);case 22:case"end":return e.stop()}},e,null,[[4,19]])}));return function(){return e.apply(this,arguments)}}(),yf=function(){var e=mt(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return"",t=!1,e.prev=2,n=new RTCPeerConnection,e.next=6,n.createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1});case 6:return-1!==e.sent.sdp.toLowerCase().indexOf("h264")&&(t=!0),n.close(),e.abrupt("return",t);case 12:return e.prev=12,e.t0=e.catch(2),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,null,[[2,12]])}));return function(){return e.apply(this,arguments)}}(),Sf=function(){var e=mt(regeneratorRuntime.mark(function e(){var t,n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!hf.result){e.next=2;break}return e.abrupt("return",hf);case 2:return t=!(Ka||ya||ba&&ka<80||ga&&_a<56),n=vf(),r=gf(),e.next=7,_f();case 7:return i=e.sent,hf.result=t&&n&&r&&i,hf.detail.isBrowserSupported=t,hf.detail.isWebRTCSupported=n,hf.detail.isMediaDevicesSupported=r,hf.detail.isH264Supported=i,hf.result||Hn.error("".concat(navigator.userAgent," isBrowserSupported: ").concat(t," isWebRTCSupported: ").concat(n," isMediaSupported: ").concat(r," isH264Supported: ").concat(i)),e.abrupt("return",hf);case 15:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}(),bf=function(){if(!Tf())return!1;if("undefined"==typeof RTCRtpTransceiver)return!1;if(!("currentDirection"in RTCRtpTransceiver.prototype))return!1;var e=new RTCPeerConnection,t=!1;try{e.addTransceiver("audio"),t=!0}catch(wv){}return e.close(),t},kf=function(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getDisplayMedia)},Rf=function(){return"RTCPeerConnection"in window&&"getReceivers"in window.RTCPeerConnection.prototype},wf=function(){return"RTCPeerConnection"in window&&"getSenders"in window.RTCPeerConnection.prototype},Tf=function(){return"RTCRtpTransceiver"in window&&"stop"in window.RTCRtpTransceiver.prototype},Ef=function(){return"RTCRtpSender"in window&&"setParameters"in window.RTCRtpSender.prototype&&wf()},Cf=function(){return!!Sc(navigator.mediaDevices)&&(Hn.error("navigator.mediaDevices is not supported on your browser"),!0)},If=function(){return"http:"===location.protocol&&!ms&&(Hn.error("not supported in http protocol, please use https protocol"),!0)},xf=new Map([[ha,"Android"],[la,"iOS"],[Ja,"Windows"],[za,"MacOS"],[Qa,"Linux"]]),Pf=function(){var e="unknown";return xf.get(!0)&&(e=xf.get(!0)),e};function Af(){var e="";screen.width&&(e+=(screen.width?screen.width*window.devicePixelRatio:"")+" * "+(screen.height?screen.height*window.devicePixelRatio:""));return e}function Of(){var e=!1;return navigator.getUserMedia?e=!0:navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&(e=!0),e}function Df(){for(var e={isSupported:!1},t=["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"],n=0;n<t.length;n++)if(t[n]in window){e.isSupported=!0;break}return e.isSupported}Sf();var Nf="stream-added",Mf="stream-removed",Lf="stream-updated",Uf="stream-subscribed",Vf="error",jf="connection-state-changed",Ff="stream-added",Bf="stream-removed",Gf="stream-updated",Wf="stream-subscribed",Hf="connection-state-changed",Jf="peer-join",zf="peer-leave",Qf="mute-audio",qf="mute-video",Kf="unmute-audio",Xf="unmute-video",$f="client-banned",Yf="network-quality",Zf="error",em="player-state-changed",tm="screen-sharing-stopped",nm="error",rm="player-state-changed",im=function(){function e(t){vt(this,e),this.prevReport_={}}var t,n,r,i;return _t(e,[{key:"getSenderStats",value:(i=mt(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={audio:{bytesSent:0,packetsSent:0,audioLevel:0,totalAudioEnergy:0},video:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0},rtt:0},!(r=t.getPeerConnection())){e.next=13;break}return e.prev=3,e.next=6,r.getStats();case 6:e.sent.forEach(function(e){if("outbound-rtp"===e.type)if("video"===e.mediaType){if(!ga&&void 0===e.trackId)return;n.video.bytesSent=e.bytesSent,n.video.packetsSent=e.packetsSent,n.video.framesEncoded=e.framesEncoded}else"audio"===e.mediaType&&(n.audio.bytesSent=e.bytesSent,n.audio.packetsSent=e.packetsSent);else"candidate-pair"===e.type?"number"==typeof e.currentRoundTripTime&&(n.rtt=1e3*e.currentRoundTripTime):"track"===e.type?(void 0!==e.frameWidth&&(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight,n.video.framesSent=e.framesSent),void 0!==e.audioLevel&&(n.audio.audioLevel=e.audioLevel||0)):"media-source"===e.type&&"audio"===e.kind&&(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0)}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),Hn.warn("failed to getStats on sender connection");case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}},e,null,[[3,10]])})),function(e){return i.apply(this,arguments)})},{key:"getReceiverStats",value:(r=mt(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={tinyId:t.getTinyId(),userId:t.getUserId(),rtt:0,hasAudio:!1,hasVideo:!1,hasAuxiliary:!1,audio:{bytesReceived:0,packetsReceived:0,packetsLost:0,jitter:0,audioLevel:0,totalAudioEnergy:0},video:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesDecoded:0,frameWidth:0,frameHeight:0},auxiliary:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesDecoded:0,frameWidth:0,frameHeight:0}},!(r=t.getPeerConnection())){e.next=13;break}return e.prev=3,e.next=6,r.getStats();case 6:e.sent.forEach(function(e){if("inbound-rtp"===e.type){if("audio"===e.mediaType)n.audio.packetsReceived=e.packetsReceived,n.audio.bytesReceived=e.bytesReceived,n.audio.packetsLost=e.packetsLost,n.audio.jitter=e.jitter,n.hasAudio=!0;else if("video"===e.mediaType){if(ga&&0===e.bytesReceived)return;var r=t.getSSRC();e.ssrc===r.video&&(n.video.packetsReceived=e.packetsReceived,n.video.bytesReceived=e.bytesReceived,n.video.packetsLost=e.packetsLost,n.video.framesDecoded=e.framesDecoded,n.hasVideo=!0),e.ssrc===r.auxiliary&&(n.auxiliary.packetsReceived=e.packetsReceived,n.auxiliary.bytesReceived=e.bytesReceived,n.auxiliary.packetsLost=e.packetsLost,n.auxiliary.framesDecoded=e.framesDecoded,n.hasAuxiliary=!0)}}else"track"===e.type?(void 0!==e.frameWidth&&(e.trackIdentifier===t.getMainStreamVideoTrackId()&&(n.video.frameWidth=e.frameWidth,n.video.frameHeight=e.frameHeight),e.trackIdentifier===t.getAuxStreamVideoTrackId()&&(n.auxiliary.frameWidth=e.frameWidth,n.auxiliary.frameHeight=e.frameHeight)),"audio"===e.kind&&(n.audio.audioLevel=e.audioLevel||0,n.audio.totalAudioEnergy=e.totalAudioEnergy||0)):"candidate-pair"===e.type&&"number"==typeof e.currentRoundTripTime&&(n.rtt=1e3*e.currentRoundTripTime)}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),Hn.warn("failed to getStats on receiver connection");case 13:return e.abrupt("return",n);case 14:case"end":return e.stop()}},e,null,[[3,10]])})),function(e){return r.apply(this,arguments)})},{key:"getStats",value:(n=mt(regeneratorRuntime.mark(function e(t,n){var r,i,o,a,s,c,u,d,l,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r={},!t){e.next=5;break}return e.next=4,this.getSenderStats(t);case 4:r=e.sent;case 5:i=[],o=!0,a=!1,s=void 0,e.prev=9,c=n[Symbol.iterator]();case 11:if(o=(u=c.next()).done){e.next=20;break}return(d=It(u.value,2))[0],l=d[1],e.next=15,this.getReceiverStats(l);case 15:p=e.sent,i.push(p);case 17:o=!0,e.next=11;break;case 20:e.next=26;break;case 22:e.prev=22,e.t0=e.catch(9),a=!0,s=e.t0;case 26:e.prev=26,e.prev=27,o||null==c.return||c.return();case 29:if(e.prev=29,!a){e.next=32;break}throw s;case 32:return e.finish(29);case 33:return e.finish(26);case 34:return e.abrupt("return",{senderStats:r,receiverStats:i});case 35:case"end":return e.stop()}},e,this,[[9,22,26,34],[27,,29,33]])})),function(e,t){return n.apply(this,arguments)})},{key:"prepareReport",value:function(e,t){uf(e.senderStats)||(t.uint32_delay=e.senderStats.rtt,t.RTTReportState.uint32_delay=e.senderStats.rtt,t.AudioReportState.sentAudioLevel=e.senderStats.audio.audioLevel,t.AudioReportState.sentAudioEnergy=e.senderStats.audio.totalAudioEnergy,t.AudioReportState.uint32_audio_enc_pkg_br=e.senderStats.audio.bytesSent,t.VideoReportState.uint32_video_snd_br=e.senderStats.video.bytesSent,t.VideoReportState.uint32_send_total_pkg=e.senderStats.video.packetsSent,t.VideoReportState.VideoEncState[0].uint32_enc_width=e.senderStats.video.frameWidth,t.VideoReportState.VideoEncState[0].uint32_enc_height=e.senderStats.video.frameHeight,t.VideoReportState.VideoEncState[0].uint32_enc_fps=e.senderStats.video.framesSent),e.receiverStats.forEach(function(e){t.RTTReportState.RTTDecState.push({uint32_delay:e.rtt,uint64_sender_uin:e.tinyId}),e.hasAudio&&(t.AudioReportState.AudioDecState.push({uint32_audio_delay:0,uint32_audio_jitter:e.audio.jitter,uint32_audio_real_recv_pkg:e.audio.packetsReceived,uint32_audio_flow:e.audio.bytesReceived,uint32_audio_real_recv_br:0,uint64_sender_uin:e.tinyId,packetsLost:e.audio.packetsLost,totalPacketsLost:e.audio.packetsLost,audioLevel:e.audio.audioLevel,audioEnergy:e.audio.totalAudioEnergy}),t.AudioReportState.uint32_audio_real_recv_pkg+=e.audio.packetsReceived,t.AudioReportState.uint32_audio_flow+=e.audio.bytesReceived,t.uint32_real_num+=e.audio.packetsReceived),e.hasVideo&&(t.VideoReportState.VideoDecState.push({uint32_video_recv_fps:e.video.framesDecoded,uint32_video_recv_br:e.video.bytesReceived,uint32_video_real_recv_pkg:e.video.packetsReceived,uint32_dec_height:e.video.frameHeight,uint32_dec_width:e.video.frameWidth,uint32_video_jitter:0,uint64_sender_uin:e.tinyId,packetsLost:e.video.packetsLost,totalPacketsLost:e.video.packetsLost,uint32_video_strtype:0}),t.VideoReportState.uint32_video_total_real_recv_pkg+=e.video.packetsReceived,t.VideoReportState.uint32_video_rcv_br+=e.video.bytesReceived),e.hasAuxiliary&&t.VideoReportState.VideoDecState.push({uint32_video_recv_fps:e.auxiliary.framesDecoded,uint32_video_recv_br:e.auxiliary.bytesReceived,uint32_video_real_recv_pkg:e.auxiliary.packetsReceived,uint32_dec_height:e.auxiliary.frameHeight,uint32_dec_width:e.auxiliary.frameWidth,uint32_video_jitter:0,uint64_sender_uin:e.tinyId,packetsLost:e.auxiliary.packetsLost,totalPacketsLost:e.auxiliary.packetsLost,uint32_video_strtype:2})}),t.uint64_end_utime=(new Date).getTime();var n=this.prevReport_;if(this.prevReport_=JSON.parse(JSON.stringify(t)),uf(n))t.AudioReportState.uint32_audio_enc_pkg_br=8*t.AudioReportState.uint32_audio_enc_pkg_br/2,t.VideoReportState.uint32_video_rcv_br=8*t.VideoReportState.uint32_video_rcv_br/2,t.VideoReportState.uint32_video_snd_br=8*t.VideoReportState.uint32_video_snd_br/2,t.VideoReportState.VideoDecState.forEach(function(e){e.uint32_video_recv_br=8*e.uint32_video_recv_br/2,t.uint32_total_send_bps=t.AudioReportState.uint32_audio_enc_pkg_br+t.VideoReportState.uint32_video_snd_br});else{t.uint64_begine_utime=n.uint64_end_utime,t.uint32_real_num-=n.uint32_real_num,t.uint32_real_num<=0&&(t.uint32_real_num=0),t.AudioReportState.uint32_audio_real_recv_pkg-=n.AudioReportState.uint32_audio_real_recv_pkg,t.AudioReportState.uint32_audio_real_recv_pkg<=0&&(t.AudioReportState.uint32_audio_real_recv_pkg=0),t.AudioReportState.uint32_audio_enc_pkg_br-=n.AudioReportState.uint32_audio_enc_pkg_br,t.AudioReportState.uint32_audio_enc_pkg_br<=0&&(t.AudioReportState.uint32_audio_enc_pkg_br=0),t.AudioReportState.uint32_audio_enc_pkg_br=8*t.AudioReportState.uint32_audio_enc_pkg_br/2,t.VideoReportState.uint32_video_snd_br-=n.VideoReportState.uint32_video_snd_br,t.VideoReportState.uint32_video_snd_br<=0&&(t.VideoReportState.uint32_video_snd_br=0),t.VideoReportState.uint32_video_snd_br=8*t.VideoReportState.uint32_video_snd_br/2,t.AudioReportState.uint32_audio_flow-=n.AudioReportState.uint32_audio_flow,t.AudioReportState.uint32_audio_flow<=0&&(t.AudioReportState.uint32_audio_flow=0),t.VideoReportState.uint32_send_total_pkg-=n.VideoReportState.uint32_send_total_pkg,t.VideoReportState.uint32_send_total_pkg<=0&&(t.VideoReportState.uint32_send_total_pkg=0),t.VideoReportState.uint32_video_rcv_br-=n.VideoReportState.uint32_video_rcv_br,t.VideoReportState.uint32_video_rcv_br<=0&&(t.VideoReportState.uint32_video_rcv_br=0),t.VideoReportState.uint32_video_rcv_br=8*t.VideoReportState.uint32_video_rcv_br/2,t.VideoReportState.uint32_video_total_real_recv_pkg-=n.VideoReportState.uint32_video_total_real_recv_pkg,t.VideoReportState.uint32_video_total_real_recv_pkg<=0&&(t.VideoReportState.uint32_video_total_real_recv_pkg=0),t.VideoReportState.VideoEncState[0].uint32_enc_fps-=n.VideoReportState.VideoEncState[0].uint32_enc_fps,t.VideoReportState.VideoEncState[0].uint32_enc_fps<0&&(t.VideoReportState.VideoEncState[0].uint32_enc_fps=0),t.VideoReportState.VideoEncState[0].uint32_enc_fps=t.VideoReportState.VideoEncState[0].uint32_enc_fps/2;for(var r=t.VideoReportState.VideoDecState.length,i=0;i<r;i++){for(var o=t.VideoReportState.VideoDecState[i],a=o.uint64_sender_uin,s=o.uint32_video_strtype,c=o.uint32_video_real_recv_pkg,u=o.uint32_video_recv_br,d=o.uint32_video_recv_fps,l=0;l<n.VideoReportState.VideoDecState.length;l++){var p=n.VideoReportState.VideoDecState[l];if(p.uint64_sender_uin===a&&p.uint32_video_strtype===s){o.packetsLost=o.totalPacketsLost-p.totalPacketsLost,(c-=p.uint32_video_real_recv_pkg)<=0&&(c=0),(u-=p.uint32_video_recv_br)<=0&&(u=0),(d-=p.uint32_video_recv_fps)<0&&(d=0);break}}t.VideoReportState.VideoDecState[i].uint32_video_real_recv_pkg=c,t.VideoReportState.VideoDecState[i].uint32_video_recv_br=8*u/2,t.VideoReportState.VideoDecState[i].uint32_video_recv_fps=d/2}r=t.AudioReportState.AudioDecState.length;for(var h=0;h<r;h++){for(var f=t.AudioReportState.AudioDecState[h],m=f.uint32_audio_real_recv_pkg,v=f.uint32_audio_flow,g=f.uint64_sender_uin,_=0;_<n.AudioReportState.AudioDecState.length;_++){var y=n.AudioReportState.AudioDecState[_];if(y.uint64_sender_uin===g){f.packetsLost=f.totalPacketsLost-y.totalPacketsLost,(m-=y.uint32_audio_real_recv_pkg)<=0&&(m=0),(v-=y.uint32_audio_flow)<=0&&(v=0);break}}t.AudioReportState.AudioDecState[h].uint32_audio_real_recv_pkg=m,t.AudioReportState.AudioDecState[h].uint32_audio_flow=v,t.AudioReportState.AudioDecState[h].uint32_audio_real_recv_br=8*v/2}t.AudioReportState.uint32_audio_real_recv_br=8*t.AudioReportState.uint32_audio_flow/2,t.uint32_real_num=t.AudioReportState.uint32_audio_real_recv_pkg+t.VideoReportState.uint32_video_total_real_recv_pkg,t.uint32_total_send_bps=t.AudioReportState.uint32_audio_enc_pkg_br+t.VideoReportState.uint32_video_snd_br,t.uint32_total_recv_bps=t.AudioReportState.uint32_audio_real_recv_br+t.VideoReportState.uint32_video_rcv_br}return t}},{key:"getStatsReport",value:(t=mt(regeneratorRuntime.mark(function e(t,n){var r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r={uint64_begine_utime:(new Date).getTime(),uint64_end_utime:0,uint32_real_num:0,uint32_delay:0,uint32_CPU_curfreq:0,uint32_total_send_bps:0,uint32_total_recv_bps:0,AudioReportState:{uint32_audio_enc_pkg_br:0,uint32_audio_real_recv_pkg:0,uint32_audio_flow:0,uint32_audio_real_recv_br:0,uint32_audio_delay:0,uint32_audio_jitter:0,uint32_microphone_status:1,sentAudioLevel:0,sentAudioEnergy:0,AudioDecState:[]},VideoReportState:{uint32_video_delay:0,uint32_video_snd_br:0,uint32_video_total_real_recv_pkg:0,uint32_video_rcv_br:0,uint32_send_total_pkg:0,VideoEncState:[{uint32_enc_width:0,uint32_enc_height:0,uint32_capture_fps:0,uint32_enc_fps:0}],VideoDecState:[]},RTTReportState:{uint32_delay:0,RTTDecState:[]}},e.next=3,this.getStats(t,n);case 3:return i=e.sent,this.prepareReport(i,r),e.abrupt("return",r);case 6:case"end":return e.stop()}},e,this)})),function(e,n){return t.apply(this,arguments)})}]),e}(),om=function(){function e(t){vt(this,e),this.id_=t.id,this.direction_=t.direction,this.type_=t.type,this.directionPrefix_="local"===this.direction_?"":"*"}return _t(e,[{key:"log",value:function(e,t){Hn[e]("[".concat(this.directionPrefix_).concat(this.id_,"] ").concat(this.type_," ").concat(t))}},{key:"info",value:function(e){this.log("info",e)}},{key:"debug",value:function(e){this.log("debug",e)}},{key:"warn",value:function(e){this.log("warn",e)}},{key:"error",value:function(e){this.log("error",e)}}]),e}(),am=function(){function e(t){var n=t.signalChannel,r=t.connections,i=t.userId;vt(this,e),this.signalChannel_=n,this.connections_=r,this.log_=new om({id:"q|"+i,direction:"local",type:""}),this.uplinkConnection_=null,this.uplinkNetworkQuality_=Os,this.uplinkRTT_=0,this.uplinkLoss_=0,this.downlinkNetworkQuality_=Os,this.downlinkPrevStatMap_=new Map,this.downlinkLossAndRTTMap_=new Map,this.interval_=-1,this.emitter_=new Wp,this.initialize()}var t,n;return _t(e,[{key:"initialize",value:function(){var e=this;this.signalChannel_.on(Kp.UPLINK_NETWORK_STATS,function(t){e.handleUplinkNetworkQuality(t)}),this.signalChannel_.on(Hp,this.handleSignalConnectionStateChange.bind(this)),this.start()}},{key:"handleUplinkNetworkQuality",value:function(e){if(!this.uplinkConnection_)return this.uplinkNetworkQuality=Os,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var t=this.uplinkConnection_.getPeerConnection();if(t&&this.isPeerConnectionDisconnected(t))return this.uplinkNetworkQuality=6,this.uplinkLoss_=0,void(this.uplinkRTT_=0);var n=e.data.content;if(0===n.result){var r=n.expectAudPkg+n.expectVidPkg,i=n.recvAudPkg+n.recvVidPkg,o=r-i;if(0===r&&0===i)return;this.uplinkLoss_=o<=0?0:Math.round(o/r*100),this.uplinkRTT_=n.rtt,this.uplinkNetworkQuality=this.getNetworkQuality(this.uplinkLoss_,this.uplinkRTT_)}}},{key:"handleDownlinkNetworkQuality",value:(n=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u,d,l,p,h,f,m=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.connections_&&0!==this.connections_.size){e.next=3;break}return this.downlinkNetworkQuality=Os,e.abrupt("return");case 3:if((t=xt(this.connections_.values()).map(function(e){return e.getPeerConnection()}).filter(function(e){return!!e})).filter(this.isPeerConnectionDisconnected).length!==t.length){e.next=8;break}return this.downlinkNetworkQuality=6,e.abrupt("return");case 8:n=t.filter(function(e){return"connected"===e.connectionState}),r=0;case 10:if(!(r<n.length)){e.next=30;break}return e.next=13,this.getStat(n[r]);case 13:if(i=e.sent,o=i.rtt,a=i.totalPacketsLost,s=i.totalPacketsReceived,this.downlinkPrevStatMap_.has(n[r])){e.next=20;break}return this.downlinkPrevStatMap_.set(n[r],{totalPacketsLost:a,totalPacketsReceived:s}),e.abrupt("continue",27);case 20:c=0,u=this.downlinkPrevStatMap_.get(n[r]),d=a-u.totalPacketsLost,l=s-u.totalPacketsReceived,c=d<=0||l<0?0:Math.round(d/(d+l)*100),this.downlinkPrevStatMap_.set(n[r],{totalPacketsLost:a,totalPacketsReceived:s}),this.downlinkLossAndRTTMap_.set(n[r],{rtt:o,loss:c});case 27:r++,e.next=10;break;case 30:if(xt(this.downlinkPrevStatMap_.keys()).forEach(function(e){m.isPeerConnectionDisconnected(e)&&(m.downlinkPrevStatMap_.delete(e),m.downlinkLossAndRTTMap_.delete(e))}),0!==this.downlinkLossAndRTTMap_.size){e.next=33;break}return e.abrupt("return");case 33:p=this.getAverageLossAndRTT(xt(this.downlinkLossAndRTTMap_.values())),h=p.rtt,f=p.loss,this.downlinkNetworkQuality=this.getNetworkQuality(f,h);case 35:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"getStat",value:(t=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={rtt:0,totalPacketsLost:0,totalPacketsReceived:0},t&&Rf()){e.next=3;break}return e.abrupt("return",n);case 3:r=t.getReceivers(),e.prev=4,i=0;case 6:if(!(i<r.length)){e.next=15;break}return o=r[i],e.next=10,o.getStats();case 10:e.sent.forEach(function(e){"candidate-pair"===e.type&&"number"==typeof e.currentRoundTripTime&&(n.rtt=Math.round(1e3*e.currentRoundTripTime)),"inbound-rtp"!==e.type||"audio"!==e.mediaType&&"video"!==e.mediaType||(n.totalPacketsLost+=e.packetsLost,n.totalPacketsReceived+=e.packetsReceived)});case 12:i++,e.next=6;break;case 15:return e.abrupt("return",n);case 18:return e.prev=18,e.t0=e.catch(4),e.abrupt("return",n);case 21:case"end":return e.stop()}},e,null,[[4,18]])})),function(e){return t.apply(this,arguments)})},{key:"getAverageLossAndRTT",value:function(e){var t={rtt:0,loss:0};return Array.isArray(e)&&e.length>0&&(e.forEach(function(e){t.rtt+=e.rtt,t.loss+=e.loss}),Object.keys(t).forEach(function(n){t[n]=Math.round(t[n]/e.length)})),t}},{key:"getNetworkQuality",value:function(e,t){return e>50||t>500?5:e>30||t>350?4:e>20||t>200?3:e>10||t>100?2:e>=0||t>=0?1:Os}},{key:"handleSignalConnectionStateChange",value:function(e){e.state===Qp.DISCONNECTED?(this.uplinkRTT_=0,this.uplinkLoss_=0,this.uplinkNetworkQuality=6):e.state===Qp.CONNECTED&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"handleUplinkConnectionStateChange",value:function(e){var t=e.state;t===Ds.DISCONNECTED?(this.uplinkLoss_=0,this.uplinkRTT_=0,this.uplinkNetworkQuality=6):t===Ds.CONNECTED&&6===this.uplinkNetworkQuality&&(this.uplinkNetworkQuality=5)}},{key:"isPeerConnectionDisconnected",value:function(e){return!(!e||"disconnected"!==e.connectionState&&"failed"!==e.connectionState&&"closed"!==e.connectionState)}},{key:"setUplinkConnection",value:function(e){this.uplinkConnection_=e,this.uplinkConnection_?this.uplinkConnection_.on(jf,this.handleUplinkConnectionStateChange.bind(this)):(this.uplinkNetworkQuality=Os,this.uplinkRTT_=0,this.uplinkLoss_=0)}},{key:"start",value:function(){var e=this;-1===this.interval_?(this.log_.info("start network quality calculating"),this.interval_=setInterval(function(){e.handleDownlinkNetworkQuality(),e.emitter_.emit(Yf,{uplinkNetworkQuality:e.uplinkNetworkQuality,downlinkNetworkQuality:e.downlinkNetworkQuality})},2e3)):this.log_.info("network quality calculating is already started")}},{key:"stop",value:function(){this.log_.info("stop network quality calculating"),-1!==this.interval_&&(clearInterval(this.interval_),this.interval_=-1)}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"uplinkNetworkQuality",get:function(){return this.uplinkNetworkQuality_},set:function(e){e!==this.uplinkNetworkQuality_&&this.log_.info("uplink network quality change ".concat(this.uplinkNetworkQuality," -> ").concat(e,", rtt: ").concat(this.uplinkRTT_,", loss: ").concat(this.uplinkLoss_)),this.uplinkNetworkQuality_=e}},{key:"downlinkNetworkQuality",get:function(){return this.downlinkNetworkQuality_},set:function(e){if(e!==this.downlinkNetworkQuality_){var t=this.getAverageLossAndRTT(xt(this.downlinkLossAndRTTMap_.values())),n=t.rtt,r=t.loss;this.log_.info("downlink network quality change ".concat(this.downlinkNetworkQuality," -> ").concat(e,", rtt: ").concat(n,", loss: ").concat(r))}this.downlinkNetworkQuality_=e}}]),e}(),sm=function(){function e(){vt(this,e),this.log_=Hn,this.localStream_=null,this.prevDevices_=[],this.initialize()}var t,n,r;return _t(e,[{key:"initialize",value:(r=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:navigator.mediaDevices&&navigator.mediaDevices.addEventListener("devicechange",this.onDeviceChange.bind(this));case 1:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"onDeviceChange",value:(n=mt(regeneratorRuntime.mark(function e(){var t,n,r,i=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.localStream_&&this.localStream_.getMediaStream()&&!this.localStream_.getScreen()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Rv.getDevices();case 4:t=e.sent,n=t.filter(function(e){return i.prevDevices_.findIndex(function(t){var n=t.deviceId;return e.deviceId===n})<0}),r=this.prevDevices_.filter(function(e){return t.findIndex(function(t){var n=t.deviceId;return e.deviceId===n})<0}),n.length>0&&this.handleDeviceAdded(this.prevDevices_,n),r.length>0&&this.handleDeviceRemoved(t,r),this.prevDevices_=t;case 10:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"setLocalStream",value:(t=mt(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=4;break}return e.next=3,Rv.getDevices();case 3:this.prevDevices_=e.sent;case 4:this.localStream_=t;case 5:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"handleDeviceAdded",value:function(e,t){this.log_.warn("devicesAdded: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=t.filter(function(e){return"videoinput"===e.kind}),r=t.filter(function(e){return"audioinput"===e.kind}),i=e.filter(function(e){return"videoinput"===e.kind}),o=e.filter(function(e){return"audioinput"===e.kind}),a=n.length>0&&0===i.length&&this.localStream_.getVideo(),s=r.length>0&&0===o.length&&this.localStream_.getAudio();if(s&&a)return this.log_.info("new microphone and camera detected, but there was no device before."),void this.localStream_.updateStream({audio:!0,video:!0,cameraId:n[0].deviceId,microphoneId:r[0].deviceId});a&&(this.log_.info("new camera detected, but there was no camera before."),this.localStream_.updateStream({audio:!1,video:!0,cameraId:n[0].deviceId})),s&&(this.log_.info("new microphone detected, but there was no microphone before."),this.localStream_.updateStream({audio:!0,video:!1,microphoneId:r[0].deviceId}))}},{key:"handleDeviceRemoved",value:function(e,t){this.log_.warn("devicesRemoved: ".concat(JSON.stringify(t))),this.localStream_.updateDeviceIdInUse();var n=!1,r=!1,i=this.localStream_.getCameraId(),o=this.localStream_.getMicrophoneId();if("default"===o){var a=this.localStream_.getMicrophoneGroupId(),s=e.filter(function(e){return"default"===e.deviceId&&"audioinput"===e.kind})[0];s&&s.groupId!==a&&(r=!0)}if(t.forEach(function(e){var t=e.deviceId;i.length>0&&t===i?n=!0:o.length>0&&t===o&&(r=!0)}),n&&r)return this.log_.warn("current camera and microphone in use is lost, cameraId: ".concat(i,", microphoneId: ").concat(o)),void((this.localStream_.getAudio()||this.localStream_.getVideo())&&this.localStream_.updateStream({video:!0,audio:!0}));n&&(this.log_.warn("current camera in use is lost, deviceId: ".concat(i)),this.localStream_.getVideo()&&this.localStream_.updateStream({video:!0,audio:!1})),r&&(this.log_.warn("current microphone in use is lost, deviceId: ".concat(o)),this.localStream_.getAudio()&&this.localStream_.updateStream({video:!1,audio:!0}))}}]),e}(),cm=fe.includes;Ae({target:"Array",proto:!0},{includes:function(e){return cm(this,e,arguments.length>1?arguments[1]:void 0)}}),Xu("includes"),Ae({target:"String",proto:!0,forced:!kp("includes")},{includes:function(e){return!!~String(f(this)).indexOf(Sp(e),arguments.length>1?arguments[1]:void 0)}});var um=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})},dm=1..toFixed,lm=Math.floor,pm=function(e,t,n){return 0===t?n:t%2==1?pm(e,t-1,n*e):pm(e*e,t/2,n)},hm=dm&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!i(function(){dm.call({})});Ae({target:"Number",proto:!0,forced:hm},{toFixed:function(e){var t,n,r,i,o=function(e){if("number"!=typeof e&&"Number"!=l(e))throw TypeError("Incorrect invocation");return+e}(this),a=se(e),s=[0,0,0,0,0,0],c="",u="0",d=function(e,t){for(var n=-1,r=t;++n<6;)r+=e*s[n],s[n]=r%1e7,r=lm(r/1e7)},p=function(e){for(var t=6,n=0;--t>=0;)n+=s[t],s[t]=lm(n/e),n=n%e*1e7},h=function(){for(var e=6,t="";--e>=0;)if(""!==t||0===e||0!==s[e]){var n=String(s[e]);t=""===t?n:t+ar.call("0",7-n.length)+n}return t};if(a<0||a>20)throw RangeError("Incorrect fraction digits");if(o!=o)return"NaN";if(o<=-1e21||o>=1e21)return String(o);if(o<0&&(c="-",o=-o),o>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(o*pm(2,69,1))-69)<0?o*pm(2,-t,1):o/pm(2,t,1),n*=4503599627370496,(t=52-t)>0){for(d(0,n),r=a;r>=7;)d(1e7,0),r-=7;for(d(pm(10,r,1),0),r=t-1;r>=23;)p(1<<23),r-=23;p(1<<r),d(1,1),p(2),u=h()}else d(0,n),d(1<<-t,0),u=h()+ar.call("0",a);return u=a>0?c+((i=u.length)<=a?"0."+ar.call("0",a-i)+u:u.slice(0,i-a)+"."+u.slice(i-a)):c+u}});var fm=window.AudioContext||window.webkitAudioContext,mm=null,vm=function(){function e(){var t=this;vt(this,e),mm||(mm=new fm),this.context_=mm,this.instant_=0,this.slow_=0,this.clip_=0,this.script_=this.context_.createScriptProcessor(2048,1,1),this.script_.onaudioprocess=function(e){var n,r=e.inputBuffer.getChannelData(0),i=0,o=0;for(n=0;n<r.length;++n)i+=r[n]*r[n],Math.abs(r[n])>.99&&(o+=1);t.instant_=Math.sqrt(i/r.length),t.slow_=.95*t.slow_+.05*t.instant_,t.clip_=o/r.length}}return _t(e,[{key:"connectToSource",value:function(e,t){try{var n=new MediaStream;n.addTrack(e),this.mic_=this.context_.createMediaStreamSource(n),this.mic_.connect(this.script_),this.script_.connect(this.context_.destination),void 0!==t&&t(null)}catch(wv){Hn.error("soundMeter connectToSource error: "+wv),void 0!==t&&t(wv)}}},{key:"stop",value:function(){this.mic_.disconnect(),this.script_.disconnect()}},{key:"resume",value:function(){this.context_&&this.context_.resume()}},{key:"getVolume",value:function(){return this.instant_.toFixed(2)}}]),e}(),gm=function(){function e(t){vt(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getIDLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.outputDeviceId_=t.outputDeviceId,this.volume_=t.volume,this.emitter_=new Wp,this.initializeElement(),this.state_="NONE",this.soundMeter_=null}var t,n,r;return _t(e,[{key:"initializeElement",value:function(){var e=new MediaStream;e.addTrack(this.track_);var t=document.createElement("audio");t.srcObject=e,t.muted=this.muted_,t.setAttribute("id","audio_".concat(this.stream_.getId())),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.div_.appendChild(t),this.element_=t,this.handleEvents()}},{key:"play",value:(r=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.outputDeviceId_){e.next=3;break}return e.next=3,this.element_.setSinkId(this.outputDeviceId_);case 3:return this.setVolume(this.volume_),e.prev=4,e.next=7,this.element_.play();case 7:e.next=15;break;case 9:if(e.prev=9,e.t0=e.catch(4),this.log_.warn("<audio> play() error: "+e.t0),!(t=e.t0.toString()+" <audio>").startsWith("NotAllowedError")){e.next=15;break}throw new vh({code:fh.PLAY_NOT_ALLOWED,message:t});case 15:case"end":return e.stop()}},e,this,[[4,9]])})),function(){return r.apply(this,arguments)})},{key:"handleEvents",value:function(){this.handleElementEvent=this.handleElementEvent.bind(this),this.handleTrackEvent=this.handleTrackEvent.bind(this),this.element_.addEventListener("playing",this.handleElementEvent),this.element_.addEventListener("ended",this.handleElementEvent),this.element_.addEventListener("pause",this.handleElementEvent),this.track_.addEventListener("ended",this.handleTrackEvent),this.track_.addEventListener("mute",this.handleTrackEvent),this.track_.addEventListener("unmute",this.handleTrackEvent)}},{key:"handleElementEvent",value:function(e){switch(e.type){case"playing":this.log_.info("stream - audio player is starting playing"),this.state_="PLAYING",this.emitter_.emit(rm,{state:this.state_,reason:"playing"});break;case"ended":this.log_.info("stream - audio player is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(rm,{state:this.state_,reason:"ended"}));break;case"pause":this.log_.info("stream - audio player is paused"),this.state_="PAUSED",this.emitter_.emit(rm,{state:this.state_,reason:"pause"})}}},{key:"handleTrackEvent",value:function(e){switch(e.type){case"ended":this.log_.info("stream - audio player track is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(rm,{state:this.state_,reason:"ended"}));break;case"mute":this.log_.info("stream - audio track is muted"),"PAUSED"!==this.state_&&(this.state_="PAUSED",this.emitter_.emit(rm,{state:this.state_,reason:"mute"}));break;case"unmute":this.log_.info("stream - audio track is unmuted"),"PAUSED"===this.state_&&(this.state_="PLAYING",this.emitter_.emit(rm,{state:this.state_,reason:"unmute"}))}}},{key:"unbindEvents",value:function(){this.element_&&(this.element_.removeEventListener("playing",this.handleElementEvent),this.element_.removeEventListener("ended",this.handleElementEvent),this.element_.removeEventListener("pause",this.handleElementEvent)),this.track_&&(this.track_.removeEventListener("ended",this.handleTrackEvent),this.track_.removeEventListener("mute",this.handleTrackEvent),this.track_.removeEventListener("unmute",this.handleTrackEvent))}},{key:"setSinkId",value:(n=mt(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.outputDeviceId_===t){e.next=4;break}return e.next=3,this.element_.setSinkId(t);case 3:this.outputDeviceId_=t;case 4:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"setVolume",value:function(e){this.log_.info("stream - audioElement setVolume to : ".concat(e)),this.element_.volume=e}},{key:"getAudioLevel",value:function(){return this.soundMeter_||(this.soundMeter_=new vm,this.soundMeter_.connectToSource(this.track_)),this.soundMeter_.getVolume()}},{key:"stop",value:function(){this.unbindEvents(),this.div_.removeChild(this.element_),this.element_.srcObject=null,this.soundMeter_&&(this.soundMeter_.stop(),this.soundMeter_=null),this.element_=null}},{key:"resume",value:(t=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.soundMeter_&&this.soundMeter_.resume(),e.next=4,this.element_.play();case 4:e.next=12;break;case 6:if(e.prev=6,e.t0=e.catch(0),this.log_.warn("<audio> play() error: "+e.t0),!(t=e.t0.toString()+" <audio>").startsWith("NotAllowedError")){e.next=12;break}throw new vh({code:fh.PLAY_NOT_ALLOWED,message:t});case 12:case"end":return e.stop()}},e,this,[[0,6]])})),function(){return t.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),_m=function(){function e(t){vt(this,e),this.stream_=t.stream,this.userId_=t.stream.getUserId(),this.log_=this.stream_.getIDLogger(),this.track_=t.track,this.div_=t.div,this.muted_=t.muted,this.objectFit_=t.objectFit,this.mirror_=t.mirror,this.emitter_=new Wp,this.initializeElement(),this.state_="NONE"}var t,n;return _t(e,[{key:"initializeElement",value:function(){var e=new MediaStream;e.addTrack(this.track_);var t=document.createElement("video");t.srcObject=e,t.muted=!0;var n="width: 100%; height: 100%; object-fit: ".concat(this.objectFit_,";");this.mirror_&&(n+="transform: rotateY(180deg);"),t.setAttribute("id","video_".concat(this.stream_.getId())),t.setAttribute("style",n),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.div_.appendChild(t),this.element_=t,this.handleEvents()}},{key:"play",value:(n=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<video> play() error: "+e.t0),!(t=e.t0.toString()+" <video>").startsWith("NotAllowedError")){e.next=11;break}throw new vh({code:fh.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}},e,this,[[0,5]])})),function(){return n.apply(this,arguments)})},{key:"handleEvents",value:function(){this.handleElementEvent=this.handleElementEvent.bind(this),this.handleTrackEvent=this.handleTrackEvent.bind(this),this.element_.addEventListener("playing",this.handleElementEvent),this.element_.addEventListener("ended",this.handleElementEvent),this.element_.addEventListener("pause",this.handleElementEvent),this.track_.addEventListener("ended",this.handleTrackEvent),this.track_.addEventListener("mute",this.handleTrackEvent),this.track_.addEventListener("unmute",this.handleTrackEvent)}},{key:"handleElementEvent",value:function(e){switch(e.type){case"playing":this.log_.info("stream - video player is starting playing"),this.state_="PLAYING",this.emitter_.emit(rm,{state:this.state_,reason:"playing"});break;case"ended":this.log_.info("stream - video player is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(rm,{state:this.state_,reason:"ended"}));break;case"pause":this.log_.info("stream - video player is paused"),this.state_="PAUSED",this.emitter_.emit(rm,{state:this.state_,reason:"pause"})}}},{key:"handleTrackEvent",value:function(e){switch(e.type){case"ended":this.log_.info("stream - video player track is ended"),"STOPPED"!==this.state_&&(this.state_="STOPPED",this.emitter_.emit(rm,{state:this.state_,reason:"ended"}));break;case"mute":this.log_.info("stream - video track is muted"),"PAUSED"!==this.state_&&(this.state_="PAUSED",this.emitter_.emit(rm,{state:this.state_,reason:"mute"}));break;case"unmute":this.log_.info("stream - video track is unmuted"),"PAUSED"===this.state_&&(this.state_="PLAYING",this.emitter_.emit(rm,{state:this.state_,reason:"unmute"}))}}},{key:"unbindEvents",value:function(){this.element_&&(this.element_.removeEventListener("playing",this.handleElementEvent),this.element_.removeEventListener("ended",this.handleElementEvent),this.element_.removeEventListener("pause",this.handleElementEvent)),this.track_&&(this.track_.removeEventListener("ended",this.handleTrackEvent),this.track_.removeEventListener("mute",this.handleTrackEvent),this.track_.removeEventListener("unmute",this.handleTrackEvent))}},{key:"stop",value:function(){this.unbindEvents(),this.div_.removeChild(this.element_),this.element_.srcObject=null,this.element_=null}},{key:"resume",value:(t=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.element_.play();case 3:e.next=11;break;case 5:if(e.prev=5,e.t0=e.catch(0),this.log_.warn("<video> play() error: "+e.t0),!(t=e.t0.toString()+" <video>").startsWith("NotAllowedError")){e.next=11;break}throw new vh({code:fh.PLAY_NOT_ALLOWED,message:t});case 11:case"end":return e.stop()}},e,this,[[0,5]])})),function(){return t.apply(this,arguments)})},{key:"getVideoFrame",value:function(){var e=document.createElement("canvas");return e.width=this.element_.videoWidth,e.height=this.element_.videoHeight,e.getContext("2d").drawImage(this.element_,0,0),e.toDataURL("image/png")}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),ym=new Wp,Sm=100,bm=120,km=function(){function e(t){vt(this,e),this.userId_=t.userId,this.isRemote_=t.isRemote,this.type_=t.type,this.log_=new om({id:"s|"+this.userId_,direction:this.isRemote_?"remote":"local",type:this.isRemote_?this.type_:""}),this.mirror_=!1,this.isRemote_||(this.mirror_=!0),void 0!==t.mirror&&(this.mirror_=t.mirror),this.client_=null,void 0!==t.client&&(this.client_=t.client),this.mediaStream_=null,this.div_=null,this.isPlaying_=!1,this.connection_=null,this.audioPlayer_=null,this.videoPlayer_=null,this.muted_=!1,this.objectFit_="cover",this.id_=um(),this.audioOutputDeviceId_=0,this.audioVolume_=1,this.emitter_=new Wp}var t,n,r,i,o;return _t(e,[{key:"getType",value:function(){return this.type_}},{key:"getIDLogger",value:function(){return this.log_}},{key:"setConnection",value:function(e){this.connection_!==e&&(this.connection_=e)}},{key:"getConnection",value:function(){return this.connection_}},{key:"play",value:(o=mt(regeneratorRuntime.mark(function e(t,n){var r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isPlaying_){e.next=2;break}throw new vh({code:fh.INVALID_OPERATION,message:"duplicated play() call observed, please stop() firstly"});case 2:if(this.isPlaying_=!0,this.log_.info("stream start to play with options: ".concat(JSON.stringify(n))),(r=document.createElement("div")).setAttribute("id","player_".concat(this.id_)),r.setAttribute("style","width: 100%; height: 100%; position: relative; background-color: black; overflow: hidden;"),i=t,"object"!==ht(t)&&(i=document.getElementById(t)),i.appendChild(r),this.div_=r,this.isRemote_||(this.muted_=!0),n&&void 0!==n.muted&&(this.muted_=n.muted),this.isRemote_&&"auxiliary"===this.getType()&&(this.objectFit_="contain"),n&&void 0!==n.objectFit&&(this.objectFit_=n.objectFit),!this.isRemote_){e.next=41;break}if(o=[],a=this.getSubscribedState(),!this.hasVideo()||!a.video){e.next=27;break}return e.prev=19,e.next=22,this.playVideo();case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(19),o.push(e.t0);case 27:if(!this.hasAudio()||!a.audio){e.next=36;break}return e.prev=28,e.next=31,this.playAudio();case 31:e.next=36;break;case 33:e.prev=33,e.t1=e.catch(28),o.push(e.t1);case 36:if(!(o.length>0)){e.next=39;break}throw new vh({code:fh.PLAY_NOT_ALLOWED,message:"NotAllowedError: autoplay is not allowed in the current browser, refer to https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-11-advanced-auto-play-policy.html"});case 39:e.next=47;break;case 41:if(!this.hasVideo()){e.next=44;break}return e.next=44,this.playVideo();case 44:if(!this.hasAudio()){e.next=47;break}return e.next=47,this.playAudio();case 47:ym.emit(Sm,{stream:this});case 48:case"end":return e.stop()}},e,this,[[19,24],[28,33]])})),function(e,t){return o.apply(this,arguments)})},{key:"playAudio",value:(i=mt(regeneratorRuntime.mark(function e(){var t,n=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getAudioTrack(),this.audioPlayer_||!t){e.next=14;break}return this.log_.info("stream - create AudioPlayer and play"),this.audioPlayer_=new gm({stream:this,track:t,div:this.div_,muted:this.muted_,outputDeviceId:this.audioOutputDeviceId_,volume:this.audioVolume_}),this.audioPlayer_.on(rm,function(e){n.emitter_.emit(em,{type:"audio",state:e.state,reason:e.reason})}),e.prev=5,e.next=8,this.audioPlayer_.play();case 8:e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(5),this.emitter_.emit(nm,e.t0),e.t0;case 14:case"end":return e.stop()}},e,this,[[5,10]])})),function(){return i.apply(this,arguments)})},{key:"playVideo",value:(r=mt(regeneratorRuntime.mark(function e(){var t,n=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.getVideoTrack(),this.videoPlayer_||!t){e.next=14;break}return this.log_.info("stream - create VideoPlayer and play"),this.videoPlayer_=new _m({stream:this,track:t,div:this.div_,muted:this.muted_,objectFit:this.objectFit_,mirror:this.mirror_}),this.videoPlayer_.on(rm,function(e){n.emitter_.emit(em,{type:"video",state:e.state,reason:e.reason})}),e.prev=5,e.next=8,this.videoPlayer_.play();case 8:e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(5),this.emitter_.emit(nm,e.t0),e.t0;case 14:case"end":return e.stop()}},e,this,[[5,10]])})),function(){return r.apply(this,arguments)})},{key:"stopAudio",value:function(){this.audioPlayer_&&(this.log_.info("stream - stop AudioPlayer"),this.audioPlayer_.stop(),this.audioPlayer_=null)}},{key:"stopVideo",value:function(){this.videoPlayer_&&(this.log_.info("stream - stop VideoPlayer"),this.videoPlayer_.stop(),this.videoPlayer_=null)}},{key:"restartAudio",value:function(){this.isPlaying_&&(this.stopAudio(),this.playAudio().catch(function(e){}))}},{key:"restartVideo",value:function(){this.isPlaying_&&(this.stopVideo(),this.playVideo().catch(function(e){}))}},{key:"stop",value:function(){this.isPlaying_&&(this.isPlaying_=!1,this.stopAudio(),this.stopVideo(),this.div_.parentNode.removeChild(this.div_))}},{key:"resume",value:(n=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isPlaying_){e.next=2;break}return e.abrupt("return");case 2:if(this.log_.info("stream - resume"),!this.audioPlayer_){e.next=6;break}return e.next=6,this.audioPlayer_.resume();case 6:if(!this.videoPlayer_){e.next=9;break}return e.next=9,this.videoPlayer_.resume();case 9:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"close",value:function(){this.isPlaying_&&this.stop(),this.mediaStream_&&(this.mediaStream_.preventEvent=1,this.mediaStream_.getTracks().forEach(function(e){e.stop()}),this.mediaStream_=null)}},{key:"muteAudio",value:function(){return this.addRemoteEvent(!0,"audio"),this.doEnableTrack("audio",!1)}},{key:"muteVideo",value:function(){return this.addRemoteEvent(!0,"video"),this.doEnableTrack("video",!1)}},{key:"unmuteAudio",value:function(){return this.addRemoteEvent(!1,"audio"),this.doEnableTrack("audio",!0)}},{key:"unmuteVideo",value:function(){return this.addRemoteEvent(!1,"video"),this.doEnableTrack("video",!0)}},{key:"addRemoteEvent",value:function(e,t){if(this.isRemote_&&this.client_){var n=this.client_.getUserId(),r="".concat(e?"mute":"unmute"," remote ").concat(t);Yh(n,{eventId:"audio"===t?e?Dh:Mh:e?Oh:Nh,eventDesc:r,timestamp:(new Date).getTime(),userId:n,tinyId:this.client_.getTinyId(),remoteUserId:this.userId_,remoteTinyId:this.connection_.getTinyId()})}}},{key:"doEnableTrack",value:function(e,t){var n=!1;return"audio"===e?this.mediaStream_.getAudioTracks().forEach(function(e){n=!0,e.enabled=t}):this.mediaStream_.getVideoTracks().forEach(function(e){n=!0,e.enabled=t}),n}},{key:"getId",value:function(){return this.id_}},{key:"getUserId",value:function(){return this.userId_}},{key:"isPlaying",value:function(){return this.isPlaying_}},{key:"setAudioOutput",value:(t=mt(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.audioOutputDeviceId_=t,!this.audioPlayer_){e.next=4;break}return e.next=4,this.audioPlayer_.setSinkId(t);case 4:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"setAudioVolume",value:function(e){this.audioVolume_=e,this.log_.info("setAudioVolume to ".concat(e)),this.audioPlayer_&&this.audioPlayer_.setVolume(e)}},{key:"getAudioLevel",value:function(){var e=0;return this.audioPlayer_&&(e=this.audioPlayer_.getAudioLevel()),e}},{key:"hasAudio",value:function(){if(this.isRemote_){if(!this.connection_)return!1;var e=this.connection_.getTrackState();return"main"===this.type_&&e.audio}return!!this.getAudioTrack()}},{key:"hasVideo",value:function(){if(this.isRemote_){if(!this.connection_)return!1;var e=this.connection_.getTrackState();return"auxiliary"===this.type_?e.auxiliary:e.video}return!!this.getVideoTrack()}},{key:"getSubscribedState",value:function(){return this.isRemote_?this.connection_.getSubscribeState():null}},{key:"getAudioTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getAudioTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoTrack",value:function(){var e=null;if(this.mediaStream_){var t=this.mediaStream_.getVideoTracks();t.length>0&&(e=t[0])}return e}},{key:"getVideoFrame",value:function(){return this.videoPlayer_?this.videoPlayer_.getVideoFrame():null}},{key:"getMediaStream",value:function(){return this.mediaStream_}},{key:"setMediaStream",value:function(e){e!==this.mediaStream_&&(this.mediaStream_&&this.mediaStream_.getTracks().forEach(function(e){return e.stop()}),this.mediaStream_=e)}},{key:"updateVideoPlayingState",value:function(e){this.isPlaying_&&(e?(this.log_.info("playing state updated, play video"),this.playVideo().catch(function(e){})):(this.log_.info("playing state updated, stop video"),this.stopVideo()))}},{key:"updateAudioPlayingState",value:function(e){this.isPlaying_&&(e?(this.log_.info("playing state updated, play audio"),this.playAudio().catch(function(e){})):(this.log_.info("playing state updated, stop audio"),this.stopAudio()))}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"isRemote",value:function(){return this.isRemote_}},{key:"getDiv",value:function(){return this.div_}},{key:"getObjectFit",value:function(){return this.objectFit_}},{key:"getMuted",value:function(){return this.muted_}}]),e}(),Rm=function(e){function t(e){var n;vt(this,t);var r=St({},e,{isRemote:!0,type:e.type});return(n=Et(this,kt(t).call(this,r))).isInSubscriptionCycle_=!1,n.isStreamAddedEventEmitted_=!1,n}return bt(t,km),_t(t,[{key:"getType",value:function(){return Ct(kt(t.prototype),"getType",this).call(this)}},{key:"setInSubscriptionCycle",value:function(e){this.isInSubscriptionCycle_=e}},{key:"isInSubscriptionCycle",value:function(){return this.isInSubscriptionCycle_}},{key:"setIsStreamAddedEventEmitted",value:function(e){this.isStreamAddedEventEmitted_=e}},{key:"getIsStreamAddedEventEmitted",value:function(){return this.isStreamAddedEventEmitted_}},{key:"getAudioTrack",value:function(){return this.connection_&&this.connection_.getTrackState().audio?Ct(kt(t.prototype),"getAudioTrack",this).call(this):null}},{key:"getVideoTrack",value:function(){if(!this.connection_)return null;var e=this.connection_.getTrackState();return("main"!==this.type_||e.video)&&("auxiliary"!==this.type_||e.auxiliary)?Ct(kt(t.prototype),"getVideoTrack",this).call(this):null}}]),t}(),wm=function(){function e(t){vt(this,e),this.client_=t.client,this.subscribedStreams_=new Map,this.unsubscribedStreams_=new Map,this.subscriptedOptions_=new Map,this.autoRecoveryFlags_=new Map}var t,n,r;return _t(e,[{key:"recover",value:(r=mt(regeneratorRuntime.mark(function e(t){var n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.getUserId(),r=t.getType(),this.hasAutoRecoveryFlag(n,r)){e.next=5;break}return e.abrupt("return");case 5:if(i=this.getUnsubscribedStream(n,r)?"unsubscribe":"subscribe",e.prev=6,Hn.warn("recover() try to recover subscription [".concat(i,"][").concat(n,"]")),"subscribe"!==i){e.next=13;break}return e.next=11,this.recoverSubscription(n,t);case 11:e.next=15;break;case 13:return e.next=15,this.recoverUnsubscription(n,t);case 15:af({eventType:tc}),Hn.warn("recover() recover successfully [".concat(i,"][").concat(n,"]")),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(6),Hn.error("recover() recover failed ".concat(i),e.t0),sf({eventType:tc,error:e.t0});case 23:this.deleteAutoRecoveryFlag(n,r);case 24:case"end":return e.stop()}},e,this,[[6,19]])})),function(e){return r.apply(this,arguments)})},{key:"recoverSubscription",value:(n=mt(regeneratorRuntime.mark(function e(t,n){var r,i,o,a,s,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getOptions(t,n.getType()),i=this.getSubscribedStream(t,n.getType()),r&&i){e.next=4;break}return e.abrupt("return");case 4:return o=this.getStreamMuteState(i),a=o.isAudioMuted,s=o.isVideoMuted,this.mergeStream(i,n),c=i.getConnection(),e.next=9,c.subscribe(i,r);case 9:this.recoverPlayingState(i),a&&i.doEnableTrack("audio",!1),s&&i.doEnableTrack("video",!1);case 12:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})},{key:"recoverUnsubscription",value:(t=mt(regeneratorRuntime.mark(function e(t,n){var r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getUnsubscribedStream(t,n.getType())){e.next=3;break}return e.abrupt("return");case 3:return this.mergeStream(r,n),i=r.getConnection(),e.next=7,i.unsubscribe(r);case 7:case"end":return e.stop()}},e,this)})),function(e,n){return t.apply(this,arguments)})},{key:"getStreamMuteState",value:function(e){var t={isAudioMuted:!1,isVideoMuted:!1},n=e.getMediaStream();return n&&(t.isAudioMuted=n.getAudioTracks().map(function(e){return e.enabled}).includes(!1),t.isVideoMuted=n.getVideoTracks().map(function(e){return e.enabled}).includes(!1)),t}},{key:"recoverPlayingState",value:function(e){var t=e.isPlaying(),n=e.getDiv();if(t&&n){var r=n.parentNode;e.stop(),e.play(r,{objectFit:e.getObjectFit(),muted:e.getMuted()})}}},{key:"mergeStream",value:function(e,t){var n=t.getConnection(),r=t.getMediaStream();e.setConnection(n),n.setRemoteStream(r.id,e),e.setMediaStream(r),e.updateAudioPlayingState(t.hasAudio()),e.updateVideoPlayingState(t.hasVideo())}},{key:"addSubscriptionRecord",value:function(e,t,n){var r=t.getType();if(this.subscribedStreams_.has(e))this.subscribedStreams_.get(e).set(r,t);else{var i=new Map;i.set(t.getType(),t),this.subscribedStreams_.set(e,i)}if(this.subscriptedOptions_.has(e))this.subscriptedOptions_.get(e).set(r,n);else{var o=new Map;o.set(t.getType(),n),this.subscriptedOptions_.set(e,o)}this.deleteUnsubscriptionRecord(e,r)}},{key:"addUnsubscriptionRecord",value:function(e,t){if(this.unsubscribedStreams_.has(e))this.unsubscribedStreams_.get(e).set(t.getType(),t);else{var n=new Map;n.set(t.getType(),t),this.unsubscribedStreams_.set(e,n)}this.deleteSubscriptionRecord(e,t.getType())}},{key:"getSubscribedStream",value:function(e,t){return this.subscribedStreams_.has(e)&&this.subscribedStreams_.get(e).has(t)?this.subscribedStreams_.get(e).get(t):null}},{key:"getOptions",value:function(e,t){return this.subscriptedOptions_.has(e)&&this.subscriptedOptions_.get(e).has(t)?this.subscriptedOptions_.get(e).get(t):null}},{key:"getUnsubscribedStream",value:function(e,t){return this.unsubscribedStreams_.has(e)&&this.unsubscribedStreams_.get(e).has(t)?this.unsubscribedStreams_.get(e).get(t):null}},{key:"deleteSubscriptionRecord",value:function(e,t){this.subscribedStreams_.has(e)&&this.subscribedStreams_.get(e).delete(t),this.subscriptedOptions_.has(e)&&this.subscriptedOptions_.get(e).delete(t)}},{key:"deleteUnsubscriptionRecord",value:function(e,t){this.unsubscribedStreams_.has(e)&&this.unsubscribedStreams_.get(e).delete(t)}},{key:"markAllStream",value:function(){for(var e=0,t=xt(this.subscribedStreams_.entries());e<t.length;e++)for(var n=It(t[e],2),r=n[0],i=0,o=xt(n[1].entries());i<o.length;i++){var a=It(o[i],1)[0];this.setAutoRecoveryFlag(r,a)}for(var s=0,c=xt(this.unsubscribedStreams_.entries());s<c.length;s++)for(var u=It(c[s],2),d=(r=u[0],0),l=xt(u[1].entries());d<l.length;d++){a=It(l[d],1)[0];this.setAutoRecoveryFlag(r,a)}}},{key:"setAutoRecoveryFlag",value:function(e,t){if(Hn.info("setAutoRecoveryFlag() mark [".concat(e,"][").concat(t,"]")),this.autoRecoveryFlags_.has(e))this.autoRecoveryFlags_.get(e).set(t);else{var n=new Map;n.set(t),this.autoRecoveryFlags_.set(e,n)}}},{key:"hasAutoRecoveryFlag",value:function(e,t){return!!this.isEnabled&&(this.autoRecoveryFlags_.has(e)&&this.autoRecoveryFlags_.get(e).has(t))}},{key:"deleteAutoRecoveryFlag",value:function(e,t){this.autoRecoveryFlags_.has(e)&&this.autoRecoveryFlags_.get(e).delete(t)}},{key:"delete",value:function(e){this.unsubscribedStreams_.delete(e),this.subscribedStreams_.delete(e),this.subscriptedOptions_.delete(e),this.autoRecoveryFlags_.delete(e)}},{key:"isEnabled",get:function(){return"webrtc"!==this.client_.getEnv()}}]),e}(),Tm=ze.find,Em=!0;"find"in[]&&Array(1).find(function(){Em=!1}),Ae({target:"Array",proto:!0,forced:Em},{find:function(e){return Tm(this,e,arguments.length>1?arguments[1]:void 0)}}),Xu("find");var Cm=function(){function e(t){vt(this,e),this.userId_=t.userId,this.tinyId_=t.tinyId,this.client_=t.client,this.sdpSemantics_=t.client.getSdpSemantics(),this.isUplink_=t.isUplink,this.log_=new om({id:"n|"+this.userId_,direction:this.isUplink_?"local":"remote",type:""}),this.signalChannel_=t.signalChannel,this.peerConnection_=null,this.connectTimer_=-1,this.isErrorObserved_=!1,this.emitter_=new Wp,this.startTime_=new Date,this.endTime_=this.startTime_,this.hasValidEndTime_=!1,this.hasVideo_=!1,this.currentState_=Ds.DISCONNECTED}var t;return _t(e,[{key:"initialize",value:function(){var e={iceServers:this.client_.getIceServers(),iceTransportPolicy:"all",sdpSemantics:this.sdpSemantics_,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};this.peerConnection_=new RTCPeerConnection(e),this.peerConnection_.onconnectionstatechange=this.onConnectionStateChange.bind(this)}},{key:"close",value:function(){this.log_.info("closing connection"),this.clearConnectTimer(),this.closePeerConnection()}},{key:"closePeerConnection",value:function(){this.peerConnection_&&(this.peerConnection_.onconnectionstatechange=function(e){},this.peerConnection_.close(),this.peerConnection_=null)}},{key:"getDTLSTransportState",value:function(){if(!this.peerConnection_)return"unknown";var e=null;if(this.isUplink_){if(!wf()||0===this.peerConnection_.getSenders().length)return"unknown";e=this.peerConnection_.getSenders()[0].transport}else{if(!Rf()||0===this.peerConnection_.getReceivers().length)return"unknown";e=this.peerConnection_.getReceivers()[0].transport}return e?e.state:"unknown"}},{key:"onConnectionStateChange",value:function(e){var t=this,n=this.peerConnection_.iceConnectionState,r=this.getDTLSTransportState();if(this.log_.info("onConnectionStateChange() connectionState: "+e.target.connectionState),this.log_.info("ICE Transport state: ".concat(n,", DTLS Transport state: ").concat(r)),"connecting"===e.target.connectionState){this.clearConnectTimer();this.connectTimer_=setTimeout(function(){var e="".concat(t.isUplink_?"uplink":"downlink"," DTLS Transport connection timeout (").concat(10,"s). ICE Transport state: ").concat(n,", DTLS Transport state: ").concat(r);t.emitter_.emit(jf,{prevState:t.currentState_,state:Ds.DISCONNECTED}),t.currentState_=Ds.DISCONNECTED,t.isErrorObserved_=!0;var i=new vh({message:e,code:fh.ICE_TRANSPORT_ERROR});sf({eventType:Xs,error:i}),t.emitter_.emit(Vf,i)},1e4),this.emitter_.emit(jf,{prevState:this.currentState_,state:Ds.CONNECTING}),this.currentState_=Ds.CONNECTING}else this.clearConnectTimer();if("failed"===e.target.connectionState||"closed"===e.target.connectionState){var i="".concat(this.isUplink_?"uplink":"downlink"," ICE/DTLS Transport connection ").concat(e.target.connectionState,". ICE Transport state: ").concat(n,", DTLS Transport state: ").concat(r),o=new vh({message:i,code:fh.ICE_TRANSPORT_ERROR});sf({eventType:Xs,error:o}),this.emitter_.emit(jf,{prevState:this.currentState_,state:Ds.DISCONNECTED}),this.currentState_=Ds.DISCONNECTED,this.isErrorObserved_||this.emitter_.emit(Vf,o)}if("connected"===e.target.connectionState||"completed"===e.target.connectionState){if(af({eventType:Xs}),this.emitter_.emit(jf,{prevState:this.currentState_,state:Ds.CONNECTED}),this.currentState_=Ds.CONNECTED,!this.isUplink_&&!this.sentSubscriptionAfterConnected_&&this.pendingSubscription_.length>0){this.log_.info("send pending subscription after RTCPeerConnection is connected");var a=this.pendingSubscription_[0];this.doSendSubscription(a.data,a.stream,a.type),this.sentSubscriptionAfterConnected_=!0}}else"disconnected"===e.target.connectionState&&(this.emitter_.emit(jf,{prevState:this.currentState_,state:Ds.DISCONNECTED}),this.currentState_=Ds.DISCONNECTED)}},{key:"hitTest",value:function(e){return(0===e||"0"===e)&&this.isUplink_||e===this.tinyId_}},{key:"addEventInternal",value:function(e,t){var n=this.client_.getUserId(),r={eventId:e,eventDesc:t,timestamp:Eo(),userId:n,tinyId:this.client_.getTinyId()};this.isUplink_||(r.remoteUserId=this.userId_,r.remoteTinyId=this.tinyId_),Yh(n,r)}},{key:"getPeerConnection",value:function(){return this.peerConnection_}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"setVideoStats",value:function(e){"start"===e?(this.hasVideo_=!0,this.startTime_=new Date,this.hasValidEndTime_=!1):(this.hasVideo_=!1,this.endTime_=new Date,this.hasValidEndTime_=!0)}},{key:"getVideoHealthStats",value:(t=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.hasValidEndTime_||(this.endTime_=new Date),t=(this.endTime_-this.startTime_)/1e3,n=!1,r=0,i=0,o=0,!this.hasVideo_){e.next=21;break}if(n=!0,!this.isUplink_){e.next=15;break}return e.next=11,(new im).getSenderStats(this);case 11:a=e.sent,r=a.video.framesSent/t,e.next=19;break;case 15:return e.next=17,(new im).getReceiverStats(this);case 17:a=e.sent,r=a.video.framesDecoded/t;case 19:i=a.video.frameWidth,o=a.video.frameHeight;case 21:return e.abrupt("return",{valid:n,framerate:r,duration:t,width:i,height:o});case 22:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"clearConnectTimer",value:function(){-1!==this.connectTimer_&&(clearTimeout(this.connectTimer_),this.connectTimer_=-1)}},{key:"on",value:function(e,t){this.emitter_.on(e,t)}}]),e}(),Im=t(function(e){var t=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%d trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"invalid",names:["value"]}]};Object.keys(t).forEach(function(e){t[e].forEach(function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")})})}),xm=(Im.v,Im.o,Im.s,Im.i,Im.u,Im.e,Im.p,Im.z,Im.r,Im.t,Im.c,Im.b,Im.m,Im.a,t(function(e,t){var n=function(e){return String(Number(e))===e?Number(e):e},r=function(e,t,r){var i=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:i&&!t[e.name]&&(t[e.name]={});var o=e.push?{}:i?t[e.name]:t;!function(e,t,r,i){if(i&&!r)t[i]=n(e[1]);else for(var o=0;o<r.length;o+=1)null!=e[o+1]&&(t[r[o]]=n(e[o+1]))}(r.match(e.reg),o,e.names,e.name),e.push&&t[e.push].push(o)},i=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},n=[],o=t;return e.split(/(\r\n|\r|\n)/).filter(i).forEach(function(e){var t=e[0],i=e.slice(2);"m"===t&&(n.push({rtp:[],fmtp:[]}),o=n[n.length-1]);for(var a=0;a<(Im[t]||[]).length;a+=1){var s=Im[t][a];if(s.reg.test(i))return r(s,o,i)}}),t.media=n,t};var o=function(e,t){var r=t.split(/=(.+)/,2);return 2===r.length?e[r[0]]=n(r[1]):1===r.length&&t.length>1&&(e[r[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],r=e.split(" ").map(n),i=0;i<r.length;i+=3)t.push({component:r[i],ip:r[i+1],port:r[i+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map(function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})})},t.parseSimulcastStreamList=function(e){return e.split(";").map(function(e){return e.split(",").map(function(e){var t,r=!1;return"~"!==e[0]?t=n(e):(t=n(e.substring(1,e.length)),r=!0),{scid:t,paused:r}})})}})),Pm=(xm.parse,xm.parseParams,xm.parseFmtpConfig,xm.parsePayloads,xm.parseRemoteCandidates,xm.parseImageAttributes,xm.parseSimulcastStreamList,/%[sdv%]/g),Am=function(e){var t=1,n=arguments,r=n.length;return e.replace(Pm,function(e){if(t>=r)return e;var i=n[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(i);case"%d":return Number(i);case"%v":return""}})},Om=function(e,t,n){var r=[e+"="+(t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format)];if(t.names)for(var i=0;i<t.names.length;i+=1){var o=t.names[i];t.name?r.push(n[t.name][o]):r.push(n[t.names[i]])}else r.push(n[t.name]);return Am.apply(null,r)},Dm=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Nm=["i","c","b","a"],Mm={write:function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach(function(e){null==e.payloads&&(e.payloads="")});var n=t.outerOrder||Dm,r=t.innerOrder||Nm,i=[];return n.forEach(function(t){Im[t].forEach(function(n){n.name in e&&null!=e[n.name]?i.push(Om(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach(function(e){i.push(Om(t,n,e))})})}),e.media.forEach(function(e){i.push(Om("m",Im.m[0],e)),r.forEach(function(t){Im[t].forEach(function(n){n.name in e&&null!=e[n.name]?i.push(Om(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach(function(e){i.push(Om(t,n,e))})})})}),i.join("\r\n")+"\r\n"},parse:xm.parse,parseFmtpConfig:xm.parseFmtpConfig,parseParams:xm.parseParams,parsePayloads:xm.parsePayloads,parseRemoteCandidates:xm.parseRemoteCandidates,parseImageAttributes:xm.parseImageAttributes,parseSimulcastStreamList:xm.parseSimulcastStreamList},Lm=function(e){return Mm.parse(e)},Um=function(e){return Mm.write(e)},Vm={voiceActivityDetection:!1},jm=function(e){function t(e){var n;return vt(this,t),(n=Et(this,kt(t).call(this,e))).localStream_=null,n.waitForUpdatedAnswer_=!1,n.publishResolve_=null,n.isReconnecting_=!1,n.reconnectionCount_=0,n.reconnectionTimer_=-1,n.isFirstConnection_=!0,n}var n,r,i,o,a,s,c,u;return bt(t,Cm),_t(t,[{key:"initialize",value:function(){Ct(kt(t.prototype),"initialize",this).call(this),this.installEvents()}},{key:"close",value:function(){Ct(kt(t.prototype),"close",this).call(this),this.uninstallEvents()}},{key:"installEvents",value:function(){var e=this;this.signalChannel_.on(Kp.PUBLISH_RESULT,this.onNewRemoteSdp,this),this.signalChannel_.on(Kp.REMOTE_ANSWER,this.onNewRemoteSdp,this),this.emitter_.on(Vf,function(t){t.getCode()===fh.ICE_TRANSPORT_ERROR&&(e.isFirstConnection_&&(e.isFirstConnection_=!1,sf({eventType:Hs,error:t})),e.isReconnecting_||(e.isReconnecting_=!0,e.reconnect(),e.addEventInternal(Jh,"uplink-connection is reconnecting")))}),this.emitter_.on(jf,function(t){t.state===Ds.CONNECTED&&(e.isFirstConnection_&&(e.isFirstConnection_=!1,af({eventType:Hs}),e.addEventInternal(Fh,"uplink-connection is connected")),e.isReconnecting_&&(af({eventType:Js}),e.log_.warn("reconnect() uplink reconnect successfully"),e.addEventInternal(zh,"uplink-connection reconnect success"),e.stopReconnection()))})}},{key:"uninstallEvents",value:function(){this.signalChannel_.removeListener(Kp.PUBLISH_RESULT,this.onNewRemoteSdp,this),this.signalChannel_.removeListener(Kp.REMOTE_ANSWER,this.onNewRemoteSdp,this)}},{key:"onNewRemoteSdp",value:function(e){if(this.hitTest(e.data.srctinyid)){var t=e.data.content;this.acceptAnswer(t)}}},{key:"publish",value:function(e){var t=this;return new Promise(function(n,r){t.localStream_=e,t.publishResolve_=n;var i=e.getMediaStream();t.log_.info("is publishing stream: ".concat(e.getId()));var o=t.localStream_.getAudioTrack(),a=t.localStream_.getVideoTrack();o&&t.peerConnection_.addTrack(o,i),a&&(t.peerConnection_.addTrack(a,i),t.setVideoStats("start")),t.updateMediaSettings(i),t.exchangeOffer(r)})}},{key:"updateMediaSettings",value:function(e){var t=this,n={EncVideoCodec:"H264",EncVideoWidth:0,EncVideoHeight:0,EncVideoBr:"0",EncVideoFps:0,EncAudioCodec:"opus",EncAudioFS:0,EncAudioCh:0,EncAudioBr:"0"};"getSettings"in MediaStreamTrack.prototype?e.getTracks().forEach(function(e){var r=e.getSettings();if("audio"===e.kind){var i=1;r.channelCount&&(i=r.channelCount),n.EncAudioCh=i,n.EncAudioBr="".concat(1e3*t.localStream_.getAudioBitrate()),n.EncAudioFS=r.sampleRate}else"video"===e.kind&&(n.EncVideoWidth=r.width,n.EncVideoHeight=r.height,n.EncVideoFps=r.frameRate,n.EncVideoBr="".concat(1e3*t.localStream_.getVideoBitrate()))}):n=this.getMediaSettingsFromProfile(n),this.log_.info("updateMediaSettings: "+JSON.stringify(n)),this.signalChannel_.send(nh,n)}},{key:"getMediaSettingsFromProfile",value:function(e){var t=this.localStream_;if(t){if(t.getAudioTrack()){var n=t.getAudioProfile();e.EncAudioCh=n.channelCount,e.EncAudioBr="".concat(1e3*n.bitrate),e.EncAudioFS=n.sampleRate}if(t.getVideoTrack()){var r=t.getVideoProfile();e.EncVideoWidth=r.width,e.EncVideoHeight=r.height,e.EncVideoFps=r.frameRate,e.EncVideoBr="".concat(1e3*r.bitrate)}}return e}},{key:"addTrack",value:(u=mt(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_){e.next=8;break}return this.log_.info("is adding ".concat(t.kind," track to current published local stream")),n=this.localStream_.getMediaStream(),this.peerConnection_.addTrack(t,n),"video"===t.kind&&this.setVideoStats("start"),this.updateMediaSettings(n),e.next=8,this.updateOffer("add",t);case 8:case"end":return e.stop()}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"isNeedToResetOfferOrder",value:function(){if("plan-b"===this.sdpSemantics_||!this.peerConnection_||!this.peerConnection_.localDescription)return!1;for(var e=this.peerConnection_.localDescription.sdp,t=Lm(e),n=0;n<t.media.length;n++)if(0===t.media[n].mid&&"video"===t.media[n].type)return!0;return!1}},{key:"removeTrack",value:(c=mt(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.peerConnection_||!wf()){e.next=18;break}if(this.log_.info("is removing ".concat(t.kind," track from current published local stream")),"video"!==t.kind||!this.isNeedToResetOfferOrder()){e.next=8;break}return this.close(),this.initialize(),e.next=7,this.publish(this.localStream_);case 7:return e.abrupt("return");case 8:if(!(n=this.peerConnection_.getSenders().find(function(e){return e.track===t}))){e.next=18;break}return r=null,"RTCPeerConnection"in window&&"getTransceivers"in window.RTCPeerConnection.prototype&&(r=this.peerConnection_.getTransceivers().find(function(e){return e.sender&&e.sender.track===t})),this.peerConnection_.removeTrack(n),r&&yc(r.stop)&&(this.log_.info("stop transceiver"),r.stop()),"video"===t.kind&&this.setVideoStats("end"),this.updateMediaSettings(this.localStream_.getMediaStream()),e.next=18,this.updateOffer("remove",t);case 18:case"end":return e.stop()}},e,this)})),function(e){return c.apply(this,arguments)})},{key:"isReplaceTrackAvailable",value:function(){return"RTCRtpSender"in window&&"replaceTrack"in window.RTCRtpSender.prototype}},{key:"replaceTrack",value:(s=mt(regeneratorRuntime.mark(function e(t){var n,r=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isReplaceTrackAvailable()&&wf()){e.next=2;break}throw new vh({code:fh.INVALID_OPERATION,message:"replaceTrack is not supported in this browser, please use switchDevice or addTrack instead"});case 2:if(this.peerConnection_){e.next=4;break}throw new vh({code:fh.INVALID_OPERATION,message:"replaceTrack() is only valid after the LocalStream has been published"});case 4:if(0!==(n=this.peerConnection_.getSenders()).length){e.next=7;break}throw new vh({code:fh.INVALID_OPERATION,message:"replaceTrack() is only valid after the LocalStream has been published"});case 7:n.forEach(function(e){e.track&&e.track.kind===t.kind&&(r.log_.info("is replacing ".concat(t.kind," track to current published local stream")),e.replaceTrack(t))});case 8:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"setBandwidth",value:(a=mt(regeneratorRuntime.mark(function e(t,n,r){var i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isUplink_){e.next=2;break}return e.abrupt("return",r);case 2:if(Ef()){e.next=4;break}return e.abrupt("return","video"===n?this.updateVideoBandwidthRestriction(r,t):this.updateAudioBandwidthRestriction(r,t));case 4:if(!(i=this.peerConnection_.getSenders().find(function(e){return e.track&&e.track.kind===n}))){e.next=20;break}return(o=i.getParameters()).encodings&&0!==o.encodings.length||(o.encodings=[{}]),"unlimited"===t?delete o.encodings[0].maxBitrate:o.encodings[0].maxBitrate=1e3*t,e.prev=9,e.next=12,i.setParameters(o);case 12:return this.log_.debug(n+" bandwidth was set to "+t+" kbps"),e.abrupt("return",r);case 16:return e.prev=16,e.t0=e.catch(9),this.log_.info("failed to set bandwidth by setting maxBitrate: "+e.t0),e.abrupt("return","video"===n?this.updateVideoBandwidthRestriction(r,t):this.updateAudioBandwidthRestriction(r,t));case 20:return e.abrupt("return",r);case 21:case"end":return e.stop()}},e,this,[[9,16]])})),function(e,t,n){return a.apply(this,arguments)})},{key:"updateVideoBandwidthRestriction",value:function(e,t){var n="AS";return ga&&(n="TIAS",t*=1e3),e=-1===e.indexOf("b="+n+":")?e.replace(/m=video (.*)\r\nc=IN (.*)\r\n/,"m=video $1\r\nc=IN $2\r\nb="+n+":"+t+"\r\n"):e.replace(new RegExp("b="+n+":.*\r\n"),"b="+n+":"+t+"\r\n")}},{key:"updateAudioBandwidthRestriction",value:function(e,t){var n="AS";return ga&&(n="TIAS",t*=1e3),e=e.replace(/m=audio (.*)\r\nc=IN (.*)\r\n/,"m=audio $1\r\nc=IN $2\r\nb="+n+":"+t+"\r\n")}},{key:"removeBandwidthRestriction",value:function(e){return e.replace(/b=AS:.*\r\n/,"").replace(/b=TIAS:.*\r\n/,"")}},{key:"removeVideoOrientation",value:function(e){return e.replace(/urn:3gpp:video-orientation/,"")}},{key:"exchangeOffer",value:function(e){var t=this;this.peerConnection_.createOffer(Vm).then(function(e){return t.peerConnection_.setLocalDescription(e)}).then(function(){t.log_.info("createOffer success, sending offer to remote server");var e=t.peerConnection_.localDescription,n={type:e.type,sdp:t.removeVideoOrientation(e.sdp)};t.log_.debug("sending sdp offer: "+n.sdp),t.signalChannel_.send(ih,n,0),af({eventType:qs,kind:"offer"})}).catch(function(n){var r=new vh({code:fh.CREATE_OFFER_FAILED,message:"failed to create offer: "+n.message});t.log_.error("failed to create offer",n),sf({eventType:qs,kind:"offer",error_:r}),e(r)})}},{key:"setSDPDirection",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all",r=Lm(e);return r.media.forEach(function(e){"all"!==n&&e.type!==n||(e.direction=t)}),Um(r)}},{key:"updateOffer",value:(o=mt(regeneratorRuntime.mark(function e(t,n){var r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r="",e.prev=1,e.next=4,this.peerConnection_.createOffer(Vm);case 4:return r=e.sent,ga&&(r.sdp=this.setSDPDirection(r.sdp,"sendrecv")),e.next=8,this.peerConnection_.setLocalDescription(r);case 8:i={action:t,trackId:n.id,kind:n.kind,type:"offer",sdp:this.peerConnection_.localDescription.sdp},this.log_.info("createOffer success, sending updated offer to remote server"),this.log_.debug("updatedOffer: "+i.sdp),this.signalChannel_.send(Xp,i,0),this.waitForUpdatedAnswer_=!0,af({eventType:qs,kind:"offer"}),e.next=22;break;case 16:throw e.prev=16,e.t0=e.catch(1),o=new vh({code:fh.CREATE_OFFER_FAILED,message:"failed to create updated sdp offer: "+e.t0.message}),this.log_.error(o),sf({eventType:qs,kind:"offer",error_:o}),o;case 22:case"end":return e.stop()}},e,this,[[1,16]])})),function(e,t){return o.apply(this,arguments)})},{key:"acceptAnswer",value:(i=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.localStream_.getVideoBitrate(),r=this.localStream_.getAudioBitrate(),i=this.removeVideoOrientation(t.sdp),e.prev=3,e.next=6,this.setBandwidth(n,"video",i);case 6:return i=e.sent,e.next=9,this.setBandwidth(r,"audio",i);case 9:return i=e.sent,o={type:t.type,sdp:i},e.next=13,this.peerConnection_.setRemoteDescription(o);case 13:this.log_.debug("accepted answer: "+i),this.waitForUpdatedAnswer_||(this.log_.info("accepted remote answer"),this.publishResolve_&&(this.publishResolve_(this.localStream_),this.publishResolve_=null)),af({eventType:Ks,kind:"answer"}),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),sf({eventType:Ks,kind:"answer",error:e.t0}),this.log_.error("failed to accept remote answer "+e.t0);case 22:case"end":return e.stop()}},e,this,[[3,18]])})),function(e){return i.apply(this,arguments)})},{key:"sendMutedFlag",value:function(e){var t={srctinyid:0,userid:this.userId_,flag:e};this.log_.info("send muted flag: ".concat(e)),this.signalChannel_.send(th,t)}},{key:"getIsReconnecting",value:function(){return this.isReconnecting_}},{key:"reconnect",value:(r=mt(regeneratorRuntime.mark(function e(){var t,n,r=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(-1===this.reconnectionTimer_){e.next=3;break}return this.log_.warn("reconnect() uplink is reconnecting, ignore current reconnection"),e.abrupt("return");case 3:if(!(this.reconnectionCount_>=30)){e.next=11;break}return this.log_.warn("SDK has tried reconnect uplink for ".concat(30," times, but all failed, please check your network")),this.stopReconnection(),t=new vh({code:fh.UPLINK_RECONNECTION_FAILED,message:"uplink reconnect failed, please check your network and publish again"}),sf({eventType:Js,error:t}),this.addEventInternal(Qh,"uplink-connection reconnect fail"),this.emitter_.emit(Vf,t),e.abrupt("return");case 11:if(this.signalChannel_.getCurrentState()===Qp.CONNECTED){e.next=15;break}return this.log_.warn("reconnect() signal channel is not connected, suspend reconnection until signal is connected"),this.signalChannel_.once(Jp,this.reconnect,this),e.abrupt("return");case 15:this.reconnectionCount_++;try{this.log_.warn("reconnect() try to reconnect uplink [".concat(this.reconnectionCount_,"/").concat(30,"]")),n=gc(this.reconnectionCount_),this.reconnectionTimer_=setTimeout(function(){r.log_.warn("reconnect() uplink reconnect timeout(".concat(n/1e3,"s), try again")),r.signalChannel_.off(Kp.UNPUBLISH_RESULT,r.onUnpublishResult,r),r.clearReconnectionTimer(),r.reconnect()},n),this.signalChannel_.send(oh),this.signalChannel_.once(Kp.UNPUBLISH_RESULT,this.onUnpublishResult,this)}catch(t){}case 17:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"onUnpublishResult",value:(n=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.close(),this.initialize(),e.next=5,this.publish(this.localStream_);case 5:e.next=9;break;case 7:e.prev=7,e.t0=e.catch(0);case 9:case"end":return e.stop()}},e,this,[[0,7]])})),function(){return n.apply(this,arguments)})},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"stopReconnection",value:function(){this.log_.info("stop uplink reconnection"),this.isReconnecting_=!1,this.reconnectionCount_=0,this.clearReconnectionTimer(),this.signalChannel_.off(Jp,this.reconnect,this)}}]),t}(),Fm=function(e){function t(e){var n;return vt(this,t),(n=Et(this,kt(t).call(this,e))).remoteStreams_=new Map,n.autoSubscribe=e.autoSubscribe,n.trackState_={audio:e.trackState.audio,video:e.trackState.video,auxiliary:e.trackState.auxiliary},n.ssrc_={audio:0,video:0,auxiliary:0},n.subscribeState_={audio:e.autoSubscribe,video:e.autoSubscribe,auxiliary:e.autoSubscribe},n.pendingSubscription_=[],n.pendingStreams_=[],n.subscriptionTimeout_=-1,n.subscriptionRetryCount_=0,n.isSubscriptionPending_=!1,n.sentSubscriptionAfterConnected_=!1,n.isSDPExchanging_=!1,n.exchangeSDPResolve_=null,n.exchangeSDPReject_=null,n.isReconnecting_=!1,n.reconnectionCount_=0,n.reconnectionTimer_=-1,n.isFirstConnection_=!0,n}var n,r,i;return bt(t,Cm),_t(t,[{key:"initialize",value:function(){Ct(kt(t.prototype),"initialize",this).call(this),this.peerConnection_.ontrack=this.onTrack.bind(this),this.installEvents()}},{key:"close",value:function(){var e=this;Ct(kt(t.prototype),"close",this).call(this),-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1),this.remoteStreams_.forEach(function(t){var n=t;n.setConnection(null),n.getIsStreamAddedEventEmitted()&&e.emitter_.emit(Mf,{stream:n})}),this.remoteStreams_.clear(),this.uninstallEvents()}},{key:"installEvents",value:function(){var e=this;this.signalChannel_.on(Kp.REMOTE_STREAM_UPDATE,this.onRemoteStreamUpdate,this),this.signalChannel_.on(Kp.SUBSCRIBE_RESULT,this.onSubscribeResult,this),this.signalChannel_.on(Kp.SUBSCRIBE_CHANGE_RESULT,this.onSubscribeChangeResult,this),this.signalChannel_.on(Kp.UNSUBSCRIBE_RESULT,this.onUnsubscribeResult,this),this.emitter_.on(Vf,function(t){if(t.getCode()===fh.ICE_TRANSPORT_ERROR&&(e.isFirstConnection_&&(e.isFirstConnection_=!1,sf({eventType:zs,error:t})),!e.isReconnecting_)){e.isReconnecting_=!0;var n=e.client_.getSubscriptionManager();if(n){var r=!0,i=!1,o=void 0;try{for(var a,s=e.remoteStreams_.values()[Symbol.iterator]();!(r=(a=s.next()).done);r=!0){var c=a.value,u=c.getType();("main"===u&&(e.trackState_.audio||e.trackState_.video)||"auxiliary"===u&&e.trackState_.auxiliary)&&n.setAutoRecoveryFlag(e.userId_,c.getType())}}catch(d){i=!0,o=d}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}}e.reconnect(),e.addEventInternal(qh,"downlink-connection is reconnecting")}}),this.emitter_.on(jf,function(t){t.state===Ds.CONNECTED&&(e.isFirstConnection_&&(e.isFirstConnection_=!1,af({eventType:zs}),e.addEventInternal(Bh,"downlink-connection is connected")),e.isReconnecting_&&(af({eventType:Qs}),e.log_.warn("reconnect() downlink reconnect successfully"),e.addEventInternal(Kh,"downlink-connection reconnect success"),e.stopReconnection()))})}},{key:"uninstallEvents",value:function(){this.signalChannel_.removeListener(Kp.SUBSCRIBE_CHANGE_RESULT,this.onSubscribeChangeResult,this),this.signalChannel_.removeListener(Kp.UNSUBSCRIBE_RESULT,this.onUnsubscribeResult,this),this.signalChannel_.removeListener(Kp.REMOTE_STREAM_UPDATE,this.onRemoteStreamUpdate,this),this.signalChannel_.removeListener(Kp.SUBSCRIBE_RESULT,this.onSubscribeResult,this)}},{key:"onRemoteStreamUpdate",value:function(e){var t=e.data.content;if(this.hitTest(t.srctinyid)){this.updateTrackState(t.action,t.kind);var n="auxVideo"===t.kind?Ps:xs,r=this.remoteStreams_.get(n);if(!r)return;"add"===t.action?this.handleRemoteAddTrack(t.kind,r):this.handleRemoteRemoveTrack(t.kind,r)}}},{key:"handleRemoteAddTrack",value:function(e,t){this.log_.info("remote add ".concat(e," track")),"audio"===e?t.updateAudioPlayingState(this.subscribeState_.audio):(t.updateVideoPlayingState("auxVideo"===e?this.subscribeState_.auxiliary:this.subscribeState_.video),this.setVideoStats("start")),t.getIsStreamAddedEventEmitted()?this.emitter_.emit(Lf,{stream:t}):this.emitter_.emit(Nf,{stream:t})}},{key:"handleRemoteRemoveTrack",value:function(e,t){t.getIsStreamAddedEventEmitted()&&(this.log_.info("remote remove ".concat(e," track")),"auxVideo"!==e&&(this.trackState_.audio||this.trackState_.video)||t.isInSubscriptionCycle()?("audio"===e?t.updateAudioPlayingState(!1):(t.updateVideoPlayingState(!1),this.setVideoStats("end")),this.emitter_.emit(Lf,{stream:t})):(this.log_.info("remote stream ".concat(t.getType()," removed")),this.emitter_.emit(Mf,{stream:t})))}},{key:"updateTrackState",value:function(e,t){switch(t){case"audio":this.trackState_.audio="add"===e;break;case"video":this.trackState_.video="add"===e;break;case"auxVideo":this.trackState_.auxiliary="add"===e}this.log_.info("trackState updated: ".concat(JSON.stringify(this.trackState_)))}},{key:"onTrack",value:function(e){var t=e.streams[0],n=e.track;if(this.log_.info("ontrack() kind: ".concat(n.kind," id: ").concat(n.id," streamId: ").concat(t.id)),"unified-plan"===this.sdpSemantics_){var r=function(e){var t=Mm.parse(e),n={audio:[],video:[]};return t.media.forEach(function(e){if(e.ssrcs){var t=e.ssrcs[0].id>>16&255;if("audio"===e.type)n.audio.push(xs);else if("video"==e.type){var r=t===As?xs:Ps;n.video.push(r)}}}),n}(this.peerConnection_.remoteDescription.sdp);if("audio"===n.kind){if(0===r.audio.length||t.id!==xs)return void this.log_.debug("skip this invalid audio track")}else if(-1===r.video.indexOf(t.id))return void this.log_.debug("skip this invalid video track: ".concat(n.id,"  msid: ").concat(t.id))}of({eventType:"ontrack",kind:n.kind});var i=!1,o=this.remoteStreams_.get(t.id),a=t.id===xs?"main":"auxiliary";Sc(o)&&((o=new Rm({type:a,userId:this.userId_,client:this.client_})).setConnection(this),this.remoteStreams_.set(t.id,o),i=!0),o.setMediaStream(t),"audio"===n.kind?o.updateAudioPlayingState(this.subscribeState_.audio):(this.setVideoStats("start"),"main"===a?o.updateVideoPlayingState(this.subscribeState_.video):o.updateVideoPlayingState(this.subscribeState_.auxiliary)),("auxiliary"!==a||this.trackState_.auxiliary)&&("main"!==a||this.trackState_.audio||this.trackState_.video)&&(i?this.emitter_.emit(Nf,{stream:o}):this.emitter_.emit(Lf,{stream:o}))}},{key:"addRRTRLine",value:function(e){var t=e.split("\r\n"),n=new Map;t.forEach(function(e,r){/^a=rtcp-fb:/.test(e)&&t[r+1]&&!/^a=rtcp-fb:/.test(t[r+1])&&n.set(r+1,e.match(/^a=rtcp-fb:\d+/)[0]+" rrtr")});for(var r=xt(n),i=0;i<r.length;i++){var o=It(r[i],2),a=o[0],s=o[1];t.splice(a+i,0,s)}return t.join("\r\n")}},{key:"addSPSDescription",value:function(e){var t=Lm(e);return t.media.forEach(function(e){"video"===e.type&&e.fmtp.forEach(function(e){e.config+=";sps-pps-idr-in-keyframe=1"})}),Um(t)}},{key:"removeSDESDescription",value:function(e){var t=["urn:ietf:params:rtp-hdrext:sdes:mid","urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id","urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id"],n=Lm(e);return n.media.forEach(function(e){e.ext=e.ext.filter(function(e){return!t.includes(e.uri)})}),Um(n)}},{key:"subscribe",value:(i=mt(regeneratorRuntime.mark(function e(t,n){var r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(("main"!==(r=t.getType())||Sc(n.audio)||Sc(n.video)||n.audio!==this.subscribeState_.audio||n.video!==this.subscribeState_.video)&&("auxiliary"!==r||Sc(n.video)||this.subscribeState_.auxiliary!==n.video)){e.next=4;break}return this.emitter_.emit(Uf,{stream:t,result:!0}),e.abrupt("return",t);case 4:if("main"===r?(Sc(n.audio)||(this.subscribeState_.audio=n.audio),Sc(n.video)||(this.subscribeState_.video=n.video),this.addEventInternal(this.subscribeState_.audio?Th:Ch,this.subscribeState_.audio?"subscribe audio":"unsubscribe audio"),this.addEventInternal(this.subscribeState_.video?Th:Ch,this.subscribeState_.video?"subscribe video":"unsubscribe video")):Sc(n.video)||(this.subscribeState_.auxiliary=n.video),this.log_.info("subscribe ".concat(r," stream with options ").concat(JSON.stringify(n)," current state: ").concat(JSON.stringify(this.subscribeState_))),!this.peerConnection_&&!this.isSDPExchanging_){e.next=14;break}return i=rc,this.isMainStreamSubscribed||this.isAuxStreamSubscribed||(i=nc),e.next=11,this.sendSubscription(t,i);case 11:"main"===r?(t.updateAudioPlayingState(this.subscribeState_.audio),t.updateVideoPlayingState(this.subscribeState_.video)):t.updateVideoPlayingState(this.subscribeState_.auxiliary),e.next=17;break;case 14:return this.initialize(),e.next=17,this.exchangeSDP();case 17:return this.emitter_.emit(Uf,{stream:t,result:!0}),e.abrupt("return",t);case 19:case"end":return e.stop()}},e,this)})),function(e,t){return i.apply(this,arguments)})},{key:"unsubscribe",value:(r=mt(regeneratorRuntime.mark(function e(t){var n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("main"!==(n=t.getType())){e.next=9;break}if(this.isMainStreamSubscribed){e.next=5;break}return this.log_.info("main stream already unsubscribed"),e.abrupt("return",t);case 5:this.subscribeState_.audio=!1,this.subscribeState_.video=!1,e.next=13;break;case 9:if(this.isAuxStreamSubscribed){e.next=12;break}return this.log_.info("auxiliary stream already unsubscribed"),e.abrupt("return",t);case 12:this.subscribeState_.auxiliary=!1;case 13:return r=nc,("main"===n&&this.isAuxStreamSubscribed||"auxiliary"===n&&this.isMainStreamSubscribed)&&(r=rc),this.log_.info("unsubscribe ".concat(n," stream with ").concat(JSON.stringify(this.subscribeState_))),e.next=18,this.sendSubscription(t,r);case 18:return t.updateVideoPlayingState(!1),t.updateAudioPlayingState(!1),r===nc&&((i=t.getMediaStream())&&i.getTracks().forEach(function(e){return i.removeTrack(e)}),this.closePeerConnection(),this.uninstallEvents()),this.addEventInternal(Ch,"unsubscribe audio"),this.addEventInternal(Eh,"unsubscribe video"),e.abrupt("return",t);case 24:case"end":return e.stop()}},e,this)})),function(e){return r.apply(this,arguments)})},{key:"sendSubscription",value:function(e,t){var n=this;return new Promise(function(r,i){var o={srctinyid:n.tinyId_,userid:n.userId_,audio:n.subscribeState_.audio,bigVideo:n.subscribeState_.video,auxVideo:n.subscribeState_.auxiliary};n.pendingSubscription_.length>0?n.log_.debug("queue the subscription for later handling"):n.doSendSubscription(o,e,t),n.pendingSubscription_.push({stream:e,type:t,data:o,callback:function(e){var o=e.code,a=e.message;if(0===o)r();else{var s="".concat(t," failed");a&&(s="".concat(s,": ").concat(a));var c=new vh({code:o,message:s});n.log_.error(c),i(c)}}}),e.setInSubscriptionCycle(!0)})}},{key:"doSendSubscription",value:function(e,t,n){var r=this;if(!this.peerConnection_||"connected"!==this.peerConnection_.connectionState&&"completed"!==this.peerConnection_.connectionState)return this.log_.debug("try to send subscription [".concat(n,"] when peeConnection connected")),void(this.sentSubscriptionAfterConnected_=!1);t&&this.pendingStreams_.push(t),this.log_.debug("doSendSubscription() send SUBSCRIBE command with data: ".concat(JSON.stringify(e))),n===rc?this.signalChannel_.send(ch,e):n===nc&&this.signalChannel_.send(sh,e),this.isSubscriptionPending_=!0,this.subscriptionTimeout_=setTimeout(function(){if(r.isSubscriptionPending_)if(r.log_.debug("subscription timeout"),r.subscriptionRetryCount_+=1,r.subscriptionRetryCount_<=3){r.log_.debug("resend subscription");var e=r.pendingSubscription_[0].data;r.doSendSubscription(e,t,n)}else r.log_.error("remote server not response to subscription"),r.pendingSubscription_.shift(),r.pendingStreams_.shift(),r.isSubscriptionPending_=!1,r.subscriptionRetryCount_=0,r.emitter_.emit(Vf,new vh({code:fh.SUBSCRIPTION_TIMEOUT,message:"remote server not response to subscription"}))},5e3)}},{key:"onSubscribeChangeResult",value:function(e){var t=e.data.content,n=t.srctinyid;if(this.hitTest(n)){var r=this.pendingSubscription_[0];r&&r.type===rc&&r.callback({code:t.errCode,message:t.errMsg}),this.sendNextSubscription()}}},{key:"onUnsubscribeResult",value:function(e){var t=e.data.content,n=t.srctinyid;if(this.hitTest(n)){var r=this.pendingSubscription_[0];r&&r.type===nc&&r.callback({code:t.errCode,message:t.errMsg}),this.sendNextSubscription()}}},{key:"exchangeSDP",value:function(){var e=this;return new Promise(function(){var t=mt(regeneratorRuntime.mark(function t(n,r){var i,o,a;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,"RTCPeerConnection"in window&&"addTransceiver"in window.RTCPeerConnection.prototype&&"unified-plan"===e.sdpSemantics_&&(e.peerConnection_.addTransceiver("audio",{direction:"recvonly"}),e.peerConnection_.addTransceiver("video",{direction:"recvonly"}),e.peerConnection_.addTransceiver("video",{direction:"recvonly"})),e.isSDPExchanging_=!0,t.next=5,e.peerConnection_.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0,voiceActivityDetection:!1});case 5:return(i=t.sent).sdp=e.addRRTRLine(i.sdp),i.sdp=e.addSPSDescription(i.sdp),"unified-plan"===e.sdpSemantics_&&(i.sdp=e.removeSDESDescription(i.sdp)),t.next=11,e.peerConnection_.setLocalDescription(i);case 11:e.log_.info("createOffer success, sending offer to remote server"),o=e.peerConnection_.localDescription,a={type:o.type,sdp:o.sdp,userid:e.userId_,srctinyid:e.tinyId_,audio:e.subscribeState_.audio,bigVideo:e.subscribeState_.video,auxVideo:e.subscribeState_.auxiliary},e.exchangeSDPResolve_=n,e.exchangeSDPReject_=r,e.signalChannel_.send(ah,a),t.next=23;break;case 19:t.prev=19,t.t0=t.catch(0),e.isSDPExchanging_=!1,r(t.t0);case 23:case"end":return t.stop()}},t,null,[[0,19]])}));return function(e,n){return t.apply(this,arguments)}}())}},{key:"onSubscribeResult",value:(n=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.data.content,r=n.srctinyid,i=n.type,o=n.sdp,a=n.errCode,s=n.errMsg,!this.hitTest(r)){e.next=20;break}if(e.prev=2,this.log_.debug("accept remote answer: "+o),0!==a){e.next=10;break}return e.next=7,this.peerConnection_.setRemoteDescription({type:i,sdp:o});case 7:this.exchangeSDPResolve_&&(this.updateSSRC(o),this.exchangeSDPResolve_()),e.next=11;break;case 10:this.exchangeSDPReject_&&(c="exchange sdp failed",s&&(c+=" ".concat(s)),this.exchangeSDPReject_(new vh({code:a,message:c})));case 11:e.next=17;break;case 13:e.prev=13,e.t0=e.catch(2),this.log_.error("exchange sdp failed: "+e.t0),this.exchangeSDPReject_&&this.exchangeSDPReject_(e.t0);case 17:this.exchangeSDPResolve_=null,this.exchangeSDPReject_=null,this.isSDPExchanging_=!1;case 20:case"end":return e.stop()}},e,this,[[2,13]])})),function(e){return n.apply(this,arguments)})},{key:"updateSSRC",value:function(e){var t=this;try{Lm(e).media.forEach(function(e){if("audio"===e.type){var n=e.ssrcs.find(function(e){return e.value.includes(xs)});n&&(t.ssrc_.audio=n.id)}else{var r=e.ssrcs.find(function(e){return e.value.includes(xs)}),i=e.ssrcs.find(function(e){return e.value.includes(Ps)});r&&(t.ssrc_.video=r.id),i&&(t.ssrc_.auxiliary=i.id)}})}catch(wv){}}},{key:"sendNextSubscription",value:function(){void 0!==this.pendingSubscription_.shift()&&(this.subscriptionRetryCount_=0,this.isSubscriptionPending_=!1,-1!==this.subscriptionTimeout_&&(clearTimeout(this.subscriptionTimeout_),this.subscriptionTimeout_=-1));var e=this.pendingStreams_.shift();if(e&&(this.log_.debug("mark ".concat(e.getType()," stream exit subscription cycle")),e.setInSubscriptionCycle(!1)),this.pendingSubscription_.length>0){var t=this.pendingSubscription_[0];this.log_.info("schedule a pending subscription"),this.doSendSubscription(t.data,t.stream,t.type)}}},{key:"setRemoteStream",value:function(e,t){this.remoteStreams_.set(e,t)}},{key:"getSubscribeState",value:function(){return this.subscribeState_}},{key:"getTrackState",value:function(){return this.trackState_}},{key:"getSSRC",value:function(){return this.ssrc_}},{key:"getMainStreamVideoTrackId",value:function(){var e=this.remoteStreams_.get(xs);if(e){var t=e.getVideoTrack();if(t)return t.id}return""}},{key:"getAuxStreamVideoTrackId",value:function(){var e=this.remoteStreams_.get(Ps);if(e){var t=e.getVideoTrack();if(t)return t.id}return""}},{key:"reconnect",value:function(){var e=this;if(-1===this.reconnectionTimer_){if(this.reconnectionCount_>=30){this.log_.warn("SDK has tried reconnect downlink [".concat(this.userId_,"] for ").concat(30," times, but all failed, please check your network")),this.stopReconnection();var t=new vh({code:fh.DOWNLINK_RECONNECTION_FAILED,message:"downlink reconnect failed, please check your network and re-join room"});return sf({eventType:Qs,error:t}),this.addEventInternal(Xh,"downlink-connection reconnect fail"),void this.emitter_.emit(Vf,t)}if(this.signalChannel_.getCurrentState()!==Qp.CONNECTED)return this.log_.warn("reconnect() signal channel is not connected, suspend reconnection until signal is connected"),void this.signalChannel_.once(Jp,this.reconnect,this);this.reconnectionCount_++,this.log_.warn("reconnect() try to reconnect downlink [".concat(this.reconnectionCount_,"/").concat(30,"]"));var n=gc(this.reconnectionCount_);this.reconnectionTimer_=setTimeout(function(){e.log_.warn("reconnect() downlink [".concat(e.userId_,"] reconnect timeout(").concat(n/1e3,"s), try again")),e.clearReconnectionTimer(),e.reconnect()},n),this.closePeerConnection(),this.uninstallEvents(),this.sentSubscriptionAfterConnected_=!1,this.remoteStreams_.forEach(function(e){e.setConnection(null)}),this.remoteStreams_.clear(),this.initialize(),this.exchangeSDP()}else this.log_.warn("reconnect() downlink is reconnecting, ignore current reconnection")}},{key:"getIsReconnecting",value:function(){return this.isReconnecting_}},{key:"clearReconnectionTimer",value:function(){-1!==this.reconnectionTimer_&&(clearTimeout(this.reconnectionTimer_),this.reconnectionTimer_=-1)}},{key:"stopReconnection",value:function(){this.log_.info("stop downlink reconnection"),this.isReconnecting_=!1,this.reconnectionCount_=0,this.clearReconnectionTimer(),this.signalChannel_.off(Jp,this.reconnect,this)}},{key:"isMainStreamSubscribed",get:function(){return(this.subscribeState_.audio||this.subscribeState_.video)&&(this.trackState_.audio||this.trackState_.video)}},{key:"isAuxStreamSubscribed",get:function(){return this.subscribeState_.auxiliary&&this.trackState_.auxiliary}}]),t}(),Bm=t(function(t){!function(e){function n(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function r(e,t,r,i,o,a){return n((s=n(n(t,e),n(i,a)))<<(c=o)|s>>>32-c,r);var s,c}function i(e,t,n,i,o,a,s){return r(t&n|~t&i,e,t,o,a,s)}function o(e,t,n,i,o,a,s){return r(t&i|n&~i,e,t,o,a,s)}function a(e,t,n,i,o,a,s){return r(t^n^i,e,t,o,a,s)}function s(e,t,n,i,o,a,s){return r(n^(t|~i),e,t,o,a,s)}function c(e,t){var r,c,u,d,l;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var p=1732584193,h=-271733879,f=-1732584194,m=271733878;for(r=0;r<e.length;r+=16)c=p,u=h,d=f,l=m,p=i(p,h,f,m,e[r],7,-680876936),m=i(m,p,h,f,e[r+1],12,-389564586),f=i(f,m,p,h,e[r+2],17,606105819),h=i(h,f,m,p,e[r+3],22,-1044525330),p=i(p,h,f,m,e[r+4],7,-176418897),m=i(m,p,h,f,e[r+5],12,1200080426),f=i(f,m,p,h,e[r+6],17,-1473231341),h=i(h,f,m,p,e[r+7],22,-45705983),p=i(p,h,f,m,e[r+8],7,1770035416),m=i(m,p,h,f,e[r+9],12,-1958414417),f=i(f,m,p,h,e[r+10],17,-42063),h=i(h,f,m,p,e[r+11],22,-1990404162),p=i(p,h,f,m,e[r+12],7,1804603682),m=i(m,p,h,f,e[r+13],12,-40341101),f=i(f,m,p,h,e[r+14],17,-1502002290),p=o(p,h=i(h,f,m,p,e[r+15],22,1236535329),f,m,e[r+1],5,-165796510),m=o(m,p,h,f,e[r+6],9,-1069501632),f=o(f,m,p,h,e[r+11],14,643717713),h=o(h,f,m,p,e[r],20,-373897302),p=o(p,h,f,m,e[r+5],5,-701558691),m=o(m,p,h,f,e[r+10],9,38016083),f=o(f,m,p,h,e[r+15],14,-660478335),h=o(h,f,m,p,e[r+4],20,-405537848),p=o(p,h,f,m,e[r+9],5,568446438),m=o(m,p,h,f,e[r+14],9,-1019803690),f=o(f,m,p,h,e[r+3],14,-187363961),h=o(h,f,m,p,e[r+8],20,1163531501),p=o(p,h,f,m,e[r+13],5,-1444681467),m=o(m,p,h,f,e[r+2],9,-51403784),f=o(f,m,p,h,e[r+7],14,1735328473),p=a(p,h=o(h,f,m,p,e[r+12],20,-1926607734),f,m,e[r+5],4,-378558),m=a(m,p,h,f,e[r+8],11,-2022574463),f=a(f,m,p,h,e[r+11],16,1839030562),h=a(h,f,m,p,e[r+14],23,-35309556),p=a(p,h,f,m,e[r+1],4,-1530992060),m=a(m,p,h,f,e[r+4],11,1272893353),f=a(f,m,p,h,e[r+7],16,-155497632),h=a(h,f,m,p,e[r+10],23,-1094730640),p=a(p,h,f,m,e[r+13],4,681279174),m=a(m,p,h,f,e[r],11,-358537222),f=a(f,m,p,h,e[r+3],16,-722521979),h=a(h,f,m,p,e[r+6],23,76029189),p=a(p,h,f,m,e[r+9],4,-640364487),m=a(m,p,h,f,e[r+12],11,-421815835),f=a(f,m,p,h,e[r+15],16,530742520),p=s(p,h=a(h,f,m,p,e[r+2],23,-995338651),f,m,e[r],6,-198630844),m=s(m,p,h,f,e[r+7],10,1126891415),f=s(f,m,p,h,e[r+14],15,-1416354905),h=s(h,f,m,p,e[r+5],21,-57434055),p=s(p,h,f,m,e[r+12],6,1700485571),m=s(m,p,h,f,e[r+3],10,-1894986606),f=s(f,m,p,h,e[r+10],15,-1051523),h=s(h,f,m,p,e[r+1],21,-2054922799),p=s(p,h,f,m,e[r+8],6,1873313359),m=s(m,p,h,f,e[r+15],10,-30611744),f=s(f,m,p,h,e[r+6],15,-1560198380),h=s(h,f,m,p,e[r+13],21,1309151649),p=s(p,h,f,m,e[r+4],6,-145523070),m=s(m,p,h,f,e[r+11],10,-1120210379),f=s(f,m,p,h,e[r+2],15,718787259),h=s(h,f,m,p,e[r+9],21,-343485551),p=n(p,c),h=n(h,u),f=n(f,d),m=n(m,l);return[p,h,f,m]}function u(e){var t,n="",r=32*e.length;for(t=0;t<r;t+=8)n+=String.fromCharCode(e[t>>5]>>>t%32&255);return n}function d(e){var t,n=[];for(n[(e.length>>2)-1]=void 0,t=0;t<n.length;t+=1)n[t]=0;var r=8*e.length;for(t=0;t<r;t+=8)n[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return n}function l(e){var t,n,r="";for(n=0;n<e.length;n+=1)t=e.charCodeAt(n),r+="0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t);return r}function p(e){return unescape(encodeURIComponent(e))}function h(e){return function(e){return u(c(d(e),8*e.length))}(p(e))}function f(e,t){return function(e,t){var n,r,i=d(e),o=[],a=[];for(o[15]=a[15]=void 0,i.length>16&&(i=c(i,8*e.length)),n=0;n<16;n+=1)o[n]=909522486^i[n],a[n]=1549556828^i[n];return r=c(o.concat(d(t)),512+8*t.length),u(c(a.concat(r),640))}(p(e),p(t))}function m(e,t,n){return t?n?f(t,e):l(f(t,e)):n?h(e):l(h(e))}t.exports?t.exports=m:e.md5=m}(e)}),Gm=function(){function e(t){vt(this,e),this.client_=t.client,this.intervalId_=-1,this.statsCalculator_=new im,this.prevStats_=null,this.blackVideoMap_=new Map,this.remoteStreamMap_=new Map,this.installEvents()}var t,n,r;return _t(e,[{key:"installEvents",value:function(){ym.on(Sm,this.onStreamPlaySuccess,this)}},{key:"uninstallEvents",value:function(){ym.off(Sm,this.onStreamPlaySuccess,this)}},{key:"start",value:function(){var e=this;this.intervalId_>0||(Hn.info("start bad case detector"),this.intervalId_=setInterval(mt(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.detectBlackVideo();case 3:t.next=7;break;case 5:t.prev=5,t.t0=t.catch(0);case 7:case"end":return t.stop()}},t,null,[[0,5]])})),2e3))}},{key:"stop",value:function(){if(-1!==this.intervalId_){if(this.uninstallEvents(),clearInterval(this.intervalId_),this.intervalId_=-1,this.blackVideoMap_.forEach(function(e){var t=e.userId,n=e.type,r=e.startTime,i=Date.now()-r;Hn.warn("[".concat(t,"] ").concat(n," video was blacked out for ").concat(i,"ms"));var o={remoteUserId:t,type:n,delta:i};nf("blackstats-"+JSON.stringify(o))}),this.blackVideoMap_.clear(),this.remoteStreamMap_.size>0){var e=this.getBlackVideoRate();nf("blackrate:"+e)}Hn.info("stop bad case detector")}}},{key:"deleteBlackVideoRecord",value:function(e){var t="".concat(e,"_").concat("main"),n="".concat(e,"_").concat("auxiliary");(this.blackVideoMap_.has(t)||this.blackVideoMap_.has(n))&&(Hn.info("delete black video record [".concat(e,"]")),this.blackVideoMap_.delete(t),this.blackVideoMap_.delete(n))}},{key:"getStats",value:(r=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u,d,l,p,h,f;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=this.client_.getConnections(),n={},r=!0,i=!1,o=void 0,e.prev=5,a=t[Symbol.iterator]();case 7:if(r=(s=a.next()).done){e.next=23;break}if(c=It(s.value,2),u=c[0],(d=c[1]).getPeerConnection()){e.next=11;break}return e.abrupt("continue",20);case 11:return l=d.getSubscribeState(),p=d.getTrackState(),e.next=15,this.statsCalculator_.getReceiverStats(d);case 15:h=e.sent,(f={userId:h.userId,tinyId:u,hasVideo:p.video&&l.video,hasAuxiliary:p.auxiliary&&l.auxiliary,video:{framesDecoded:0},auxiliary:{framesDecoded:0}}).hasVideo&&(f.video.framesDecoded=h.video.framesDecoded),f.hasAuxiliary&&(f.auxiliary.framesDecoded=h.auxiliary.framesDecoded),n[h.userId]=f;case 20:r=!0,e.next=7;break;case 23:e.next=29;break;case 25:e.prev=25,e.t0=e.catch(5),i=!0,o=e.t0;case 29:e.prev=29,e.prev=30,r||null==a.return||a.return();case 32:if(e.prev=32,!i){e.next=35;break}throw o;case 35:return e.finish(32);case 36:return e.finish(29);case 37:return e.abrupt("return",n);case 38:case"end":return e.stop()}},e,this,[[5,25,29,37],[30,,32,36]])})),function(){return r.apply(this,arguments)})},{key:"detectBlackVideo",value:(n=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getStats();case 2:if(t=e.sent,this.prevStats_){e.next=6;break}return this.prevStats_=t,e.abrupt("return");case 6:e.t0=regeneratorRuntime.keys(t);case 7:if((e.t1=e.t0()).done){e.next=17;break}if(n=e.t1.value,this.prevStats_[n]){e.next=11;break}return e.abrupt("continue",7);case 11:r=t[n].tinyId,i=this.client_.getMutedStates(),t[n].hasVideo&&this.prevStats_[n].hasVideo&&i.has(r)&&!i.get(r).videoMuted&&(o=(t[n].video.framesDecoded-this.prevStats_[n].video.framesDecoded)/2,this.handleBlackVideo({userId:n,type:"main",fps:o})),t[n].hasAuxiliary&&this.prevStats_[n].hasAuxiliary&&(a=(t[n].auxiliary.framesDecoded-this.prevStats_[n].auxiliary.framesDecoded)/2,this.handleBlackVideo({userId:n,type:"auxiliary",fps:a})),e.next=7;break;case 17:this.prevStats_=t;case 18:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"handleBlackVideo",value:(t=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=t.userId,r=t.fps,i=t.type,o="".concat(n,"_").concat(i),this.remoteStreamMap_.has(o)&&(this.remoteStreamMap_.get(o).isFPSDetected=!0),0===r?this.blackVideoMap_.has(o)||(Hn.info("[".concat(n,"] ").concat(i," video black detected")),this.blackVideoMap_.set(o,{userId:n,type:i,startTime:Date.now()})):r>0&&(this.remoteStreamMap_.has(o)&&(this.remoteStreamMap_.get(o).isRendered=!0),this.blackVideoMap_.has(o)&&(a=Date.now()-this.blackVideoMap_.get(o).startTime,Hn.info("[".concat(n,"] ").concat(i," video was blacked out for ").concat(a,"ms")),s={remoteUserId:n,type:i,delta:a},nf("blackstats-"+JSON.stringify(s)),this.blackVideoMap_.delete(o)));case 4:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"onStreamPlaySuccess",value:function(e){var t=e.stream;if(t.isRemote()){var n="".concat(t.getUserId(),"_").concat(t.getType());if(this.remoteStreamMap_.has(n))this.remoteStreamMap_.get(n).remoteStream=t;else this.remoteStreamMap_.set(n,{isRendered:!1,isFPSDetected:!1,remoteStream:t})}}},{key:"getBlackVideoRate",value:function(){return 0===this.remoteStreamMap_.size?0:xt(this.remoteStreamMap_.values()).filter(function(e){return e.isFPSDetected&&!e.isRendered}).length/this.remoteStreamMap_.size}}]),e}(),Wm=function e(t){vt(this,e),this.userId=t.userId,this.tinyId=t.tinyId,this.role=t.role===Es?Is:"audience"},Hm=0,Jm=1,zm=2;function Qm(e){var t=e.retryFunction,n=e.settings,r=e.onError,i=e.onRetrying,o=e.context;return function(){for(var e=this,a=arguments.length,s=new Array(a),c=0;c<a;c++)s[c]=arguments[c];var u=n.retries||5,d=0,l=-1,p=Hm,h=function(){var a=mt(regeneratorRuntime.mark(function a(c,f){var m,v,g,_;return regeneratorRuntime.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.prev=0,m=o||e,a.next=4,t.apply(m,s);case 4:v=a.sent,d=0,c(v),a.next=14;break;case 9:a.prev=9,a.t0=a.catch(0),g=function(){clearTimeout(l),d=0,p=zm,f(a.t0)},_=function(){p!==zm&&d<u?(d++,p=Jm,yc(i)&&i(d,g),l=setTimeout(function(){l=-1,h(c,f)},n.timeout||1e3)):g()},r(a.t0,_,f);case 14:case"end":return a.stop()}},a,null,[[0,9]])}));return function(e,t){return a.apply(this,arguments)}}();return new Promise(h)}}var qm=function(){function e(t){if(vt(this,e),this.mode_=t.mode,this.sdpSemantics_="plan-b",void 0!==t.sdpSemantics?this.sdpSemantics_=t.sdpSemantics:bf()&&(this.sdpSemantics_="unified-plan"),this.sdkAppId_=t.sdkAppId,this.userId_=t.userId,this.log_=new om({id:"c|"+this.userId_,direction:"local",type:""}),this.userSig_=t.userSig,this.roomId_=0,this.useStringRoomId_=t.useStringRoomId||!1,this.recordId_=null,this.pureAudioPushMode_=null,this.version_=t.version,this.log_.info("using sdpSemantics: "+this.sdpSemantics_),Hn.setConfig({sdkAppId:this.sdkAppId_,userId:this.userId_,version:this.version_}),void 0!==t.recordId){if(!Number.isInteger(Number(t.recordId)))throw new vh({code:fh.INVALID_PARAMETER,message:"recordId must be an integer number"});this.recordId_=t.recordId}this.signalChannel_=null,this.isScreenShareOnly_=0,void 0!==t.isScreenShareOnly&&(this.isScreenShareOnly_=t.isScreenShareOnly?1:0),this.role_=Is,this.privateMapKey_="",this.tinyId_=0,this.env_="",this.proxy_=null,this.turnServers_=[],this.connections_=new Map,this.mutedStates_=new Map,this.userMap_=new Map,this.localStream_=null,this.isPublishing_=!1,this.isUplinkClosing_=!1,this.uplinkConnection_=null,this.emitter_=new Wp,this.signalInfo_={},this.isSignalReady_=!1,this.isJoined_=!1,this.heartbeat_=-1,this.stats_=new im,this.joinTimeout_=-1,this.publishTimeout_=-1,this.unpublishTimeout_=-1,this.networkQuality_=null,this.badCaseDetector_=null,this.autoSubscribe_=!!Sc(t.autoSubscribe)||t.autoSubscribe,this.startJoinTimestamp_=new Date,this.joinedTimestamp_=new Date,this.joinOptions={},this.downlinkVideoHealthStats_={},this.uplinkVideoHealthStats_={},this.basis_={browser:mf().browserName+"/"+mf().browserVersion,os:Pf(),displayResolution:Af(),isScreenShareSupported:kf(),isWebRTCSupported:vf(),isGetUserMediaSupported:Of(),isWebAudioSupported:Df(),isWebSocketsSupported:"WebSocket"in window&&2===window.WebSocket.CLOSING},this.initBussinessInfo_(t),this.publishedCDN_=!1,this.publishCDNData_=null,this.mixedMCU_=!1,this.mixTranscodeData_=null}var t,n,r,i,o,a,s,c,u,d,l,p,h,f,m,v,g;return _t(e,[{key:"initBussinessInfo_",value:function(e){this.bussinessInfo_=e.bussinessInfo;var t={};if("string"==typeof e.bussinessInfo&&(t=JSON.parse(e.bussinessInfo)),void 0!==e.pureAudioPushMode){if(!Number.isInteger(Number(e.pureAudioPushMode)))throw new vh({code:fh.INVALID_PARAMETER,message:"pureAudioPushMode must be an integer number"});this.pureAudioPushMode_=e.pureAudioPushMode,t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.pure_audio_push_mod=this.pureAudioPushMode_}if(void 0!==e.streamId){if(!("string"==typeof e.streamId&&String(e.streamId)&&String(e.streamId).length<=64))throw new vh({code:fh.INVALID_PARAMETER,message:"streamId must be a sting literal within 64 bytes, and not be empty"});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_streamid_main=e.streamId}if(void 0!==e.userDefineRecordId){if(null===e.userDefineRecordId.match(/^[A-Za-z0-9_-]{1,64}$/gi))throw new vh({code:fh.INVALID_PARAMETER,message:"userDefineRecordId must be a sting literal contains (a-zA-Z),(0-9), underline and hyphen, within 64 bytes, and not be empty"});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_record_id=e.userDefineRecordId}if(void 0!==e.userDefinePushArgs){if(!("string"==typeof e.userDefinePushArgs&&String(e.userDefinePushArgs)&&String(e.userDefinePushArgs).length<=256))throw new vh({code:fh.INVALID_PARAMETER,message:"userDefinePushArgs must be a sting literal within 256 bytes, and not be empty"});t.Str_uc_params||(t.Str_uc_params={}),t.Str_uc_params.userdefine_push_args=e.userDefinePushArgs}uf(t)||(this.bussinessInfo_=JSON.stringify(t))}},{key:"setProxyServer",value:function(e){if(this.log_.info("set proxy server: ".concat(JSON.stringify(e))),"string"==typeof e){if(!e.startsWith("wss://"))throw new vh({code:fh.INVALID_PARAMETER,message:'proxy server url shall be started with "wss://"'});this.proxy_=e}else if(vc(e)){var t=e.websocketProxy,n=e.loggerProxy;t&&(this.proxy_=t),n&&Rs(n)}}},{key:"getUrl",value:function(e){var t=fc(e);return!hc()&&this.proxy_&&(t=this.proxy_),t}},{key:"getBackupUrl",value:function(){return this.proxy_?this.proxy_:fc(bs)}},{key:"getUserId",value:function(){return this.userId_}},{key:"getTinyId",value:function(){return this.tinyId_}},{key:"setTurnServer",value:function(e){this.log_.info("set turn server: "+JSON.stringify(e));var t=[];Array.isArray(e)?e.forEach(function(e){return t.push(wc(e))}):vc(e)&&t.push(wc(e)),this.turnServers_=t}},{key:"initialize",value:function(){var e=this;return new Promise(function(t,n){e.log_.info("setup signal channel"),e.signalChannel_=new lf({sdkAppId:e.sdkAppId_,userId:e.userId_,userSig:e.userSig_,url:e.getUrl(Ss),backupUrl:e.getUrl(bs),version:e.version_}),e.networkQuality_||(e.networkQuality_=new am({connections:e.connections_,signalChannel:e.signalChannel_,userId:e.userId_}),e.networkQuality_.on(Yf,function(t){e.emitter_.emit(Yf,t)})),e.deviceDetector_||(e.deviceDetector_=new sm),e.subscriptionManager_||(e.subscriptionManager_=new wm({client:e})),e.badCaseDetector_||(e.badCaseDetector_=new Gm({client:e})),e.signalChannel_.on(Hp,function(t){switch(e.log_.info("SignalChannel state changed from ".concat(t.prevState," to ").concat(t.state)),t.state){case Qp.CONNECTED:t.prevState===Qp.RECONNECTING?(e.log_.warn("signal channel reconnect successfully"),e.syncUserList(),af({eventType:Zs})):t.prevState===Qp.CONNECTING&&af({eventType:Ys})}e.emitter_.emit(Hf,t)}),e.signalChannel_.on(zp,function(t){e.isSignalReady_?(e.closeUplink(),e.closeConnections(),e.emitter_.emit(Zf,t)):n(t)}),e.signalChannel_.on(Kp.CHANNEL_SETUP_FAILED,function(t){e.log_.error("signal channel setup failed"),n(t)}),e.signalChannel_.on(Kp.CHANNEL_SETUP_SUCCESS,function(n){e.signalInfo_=n.signalInfo,e.tinyId_=e.signalInfo_.tinyId,e.isSignalReady_||(e.isSignalReady_=!0,t())}),e.signalChannel_.on(Kp.PEER_JOIN,e.onPeerJoin,e),e.signalChannel_.on(Kp.PEER_LEAVE,e.onPeerLeave,e),e.signalChannel_.on(Kp.STREAM_ADDED,function(t){e.onRemoteStreamAdded(t.data)}),e.signalChannel_.on(Kp.STREAM_REMOVED,function(t){e.onRemoteStreamRemoved(t.data)}),e.signalChannel_.on(Kp.UPDATE_REMOTE_MUTE_STAT,function(t){e.onPublishedUserList(t.data),e.onUpdateRemoteMuteStat(t.data)}),e.signalChannel_.on(Kp.CLINET_BANNED,function(){var t=mt(regeneratorRuntime.mark(function t(n){var r,i,o;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=n.data.content,t.next=3,e.reset();case 3:"banned"===r.type?(i="you got banned by account admin",e.log_.error("user was banned because of "+i),e.onClientBanned(i)):"kick"===r.type?(o="duplicated userId joining the room",e.log_.error("user was banned because of "+o),e.onClientBanned(o)):(e.log_.error("Relay server timeout observed [".concat(r.type,"]")),e.emitter_.emit(Zf,new vh({code:fh.SERVER_TIMEOUT,message:"Relay server timeout observed"})));case 4:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()),e.signalChannel_.on(Kp.REQUEST_REBUILD_SESSION,function(t){e.signalInfo_=t.signalInfo;var n=[];e.connections_&&n.push(0);var r=[],i=!0,o=!1,a=void 0;try{for(var s,c=e.connections_[Symbol.iterator]();!(i=(s=c.next()).done);i=!0){var u=It(s.value,2),d=u[0],l=u[1];n.push(d);var p=l.getPeerConnection();if(p){var h=p.remoteDescription;h&&r.push(h.sdp)}}}catch(m){o=!0,a=m}finally{try{i||null==c.return||c.return()}finally{if(o)throw a}}var f={socketid:e.signalInfo_.socketId,tinyid:e.tinyId_,appid:e.sdkAppId_,openid:e.userId_,sessionid:String(e.roomId_),sids:n,relayInfo:e.signalInfo_.relayInnerIp,remotesdp:r};e.log_.debug("reconnect - rebuild session with data: ".concat(JSON.stringify(f))),e.signalChannel_.send(eh,f)}),e.signalChannel_.on(Kp.CLIENT_REJOIN,function(){var t=mt(regeneratorRuntime.mark(function t(n){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.reJoin();case 2:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}()),e.getUserList=Qm({retryFunction:e.getUserList,settings:{retries:3},onError:function(e,t){return t()},onRetrying:function(t){e.log_.info("retrying to get user list [".concat(t,"/3]"))}}),e.signalChannel_.connect()})}},{key:"join",value:(g=mt(regeneratorRuntime.mark(function e(t){var n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=mf(),(r=hc())||(r="qcloud",this.proxy_&&(this.proxy_.startsWith(_s)?r="trtc":this.proxy_.startsWith(ys)&&(r="webrtc"))),this.env_=r,tf({env:r,sdkAppId:this.sdkAppId_,userId:this.userId_,version:this.version_,browserVersion:n.name+n.version,ua:navigator.userAgent}),this.uploadTrtcStats(),e.t0=!vf(),e.t0){e.next=11;break}return e.next=10,_f();case 10:e.t0=!e.sent;case 11:if(!e.t0){e.next=13;break}throw new vh({code:fh.NOT_SUPPORTED,message:"the browser does NOT support WebRTC!"});case 13:if(!this.useStringRoomId_){e.next=19;break}if(bc(t.roomId)&&/^[A-Za-z\d\s!#$%&()+\-:;<=.>?@[\]^_{}|~,]{1,64}$/.test(t.roomId)){e.next=17;break}throw new vh({code:fh.INVALID_PARAMETER,message:"roomId must be validate string when useStringRoomId is true"});case 17:e.next=22;break;case 19:if("number"==typeof t.roomId&&/^[1-9]\d*$/.test(String(t.roomId))&&t.roomId<4294967295){e.next=22;break}throw new vh({code:fh.INVALID_PARAMETER,message:"roomId must be validate integer when useStringRoomId is false"});case 22:return this.joinOptions=t,this.startJoinTimestamp_=new Date,Yh(this.userId_,{eventId:Lh,eventDesc:"joining room",timestamp:Eo(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=25,e.next=28,this.initialize();case 28:e.next=34;break;case 30:throw e.prev=30,e.t1=e.catch(25),sf({eventType:Ns,error:e.t1}),e.t1;case 34:return e.prev=34,e.next=37,this.doJoin(t);case 37:e.next=43;break;case 39:throw e.prev=39,e.t2=e.catch(34),sf({eventType:Ns,error:e.t2}),e.t2;case 43:this.joinedTimestamp_=new Date,i=this.joinedTimestamp_-this.startJoinTimestamp_,af({eventType:Ms,delta:i}),af({eventType:Ns});case 47:case"end":return e.stop()}},e,this,[[25,30],[34,39]])})),function(e){return g.apply(this,arguments)})},{key:"uploadTrtcStats",value:(v=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="undefined",n="undefined",e.prev=2,e.next=5,Rv.getMicrophones();case 5:r=e.sent,t=r&&r.length,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),t="undefined";case 12:return e.prev=12,e.next=15,Rv.getCameras();case 15:i=e.sent,n=i&&i.length,e.next=22;break;case 19:e.prev=19,e.t1=e.catch(12),n="undefined";case 22:return o={microphone:t,camera:n},e.t2=this.basis_.isWebRTCSupported,e.t3=this.basis_.isGetUserMediaSupported,e.t4=this.basis_.isWebSocketsSupported,e.t5=this.basis_.isScreenShareSupported,e.t6=this.basis_.isWebAudioSupported,e.next=30,_f();case 30:return e.t7=e.sent,e.next=33,yf();case 33:e.t8=e.sent,a={webRTC:e.t2,getUserMedia:e.t3,webSocket:e.t4,screenShare:e.t5,webAudio:e.t6,h264Encode:e.t7,h264Decode:e.t8},s={browser:this.basis_.browser,os:this.basis_.os,trtc:a,devices:o},nf("trtcstats-"+JSON.stringify(s)),this.log_.info("TrtcStats-"+JSON.stringify(s));case 38:case"end":return e.stop()}},e,this,[[2,9],[12,19]])})),function(){return v.apply(this,arguments)})},{key:"getVersion",value:function(){var e=this.version_.split(".");return 1e3*parseInt(e[0])+100*parseInt(e[1])+parseInt(e[2])}},{key:"doJoin",value:function(e){var t=this;return new Promise(function(n,r){if(!t.isSignalReady_)throw new vh({code:fh.INVALID_OPERATION,message:"SignalChannel is not ready yet"});if(t.isJoined_)throw new vh({code:fh.INVALID_OPERATION,message:"duplicate join() called"});t.roomId_=e.roomId,void 0!==e.role&&(t.role_=e.role);var i="";void 0!==e.privateMapKey&&(i=e.privateMapKey),t.privateMapKey_=i,t.log_.info("Join() => joining room: ".concat(e.roomId," useStringRoomId: ").concat(t.useStringRoomId_," mode: ").concat(t.mode_," role: ").concat(t.role_));var o,a=t.signalInfo_,s={openid:a.openId,tinyid:a.tinyId,peerconnectionport:"",useStrRoomid:!!t.useStringRoomId_&&1,roomid:String(e.roomId),sdkAppID:String(t.sdkAppId_),socketid:a.socketId,userSig:t.userSig_,privMapEncrypt:i,privMap:"",relayip:a.relayInnerIp,dataport:a.dataPort,stunport:a.stunPort,checkSigSeq:a.checkSigSeq,pstnBizType:0,pstnPhoneNumber:null,recordId:t.recordId_,role:"user",jsSdkVersion:String(t.getVersion()),sdpSemantics:t.sdpSemantics_,browserVersion:fs,closeLocalMedia:!0,trtcscene:"live"===t.mode_?Ts:ws,trtcrole:t.role_===Is?Es:Cs,bussinessInfo:t.bussinessInfo_,isAuxUser:t.isScreenShareOnly_,autoSubscribe:t.autoSubscribe_};t.joinTimeout_=setTimeout(function(){t.log_.error("join room timeout observed  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),r(new vh({code:fh.JOIN_ROOM_FAILED,message:"join room timeout"}))},5e3),t.signalChannel_.sendWithReport($p,s,(o=0,navigator&&navigator.connection&&navigator.connection.effectiveType&&(o="4g"===navigator.connection.effectiveType?1:4),navigator&&navigator.connection&&navigator.connection.type&&("wifi"===navigator.connection.type?o=1:"cellular"===navigator.connection.type&&(o=4)),{AbilityOption:{GeneralLimit:{CPULimit:{uint32_CPU_num:String(navigator.hardwareConcurrency||0),str_CPU_name:String(navigator.platform),uint32_CPU_maxfreq:String(0),model:"",uint32_total_memory:String(0)},uint32_terminal_type:String(ha?4:ua?2:ca?3:za?12:Ja?5:Qa?13:1),uint32_device_type:String(0),str_os_verion:ha?"Android":ua?"iPhone":ca?"iPad":za?"Mac":Ja?"Windows":Qa?"Linux":"unknown",uint32_link_type:String(1),str_client_version:"4.8.5",uint32_net_type:String(o),ua:navigator.userAgent,version:""}}})),t.signalChannel_.once(Kp.JOIN_ROOM_RESULT,function(e){clearTimeout(t.joinTimeout_),t.joinTimeout_=-1;var i=e.data.content.ret;i?(t.log_.error("Join room failed result: "+i+" error: "+e.data.content.error),r(new vh({code:fh.JOIN_ROOM_FAILED,extraCode:i,message:"Failed to join room - "+e.data.content.error}))):(t.isJoined_=!0,t.log_.info("Join room success, start heartbeat"),t.startHeartbeat(),t.badCaseDetector_&&t.badCaseDetector_.start(),t.syncUserList(),n())})})}},{key:"connectSignalBeforeReJoin",value:function(){var e=this;return new Promise(function(t,n){e.log_.warn("connectSignalBeforeReJoin() try to connect signal before reJoin"),e.isSignalReady_=!1,e.signalChannel_.close(),e.signalChannel_.once(Kp.CHANNEL_SETUP_SUCCESS,function(n){e.log_.warn("connectSignalBeforeReJoin() signal setup successfully"),t()}),e.signalChannel_.once(zp,function(t){e.log_.warn("connectSignalBeforeReJoin() signal setup failed"),n(t)}),e.signalChannel_.connect()})}},{key:"reJoin",value:(m=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isJoined_){e.next=3;break}return this.log_.warn("reJoin() you haven't join room yet, abort reJoin"),e.abrupt("return");case 3:return this.isJoined_=!1,e.prev=4,this.log_.warn("reJoin() try to reJoin room: ".concat(this.joinOptions.roomId)),this.subscriptionManager_&&this.subscriptionManager_.markAllStream(),e.next=9,this.connectSignalBeforeReJoin();case 9:return e.next=11,this.doJoin(this.joinOptions);case 11:if(this.log_.warn("reJoin() reJoin successfully"),af({eventType:Ls}),e.prev=13,!this.uplinkConnection_||!this.localStream_||this.uplinkConnection_.getIsReconnecting()){e.next=17;break}return e.next=17,this.republish();case 17:e.next=21;break;case 19:e.prev=19,e.t0=e.catch(13);case 21:e.next=28;break;case 23:e.prev=23,e.t1=e.catch(4),this.log_.warn("reJoin() reJoin failed"+e.t1),sf({eventType:Ls,error:e.t1}),this.emitter_.emit(Zf,new vh({code:fh.JOIN_ROOM_FAILED,message:"reJoin room: ".concat(this.joinOptions.roomId," failed, please check your network")}));case 28:case"end":return e.stop()}},e,this,[[4,23],[13,19]])})),function(){return m.apply(this,arguments)})},{key:"republish",value:(f=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.log_.warn("republish() try to re-publish localStream"),t=this.localStream_,e.next=5,this.doUnpublish(t);case 5:return e.next=7,this.publish(t);case 7:this.log_.warn("republish() re-publish localStream successfully"),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(0),this.log_.warn("republish() re-publish localStream failed "+e.t0),e.t0;case 14:case"end":return e.stop()}},e,this,[[0,10]])})),function(){return f.apply(this,arguments)})},{key:"leave",value:(h=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u,d,l,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return Yh(this.userId_,{eventId:Uh,eventDesc:"leaving room",timestamp:Eo(),userId:this.userId_,tinyId:this.tinyId_}),e.prev=1,e.next=4,this.doHeartbeat();case 4:e.next=8;break;case 6:e.prev=6,e.t0=e.catch(1);case 8:t=!1,n=!0,r=!1,i=void 0,e.prev=12,o=this.connections_[Symbol.iterator]();case 14:if(n=(a=o.next()).done){e.next=24;break}if((s=It(a.value,2))[0],c=s[1],t){e.next=21;break}return e.next=19,c.getVideoHealthStats();case 19:(u=e.sent).valid&&(t=!0,this.downlinkVideoHealthStats_=u);case 21:n=!0,e.next=14;break;case 24:e.next=30;break;case 26:e.prev=26,e.t1=e.catch(12),r=!0,i=e.t1;case 30:e.prev=30,e.prev=31,n||null==o.return||o.return();case 33:if(e.prev=33,!r){e.next=36;break}throw i;case 36:return e.finish(33);case 37:return e.finish(30);case 38:return d={userAgent:navigator.userAgent},l={uplink:this.uplinkVideoHealthStats_,downlink:this.downlinkVideoHealthStats_},nf("healthstats-"+JSON.stringify(d)),nf("healthstats-"+JSON.stringify(l)),this.log_.info("HealthStats-"+JSON.stringify(l)),e.prev=43,e.next=46,this.doLeave();case 46:e.next=50;break;case 48:e.prev=48,e.t2=e.catch(43);case 50:af({eventType:Us}),p=Math.floor((new Date-this.joinedTimestamp_)/1e3),af({eventType:Vs,delta:p});case 53:case"end":return e.stop()}},e,this,[[1,6],[12,26,30,38],[31,,33,37],[43,48]])})),function(){return h.apply(this,arguments)})},{key:"doLeave",value:function(){var e=this;return new Promise(function(){var t=mt(regeneratorRuntime.mark(function t(n,r){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.isJoined_){t.next=2;break}return t.abrupt("return",n());case 2:return e.log_.info("leave() => leaving room"),e.signalChannel_.send(Yp),t.next=6,e.reset();case 6:return t.abrupt("return",n());case 7:case"end":return t.stop()}},t)}));return function(e,n){return t.apply(this,arguments)}}())}},{key:"clearPublishTimeout",value:function(){-1!==this.publishTimeout_&&(clearTimeout(this.publishTimeout_),this.publishTimeout_=-1)}},{key:"clearUnpublishTimeout",value:function(){this.unpublishTimeout_&&(clearTimeout(this.unpublishTimeout_),this.unpublishTimeout_=-1)}},{key:"clearNetworkQuality",value:function(){this.networkQuality_&&(this.networkQuality_.stop(),this.networkQuality_=null)}},{key:"closeConnections",value:function(){var e=this;this.connections_.forEach(function(t){e.closeDownLink(t.getTinyId())})}},{key:"destroy",value:function(){if(this.isJoined_)throw this.log_.warn("please call leave() before destroy() client"),new vh({code:fh.INVALID_OPERATION,message:"Please call leave() before destory() the client"});this.log_.info("destroying SignalChannel"),this.signalChannel_&&(this.signalChannel_.close(),this.signalChannel_=null)}},{key:"reset",value:(p=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.stopHeartbeat(),this.closeConnections(),this.mutedStates_.clear(),this.clearPublishTimeout(),this.clearUnpublishTimeout(),this.clearNetworkQuality(),this.badCaseDetector_&&this.badCaseDetector_.stop(),e.prev=7,e.next=10,this.closeUplink();case 10:e.next=14;break;case 12:e.prev=12,e.t0=e.catch(7);case 14:this.isJoined_=!1,this.isSignalReady_=!1,this.destroy();case 17:case"end":return e.stop()}},e,this,[[7,12]])})),function(){return p.apply(this,arguments)})},{key:"syncUserList",value:(l=mt(regeneratorRuntime.mark(function e(){var t,n=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getUserList();case 3:t=e.sent,0!==this.userMap_.size&&this.userMap_.forEach(function(e){t.findIndex(function(t){return t.userId===e.userId})<0&&(n.log_.info("peer leave detected"),n.cleanUser({userId:e.userId,tinyId:e.tinyId}))}),t.forEach(function(e){var t=e.userId;n.userMap_.has(t)||t===n.userId_||(n.userMap_.set(t,e),n.emitter_.emit(Jf,{userId:t}))}),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),this.log_.warn("sync user list failed: "+e.t0);case 11:case"end":return e.stop()}},e,this,[[0,8]])})),function(){return l.apply(this,arguments)})},{key:"getUserList",value:function(){var e=this;return new Promise(function(t,n){e.signalChannel_.send(hh),e.signalChannel_.once(Kp.USER_LIST_RES,function(e){var n=e.data.content.userlist.map(function(e){var t=e.userid,n=e.srctinyid,r=e.role;return new Wm({userId:t,tinyId:n,role:r})});t(n)}),setTimeout(n,2e3)})}},{key:"publish",value:function(e){var t=this;return new Promise(function(){var n=mt(regeneratorRuntime.mark(function n(r,i){var o,a,s,c;return regeneratorRuntime.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(hf.result){n.next=2;break}throw new vh({code:fh.NOT_SUPPORTED,message:"the browser does NOT support TRTC!"});case 2:if(t.isJoined_){n.next=4;break}throw new vh({code:fh.INVALID_OPERATION,message:"please call join() before publish()"});case 4:if("live"!==t.mode_||"audience"!==t.role_){n.next=6;break}throw new vh({code:fh.INVALID_OPERATION,message:"no permission to publish() under live/".concat("audience",', please call switchRole("').concat(Is,'") firstly before publish()')});case 6:if(!t.localStream_){n.next=8;break}throw new vh({code:fh.INVALID_OPERATION,message:"duplicate publishing, please unpublish and then re-publish  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"});case 8:if(!t.isPublishing_){n.next=10;break}throw new vh({code:fh.INVALID_OPERATION,message:"previous publishing is ongoing, please avoid re-publishing  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"});case 10:return t.isPublishing_=!0,o=new Date,t.log_.info("publish() => publishing local stream"),(a=new jm({userId:t.userId_,tinyId:t.tinyId_,client:t,isUplink:!0,signalChannel:t.signalChannel_})).initialize(),t.publishTimeout_=setTimeout(function(){t.isPublishing_=!1,a.close(),t.log_.error("failed to publish because of timeout  https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html");var e=new vh({code:fh.UNKNOWN,message:"publish timeout"});sf({eventType:js,error:e}),i(e)},1e4),n.prev=16,n.next=19,a.publish(e);case 19:t.localStream_=n.sent,t.log_.info("local stream publish successfully"),t.clearPublishTimeout(),t.isPublishing_=!1,t.localStream_.setConnection(a),t.uplinkConnection_=a,s=new Date,c=s-o,af({eventType:js}),af({eventType:Fs,delta:c}),e.hasAudio()&&Yh(t.userId_,{eventId:_h,eventDesc:"publish audio track",timestamp:Eo(),userId:t.userId_,tinyId:t.tinyId_}),e.hasVideo()&&Yh(t.userId_,{eventId:gh,eventDesc:"publish video track",timestamp:Eo(),userId:t.userId_,tinyId:t.tinyId_}),t.networkQuality_&&t.networkQuality_.setUplinkConnection(t.uplinkConnection_),t.deviceDetector_&&t.deviceDetector_.setLocalStream(t.localStream_),ym.emit(bm,{localStream:t.localStream_}),r(),n.next=45;break;case 37:n.prev=37,n.t0=n.catch(16),t.clearPublishTimeout(),t.isPublishing_=!1,a.close(),t.log_.error("failed to publish stream",n.t0),sf({eventType:js,error:n.t0}),i(n.t0);case 45:a.on(Vf,function(){var e=mt(regeneratorRuntime.mark(function e(n){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((r=n.getCode())!==fh.ICE_TRANSPORT_ERROR){e.next=3;break}return e.abrupt("return");case 3:if(r!==fh.UPLINK_RECONNECTION_FAILED){e.next=6;break}return e.next=6,t.closeUplink();case 6:t.emitter_.emit(Zf,n);case 7:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}());case 46:case"end":return n.stop()}},n,null,[[16,37]])}));return function(e,t){return n.apply(this,arguments)}}())}},{key:"unpublish",value:function(e){var t=this;return new Promise(function(n,r){if(t.isPublishing_)throw new vh({code:fh.INVALID_OPERATION,message:"unpublish() is being called during publish() is ongoing"});if(e!==t.localStream_)throw new vh({code:fh.INVALID_PARAMETER,message:"stream has not been published yet"});t.log_.info("unpublish() => unpublishing local stream"),t.doUnpublish().then(function(){af({eventType:Bs}),n()}).catch(function(e){sf({eventType:Bs,error:e}),r(e)})})}},{key:"doUnpublish",value:function(){var e=this;return new Promise(function(t,n){e.signalChannel_.send(oh),e.unpublishTimeout_=setTimeout(mt(regeneratorRuntime.mark(function n(){return regeneratorRuntime.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.log_.warn("unpublish() timeout observed"),n.next=3,e.closeUplink();case 3:return n.abrupt("return",t());case 4:case"end":return n.stop()}},n)})),5e3),e.signalChannel_.once(Kp.UNPUBLISH_RESULT,function(){var n=mt(regeneratorRuntime.mark(function n(r){return regeneratorRuntime.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.clearUnpublishTimeout(),e.log_.info("received UNPUBLISH_RESULT, close uplink connection"),n.next=4,e.closeUplink();case 4:return n.abrupt("return",t());case 5:case"end":return n.stop()}},n)}));return function(e){return n.apply(this,arguments)}}())})}},{key:"closeUplink",value:(d=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isUplinkClosing_){e.next=3;break}return this.log_.warn("uplink is closing, abort current closeUplink request"),e.abrupt("return");case 3:if(this.isUplinkClosing_=!0,!this.uplinkConnection_){e.next=17;break}return e.next=7,this.uplinkConnection_.getVideoHealthStats();case 7:this.uplinkVideoHealthStats_=e.sent,this.uplinkConnection_.getIsReconnecting()&&this.uplinkConnection_.stopReconnection(),this.uplinkConnection_.close(),this.uplinkConnection_=null,this.networkQuality_&&this.networkQuality_.setUplinkConnection(null),this.localStream_.hasAudio()&&Yh(this.userId_,{eventId:Sh,eventDesc:"unpublish audio track",timestamp:Eo(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.hasVideo()&&Yh(this.userId_,{eventId:yh,eventDesc:"unpublish video track",timestamp:Eo(),userId:this.userId_,tinyId:this.tinyId_}),this.localStream_.setConnection(null),this.localStream_=null,this.deviceDetector_&&this.deviceDetector_.setLocalStream(null);case 17:this.isUplinkClosing_=!1;case 18:case"end":return e.stop()}},e,this)})),function(){return d.apply(this,arguments)})},{key:"closeDownLink",value:function(e){var t=this.connections_.get(e);t&&(t.getIsReconnecting()&&t.stopReconnection(),this.subscriptionManager_&&this.subscriptionManager_.delete(t.getUserId()),t.close(),this.connections_.delete(e),this.mutedStates_.delete(e))}},{key:"subscribe",value:(u=mt(regeneratorRuntime.mark(function e(t,n){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new vh({code:fh.INVALID_PARAMETER,message:"stream is undefined or null"});case 2:if(t.isRemote()){e.next=4;break}throw new vh({code:fh.INVALID_PARAMETER,message:"try to subscribe a local stream"});case 4:return this.log_.info("subscribe() => subscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream with options: ").concat(JSON.stringify(n))),Sc(n)&&(n={audio:!0,video:!0}),Sc(n.video)&&(n.video=!0),Sc(n.audio)&&(n.audio=!0),r=t.getConnection(),e.prev=9,e.next=12,r.subscribe(t,n);case 12:this.subscriptionManager_&&this.subscriptionManager_.addSubscriptionRecord(t.getUserId(),t,n),af({eventType:Gs}),e.next=20;break;case 16:throw e.prev=16,e.t0=e.catch(9),sf({eventType:Gs,error:e.t0}),e.t0;case 20:case"end":return e.stop()}},e,this,[[9,16]])})),function(e,t){return u.apply(this,arguments)})},{key:"unsubscribe",value:(c=mt(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw new vh({code:fh.INVALID_PARAMETER,message:"stream is undefined or null"});case 2:if(t.isRemote()){e.next=4;break}throw new vh({code:fh.INVALID_PARAMETER,message:"try to unsubscribe a local stream"});case 4:return this.log_.info("unsubscribe() => unsubscribe to [".concat(t.getUserId(),"] ").concat(t.getType()," stream")),n=t.getConnection(),e.prev=6,e.next=9,n.unsubscribe(t);case 9:this.subscriptionManager_&&this.subscriptionManager_.addUnsubscriptionRecord(t.getUserId(),t),af({eventType:Ws}),e.next=17;break;case 13:throw e.prev=13,e.t0=e.catch(6),sf({eventType:Ws,error:e.t0}),e.t0;case 17:case"end":return e.stop()}},e,this,[[6,13]])})),function(e){return c.apply(this,arguments)})},{key:"switchRole",value:(s=mt(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("live"===this.mode_){e.next=2;break}throw new vh({code:fh.INVALID_PARAMETER,message:"role is only valid in live mode"});case 2:if(t===Is||"audience"===t){e.next=4;break}throw new vh({code:fh.INVALID_PARAMETER,message:"role could only be set to a value as ".concat(Is," or ").concat("audience")});case 4:if(this.role_===t){e.next=13;break}if(this.role_=t,this.log_.info("switchRole() => switch role to: "+t),this.isJoined_){e.next=9;break}return e.abrupt("return");case 9:return e.next=11,this.leave();case 11:return e.next=13,this.join({role:t,roomId:this.roomId_,privateMapKey:this.privateMapKey_});case 13:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"on",value:function(e,t){this.emitter_.on(e,t)}},{key:"off",value:function(e,t){"*"===e?this.emitter_.removeAllListeners():this.emitter_.off(e,t)}},{key:"getRemoteMutedState",value:function(){var e=this,t=[];return this.mutedStates_.forEach(function(n,r,i){var o=e.connections_.get(r);o&&t.push(St({userId:o.getUserId()},n))}),t}},{key:"getTransportStats",value:(a=mt(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t={rtt:0},!this.uplinkConnection_){e.next=6;break}return e.next=4,this.stats_.getSenderStats(this.uplinkConnection_);case 4:n=e.sent,t.rtt=n.rtt;case 6:return e.abrupt("return",t);case 7:case"end":return e.stop()}},e,this)})),function(){return a.apply(this,arguments)})},{key:"getLocalAudioStats",value:(o=mt(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.audio.bytesSent,packetsSent:n.audio.packetsSent};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}},e,this)})),function(){return o.apply(this,arguments)})},{key:"getLocalVideoStats",value:(i=mt(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((t={})[this.userId_]={bytesSent:0,packetsSent:0,framesEncoded:0,framesSent:0,frameWidth:0,frameHeight:0},!this.uplinkConnection_){e.next=7;break}return e.next=5,this.stats_.getSenderStats(this.uplinkConnection_);case 5:n=e.sent,t[this.userId_]={bytesSent:n.video.bytesSent,packetsSent:n.video.packetsSent,framesEncoded:n.video.framesEncoded,framesSent:n.video.framesSent,frameWidth:n.video.frameWidth,frameHeight:n.video.frameHeight};case 7:return e.abrupt("return",t);case 8:case"end":return e.stop()}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getRemoteAudioStats",value:(r=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t={},n=!0,r=!1,i=void 0,e.prev=4,o=this.connections_[Symbol.iterator]();case 6:if(n=(a=o.next()).done){e.next=15;break}return(s=It(a.value,2))[0],c=s[1],e.next=10,this.stats_.getReceiverStats(c);case 10:(u=e.sent).hasAudio&&(t[u.userId]={bytesReceived:u.audio.bytesReceived,packetsReceived:u.audio.packetsReceived,packetsLost:u.audio.packetsLost});case 12:n=!0,e.next=6;break;case 15:e.next=21;break;case 17:e.prev=17,e.t0=e.catch(4),r=!0,i=e.t0;case 21:e.prev=21,e.prev=22,n||null==o.return||o.return();case 24:if(e.prev=24,!r){e.next=27;break}throw i;case 27:return e.finish(24);case 28:return e.finish(21);case 29:return e.abrupt("return",t);case 30:case"end":return e.stop()}},e,this,[[4,17,21,29],[22,,24,28]])})),function(){return r.apply(this,arguments)})},{key:"getRemoteVideoStats",value:(n=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t={},n=!0,r=!1,i=void 0,e.prev=4,o=this.connections_[Symbol.iterator]();case 6:if(n=(a=o.next()).done){e.next=15;break}return(s=It(a.value,2))[0],c=s[1],e.next=10,this.stats_.getReceiverStats(c);case 10:(u=e.sent).hasVideo&&(t[u.userId]={bytesReceived:u.video.bytesReceived,packetsReceived:u.video.packetsReceived,packetsLost:u.video.packetsLost,framesDecoded:u.video.framesDecoded,frameWidth:u.video.frameWidth,frameHeight:u.video.frameHeight});case 12:n=!0,e.next=6;break;case 15:e.next=21;break;case 17:e.prev=17,e.t0=e.catch(4),r=!0,i=e.t0;case 21:e.prev=21,e.prev=22,n||null==o.return||o.return();case 24:if(e.prev=24,!r){e.next=27;break}throw i;case 27:return e.finish(24);case 28:return e.finish(21);case 29:return e.abrupt("return",t);case 30:case"end":return e.stop()}},e,this,[[4,17,21,29],[22,,24,28]])})),function(){return n.apply(this,arguments)})},{key:"getSdpSemantics",value:function(){return this.sdpSemantics_}},{key:"getIceServers",value:function(){return this.turnServers_}},{key:"getConnections",value:function(){return this.connections_}},{key:"getMutedStates",value:function(){return this.mutedStates_}},{key:"startHeartbeat",value:function(){if(-1===this.heartbeat_){this.log_.info("startHeartbeat..."),this.heartbeat_=setInterval(this.doHeartbeat.bind(this),2e3)}}},{key:"stopHeartbeat",value:function(){-1!==this.heartbeat_&&(this.log_.info("stopHeartbeat"),clearInterval(this.heartbeat_),this.heartbeat_=-1)}},{key:"doHeartbeat",value:(t=mt(regeneratorRuntime.mark(function e(){var t,n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.stats_.getStatsReport(this.uplinkConnection_,this.connections_);case 2:if(t=e.sent,this.signalChannel_){e.next=5;break}return e.abrupt("return");case 5:n=this.signalChannel_.isConnected()?Zh(this.userId_):[],r={WebRTCQualityReq:t,eventList:n,sdkAppId:this.sdkAppId_,tinyid:this.tinyId_,roomid:this.roomId_,socketid:this.signalInfo_.socketId,clientip:this.signalInfo_.localIp,serverip:this.signalInfo_.relayIp,cpunumber:navigator.hardwareConcurrency||0,cpudevice:navigator.platform,devicename:navigator.platform,ostype:navigator.platform,mode:this.localStream_?this.localStream_.getScreen()?"screen":"video":""},i=0,this.localStream_&&this.localStream_.getMediaStream()&&(o=this.localStream_.getMediaStream().getAudioTracks(),i=o.length>0&&o[0].muted?3:1),r.WebRTCQualityReq.AudioReportState.uint32_microphone_status=i,this.signalChannel_.send(Zp,r);case 11:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"onRemoteStreamAdded",value:function(e){var t=e.content,n=t.srcopenid,r=t.srctinyid;if(null!==n){var i=this.connections_.get(r);if(i){if(i.getIsReconnecting())return;this.log_.warn("duplicated stream-added observed, rebuild the connection"),i.close(),this.connections_.delete(r)}var o={audio:t.audio,video:t.bigVideo,auxiliary:t.auxVideo};this.log_.info("remote peer [".concat(n,"] published stream. trackState: ").concat(JSON.stringify(o))),this.createDownlinkConnection({userId:n,tinyId:r,trackState:o})}else this.log_.warn("received null userId on stream added")}},{key:"createDownlinkConnection",value:function(e){var t=e.userId,n=e.tinyId,r=e.trackState,i=new Fm({userId:t,tinyId:n,client:this,isUplink:!1,signalChannel:this.signalChannel_,autoSubscribe:this.autoSubscribe_,trackState:r});if(this.connections_.set(n,i),this.installDownlinkEvents(i,t,n),this.autoSubscribe_)i.initialize(),i.exchangeSDP();else{if(r.audio||r.video){var o=new Rm({type:"main",userId:t,client:this.client_});o.setConnection(i),i.setRemoteStream(xs,o),o.setIsStreamAddedEventEmitted(!0),this.emitter_.emit(Ff,{stream:o})}if(r.auxiliary){var a=new Rm({type:"auxiliary",userId:t,client:this.client_});a.setConnection(i),i.setRemoteStream(Ps,a),a.setIsStreamAddedEventEmitted(!0),this.emitter_.emit(Ff,{stream:a})}}}},{key:"installDownlinkEvents",value:function(e,t,n){var r=this;e.on(Nf,function(e){r.subscriptionManager_.hasAutoRecoveryFlag(t,e.stream.getType())?r.subscriptionManager_.recover(e.stream):(e.stream.setIsStreamAddedEventEmitted(!0),r.emitter_.emit(Ff,{stream:e.stream}))}),e.on(Mf,function(e){r.subscriptionManager_.hasAutoRecoveryFlag(t,e.stream.getType())||(e.stream.setIsStreamAddedEventEmitted(!1),r.emitter_.emit(Bf,{stream:e.stream}))}),e.on(Lf,function(e){r.subscriptionManager_.hasAutoRecoveryFlag(t,e.stream.getType())||r.emitter_.emit(Gf,{stream:e.stream})}),e.on(Uf,function(e){r.subscriptionManager_.hasAutoRecoveryFlag(t,e.stream.getType())||r.emitter_.emit(Wf,{stream:e.stream})}),e.on(Vf,function(e){var t=e.getCode();t!==fh.ICE_TRANSPORT_ERROR&&(t===fh.DOWNLINK_RECONNECTION_FAILED&&r.closeDownLink(n),r.emitter_.emit(Zf,e))})}},{key:"onRemoteStreamRemoved",value:function(e){var t=e.content,n=t.srctinyid,r=t.srcopenid,i=this.connections_.get(n);i&&(void 0===r&&(r=i.getUserId()),this.log_.info("remote peer [".concat(r,"] unpublished stream")),this.closeDownLink(n),this.badCaseDetector_&&this.badCaseDetector_.deleteBlackVideoRecord(r))}},{key:"onPeerJoin",value:function(e){var t=e.data.content,n=t.srctinyid,r=t.userid,i=t.role;this.userMap_.has(r)||(this.userMap_.set(r,new Wm({userId:r,tinyId:n,role:i})),this.emitter_.emit(Jf,{userId:r}))}},{key:"onPeerLeave",value:function(e){var t=e.data.content,n=t.srctinyid,r=t.userid;this.log_.info("peer leave [".concat(r,"]")),this.cleanUser({userId:r,tinyId:n})}},{key:"cleanUser",value:function(e){var t=e.userId,n=e.tinyId;this.userMap_.delete(t),this.closeDownLink(n),this.emitter_.emit(zf,{userId:t})}},{key:"onPublishedUserList",value:function(e){var t=this;try{var n=e.content.userlist.map(function(e){return e.userid});this.connections_.forEach(function(e){var r=e.getUserId(),i=e.getTinyId();n.findIndex(function(e){return e===r})<0&&(t.log_.info("peer unpublished detected [".concat(r,"]")),t.closeDownLink(i))})}catch(wv){}}},{key:"onUpdateRemoteMuteStat",value:function(e){var t=this,n=e.content;n.userlist.forEach(function(e){var n=e.srctinyid,r=e.userid;if(0!==n&&n!==t.tinyId_)if(t.connections_.get(n)){var i=!!(1&e.flag),o=!!(8&e.flag),a=!!(64&e.flag),s=!!(16&e.flag),c=t.mutedStates_.get(n);if(void 0===c)return t.mutedStates_.set(n,{hasAudio:o,hasVideo:i,audioMuted:a,videoMuted:s}),i?s?t.emitter_.emit(qf,{userId:r}):t.emitter_.emit(Xf,{userId:r}):t.emitter_.emit(qf,{userId:r}),void(o?a?t.emitter_.emit(Qf,{userId:r}):t.emitter_.emit(Kf,{userId:r}):t.emitter_.emit(Qf,{userId:r}));var u=!a&&o;(!c.audioMuted&&c.hasAudio)!==u&&(u?t.emitter_.emit(Kf,{userId:r}):t.emitter_.emit(Qf,{userId:r}));var d=!s&&i;(!c.videoMuted&&c.hasVideo)!==d&&(d?t.emitter_.emit(Xf,{userId:r}):t.emitter_.emit(qf,{userId:r})),t.mutedStates_.set(n,{hasAudio:o,hasVideo:i,audioMuted:a,videoMuted:s})}else t.mutedStates_.delete(n)})}},{key:"onClientBanned",value:function(e){this.emitter_.emit($f,new vh({code:fh.CLIENT_BANNED,message:"client was banned because of "+e}))}},{key:"getEnv",value:function(){return this.env_}},{key:"getSubscriptionManager",value:function(){return this.subscriptionManager_}},{key:"startPublishCDNStream",value:function(e){var t=this;if(this.publishedCDN_)throw new vh({code:fh.INVALID_OPERATION,message:"publish CDN stream has been started"});if(void 0===e.appId)throw new vh({code:fh.INVALID_PARAMETER,message:"appId is required"});if(void 0===e.bizId)throw new vh({code:fh.INVALID_PARAMETER,message:"bizId is required"});if(void 0===e.url)throw new vh({code:fh.INVALID_PARAMETER,message:"url is required"});return new Promise(function(n,r){var i={roomid:String(t.roomId_),sdkAppID:String(t.sdkAppId_),socketid:t.signalInfo_.socketId,pushRequestTime:Date.now(),pushAppid:e.appId,pushBizId:e.bizId,pushCdnUrl:e.url,pushStreamType:"main"};t.log_.info("startPublishCDNStream: "+JSON.stringify(i)),t.publishCDNData_=i,t.publishedCDN_=!0,t.signalChannel_.send(uh,i);var o=setTimeout(function(){t.log_.error("start publish cdn stream timeout observed"),t.publishedCDN_=!1,t.signalChannel_.off(Kp.START_PUSH_CDN_RES,a),r(new vh({code:fh.START_PUBLISH_CDN_FAILED,message:"start publish cdn stream timeout"}))},5e3),a=function(e){clearTimeout(o),e.data.content.errCode?(t.log_.error("start publish cdn stream failed, errCode: ".concat(e.data.content.errCode,", errMsg: ").concat(e.data.content.errMsg)),t.publishedCDN_=!1,r(new vh({code:fh.START_PUBLISH_CDN_FAILED,message:"start publish cdn stream failed, errMsg: ".concat(e.data.content.errMsg)}))):n()};t.signalChannel_.once(Kp.START_PUSH_CDN_RES,a)})}},{key:"stopPublishCDNStream",value:function(){var e=this;if(!this.publishedCDN_)throw new vh({code:fh.INVALID_OPERATION,message:"publish CDN stream hasn't been started"});return new Promise(function(t,n){var r=e.publishCDNData_,i=r.pushAppid,o=r.pushBizId,a=r.pushCdnUrl,s=r.pushStreamType,c={roomid:String(e.roomId_),sdkAppID:String(e.sdkAppId_),socketid:e.signalInfo_.socketId,pushRequestTime:Date.now(),pushAppid:i,pushBizId:o,pushCdnUrl:a,pushStreamType:s};e.log_.info("stopPublishCDNStream: "+JSON.stringify(c)),e.signalChannel_.send(dh,c);var u=setTimeout(function(){e.log_.error("stop publish cdn stream timeout observed"),e.signalChannel_.off(Kp.STOP_PUSH_CDN_RES,d),n(new vh({code:fh.STOP_PUBLISH_CDN_FAILED,message:"stop publish cdn timeout"}))},5e3),d=function(r){clearTimeout(u),r.data.content.errCode?(e.log_.error("stop publish cdn stream failed, errCode: ".concat(r.data.content.errCode," errMsg: ").concat(r.data.content.errMsg)),n(new vh({code:fh.STOP_PUBLISH_CDN_FAILED,message:"stop publish cdn stream failed, errMsg: ".concat(r.data.content.errMsg)}))):(e.publishedCDN_=!1,t())};e.signalChannel_.once(Kp.STOP_PUSH_CDN_RES,d)})}},{key:"startMixTranscode",value:function(e){var t=this;if(this.mixedMCU_)throw new vh({code:fh.INVALID_OPERATION,message:"mix transcoding has been started"});this.validateMixTranscodeParamsType(e);var n=this.transformMixTranscodeParams(e),r=n.outputSessionId,i=n.inputParam,o=n.outputParam;return this.validateMixTranscodeParamsValue(i,o),new Promise(function(e,n){var a={roomid:String(t.roomId_),sdkAppID:String(t.sdkAppId_),socketid:t.signalInfo_.socketId,mcuRequestTime:Date.now(),outputSessionId:r,inputParam:i,outputParam:o};t.mixTranscodeData_=a,t.log_.info("startMixTranscode: "+JSON.stringify(a)),t.signalChannel_.send(lh,a),t.mixedMCU_=!0;var s=setTimeout(function(){t.log_.error("startMixTranscode timeout observed"),t.mixedMCU_=!1,t.signalChannel_.off(Kp.START_MIX_TRANSCODE_RES,c),n(new vh({code:fh.START_MIX_TRANSCODE_FAILED,message:"startMixTranscode timeout"}))},5e3),c=function(r){clearTimeout(s),r.data.content.errCode?(t.log_.error("startMixTranscode failed, errCode: ".concat(r.data.content.errCode," errMsg: ").concat(r.data.content.errMsg)),t.mixedMCU_=!1,n(new vh({code:fh.START_MIX_TRANSCODE_FAILED,message:"startMixTranscode failed, errMsg: ".concat(r.data.content.errMsg)}))):e()};t.signalChannel_.once(Kp.START_MIX_TRANSCODE_RES,c)})}},{key:"validateMixTranscodeParamsType",value:function(e){if("object"!==kc(e))throw new vh({code:fh.INVALID_PARAMETER,message:"param should be object when start mix transcoding"});var t=Rc(e,{streamId:ac,videoWidth:sc,videoHeight:sc,videoBitrate:sc,videoFramerate:sc,videoGOP:sc,audioSampleRate:sc,audioBitrate:sc,audioChannels:sc,backgroundColor:sc,backgroundImage:ac,mixUsers:uc});if(!t.ret)throw new vh({code:fh.INVALID_PARAMETER,message:t.message});var n={userId:ac,pureAudio:cc,width:sc,height:sc,locationX:sc,locationY:sc,zOrder:sc};e.mixUsers.forEach(function(e){var t=Rc(e,n);if(!t.ret)throw new vh({code:fh.INVALID_PARAMETER,message:"mixUser.".concat(t.message)})})}},{key:"transformMixTranscodeParams",value:function(e){var t=e.streamId,n=e.mixUsers;return{outputSessionId:t.length>0?t:Bm("".concat(this.roomId_,"_").concat(this.userId_,"_main")),inputParam:n.map(function(e){return{userid:e.userId,width:e.width||0,height:e.height||0,locationX:e.locationX||0,locationY:e.locationY||0,zOrder:e.zOrder,streamType:0,inputType:e.pureAudio?oc:ic}}),outputParam:{streamId:e.streamId||"",streamType:t.length>0?1:0,videoWidth:e.videoWidth||0,videoHeight:e.videoHeight||0,videoBitrate:e.videoBitrate||0,videoFramerate:e.videoFramerate||15,videoGOP:e.videoGOP||2,audioSampleRate:e.audioSampleRate||48e3,audioBitrate:e.audioBitrate||64,audioChannels:e.audioChannels||1,backgroundColor:e.backgroundColor||0,backgroundImage:e.backgroundImage||"",extraInfo:"",VCodec:2,ACodec:0}}}},{key:"validateMixTranscodeParamsValue",value:function(e,t){var n=[],r=0,i=0;if(e.forEach(function(e){if(void 0===e.userid)throw new vh({code:fh.INVALID_PARAMETER,message:"mixUser.userId is required"});if(e.inputType!==oc){if(e.width<0||e.height<0)throw new vh({code:fh.INVALID_PARAMETER,message:"mixUser.width and mixUser.height can't be less than 0"});if(e.locationX<0||e.locationY<0)throw new vh({code:fh.INVALID_PARAMETER,message:"mixUser.locationX and mixUser.locationY can't be less than 0"});if(!e.zOrder||e.zOrder<1||e>15)throw new vh({code:fh.INVALID_PARAMETER,message:"mixUser.zOrder should between 1 and 15"});e.width+e.locationX>r&&(r=e.width+e.locationX),e.height+e.locationY>i&&(i=e.height+e.locationY),n.push(e.zOrder)}}),t.videoWidth<0||t.videoWidth<0)throw new vh({code:fh.INVALID_PARAMETER,message:"videoWidth and videoHeight of output stream can't be less than 0"});if(t.videoWidth<r||t.videoHeight<i)throw new vh({code:fh.INVALID_PARAMETER,message:"videoWidth and videoHeight of output stream should be contain all mix stream"});if(n.length!==xt(new Set(n)).length)throw new vh({code:fh.INVALID_PARAMETER,message:"every mixUser.zOrder should be no repeat"})}},{key:"stopMixTranscode",value:function(){var e=this;if(!this.mixedMCU_)throw new vh({code:fh.INVALID_OPERATION,message:"mixTranscode hasn't been started"});return new Promise(function(t,n){var r={roomid:String(e.roomId_),sdkAppID:String(e.sdkAppId_),socketid:e.signalInfo_.socketId,userid:e.userId_,mcuRequestTime:Date.now(),outPutSessionId:e.mixTranscodeData_.outputSessionId,streamType:e.mixTranscodeData_.outputParam.streamType};e.log_.info("stopMixTranscode: "+JSON.stringify(r)),e.signalChannel_.send(ph,r);var i=setTimeout(function(){e.log_.error("stopMixTranscode timeout observed"),e.signalChannel_.off(Kp.STOP_MIX_TRANSCODE_RES,o),n(new vh({code:fh.STOP_MIX_TRANSCODE_FAILED,message:"stopMixTranscode timeout"}))},5e3),o=function(r){clearTimeout(i),r.data.content.errCode?(e.log_.error("stopMixTranscode failed, errCode: ".concat(r.data.content.errCode," errMsg: ").concat(r.data.content.errMsg)),n(new vh({code:fh.STOP_MIX_TRANSCODE_FAILED,message:"stopMixTranscode failed, errMsg: ".concat(r.data.content.errMsg)}))):(e.mixedMCU_=!1,t())};e.signalChannel_.once(Kp.STOP_MIX_TRANSCODE_RES,o)})}}]),e}(),Km=Qm({retryFunction:function(){var e=mt(regeneratorRuntime.mark(function e(t){var n,r,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!Cf()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Xm(t);case 4:if(n=e.sent,Hn.info("getUserMedia with constraints: "+JSON.stringify(n)),!n.audio){e.next=13;break}return e.next=9,Rv.getMicrophones();case 9:if(r=e.sent,Hn.info("microphones: ".concat(JSON.stringify(r))),0!==r.length){e.next=13;break}throw new vh({code:fh.DEVICE_NOT_FOUND,message:"no microphone detected, but you are trying to get audio stream, please check your microphone and the configeration on TRTC.createStream."});case 13:if(!n.video){e.next=20;break}return e.next=16,Rv.getCameras();case 16:if(i=e.sent,Hn.info("cameras: ".concat(JSON.stringify(i))),0!==i.length){e.next=20;break}throw new vh({code:fh.DEVICE_NOT_FOUND,message:"no camera detected, but you are trying to get video stream, please check your camera and the configeration on TRTC.createStream."});case 20:return e.next=22,navigator.mediaDevices.getUserMedia(n);case 22:return e.abrupt("return",e.sent);case 23:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),settings:{retries:5,timeout:2e3},onError:function(e,t,n){"NotReadableError"===e.name?t():n(e)},onRetrying:function(e){Hn.warn("getUserMedia NotReadableError observed, retrying [".concat(e,"/5]"))}});function Xm(e){return $m.apply(this,arguments)}function $m(){return($m=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0},t.audio){e.next=5;break}n=!1,e.next=15;break;case 5:if(uf(t.microphoneId)){e.next=9;break}n=St({deviceId:{exact:t.microphoneId},sampleRate:t.sampleRate,channelCount:t.channelCount},n),e.next=15;break;case 9:return n=St({sampleRate:t.sampleRate,channelCount:t.channelCount},n),e.next=12,Rv.getMicrophones();case 12:r=e.sent,(i=r.filter(function(e){var t=e.deviceId;return t.length>0&&"default"!==t})).length>0&&(n.deviceId={exact:i[0].deviceId});case 15:return o={},o=void 0!==t.facingMode&&t.video?{facingMode:t.facingMode,width:t.width,height:t.height,frameRate:t.frameRate}:!uf(t.cameraId)&&t.video?{deviceId:{exact:t.cameraId},width:t.width,height:t.height,frameRate:t.frameRate}:!!t.video&&(void 0===t.width||{width:t.width,height:t.height,frameRate:t.frameRate}),e.abrupt("return",{audio:n,video:o});case 18:case"end":return e.stop()}},e)}))).apply(this,arguments)}var Ym=function(){var e=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c,u,d;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!Cf()){e.next=2;break}return e.abrupt("return");case 2:if(n=null,!(cs&&us<74||ls)){e.next=27;break}return r=ev(t),Hn.info("getDisplayMedia with constraints: "+JSON.stringify(r)),e.next=8,navigator.mediaDevices.getDisplayMedia(r);case 8:if(i=e.sent,!t.screenAudio){e.next=14;break}return Hn.warn("Your browser not support capture system audio"),e.abrupt("return",i);case 14:if(!t.audio){e.next=24;break}return o=Zm(t),Hn.info("getUserMedia with constraints: "+JSON.stringify(o)),e.next=19,navigator.mediaDevices.getUserMedia(o);case 19:return n=e.sent,i.addTrack(n.getAudioTracks()[0]),e.abrupt("return",i);case 24:return e.abrupt("return",i);case 25:e.next=53;break;case 27:if(!t.screenAudio){e.next=37;break}return t.audioConstraints={echoCancellation:!0,noiseSuppression:!0,sampleRate:44100},a=ev(t),Hn.info("getDisplayMedia with constraints: "+JSON.stringify(a)),e.next=33,navigator.mediaDevices.getDisplayMedia(a);case 33:return s=e.sent,e.abrupt("return",s);case 37:return c=ev(t),Hn.info("getDisplayMedia with constraints: "+JSON.stringify(c)),e.next=41,navigator.mediaDevices.getDisplayMedia(c);case 41:if(u=e.sent,!t.audio){e.next=52;break}return d=Zm(t),Hn.info("getUserMedia with constraints: "+JSON.stringify(d)),e.next=47,navigator.mediaDevices.getUserMedia(d);case 47:return n=e.sent,u.addTrack(n.getAudioTracks()[0]),e.abrupt("return",u);case 52:return e.abrupt("return",u);case 53:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}();function Zm(e){return{audio:void 0!==e.microphoneId?{deviceId:{exact:e.microphoneId},sampleRate:e.sampleRate,channelCount:e.channelCount}:{sampleRate:e.sampleRate,channelCount:e.channelCount},video:!1}}function ev(e){var t={},n={width:e.width,height:e.height,frameRate:e.frameRate};return void 0!==e.screenSource&&(n.displaySurface=e.screenSource),t.video=n,void 0!==e.audioConstraints&&(t.audio=e.audioConstraints),t}var tv=new Map;tv.set("120p",{width:160,height:120,frameRate:15,bitrate:200}),tv.set("180p",{width:320,height:180,frameRate:15,bitrate:350}),tv.set("240p",{width:320,height:240,frameRate:15,bitrate:400}),tv.set("360p",{width:640,height:360,frameRate:15,bitrate:800}),tv.set("480p",{width:640,height:480,frameRate:15,bitrate:900}),tv.set("720p",{width:1280,height:720,frameRate:15,bitrate:1500}),tv.set("1080p",{width:1920,height:1080,frameRate:15,bitrate:2e3}),tv.set("1440p",{width:2560,height:1440,frameRate:30,bitrate:4860}),tv.set("4K",{width:3840,height:2160,frameRate:30,bitrate:9e3});var nv=new Map;nv.set("480p",{width:640,height:480,frameRate:5,bitrate:900}),nv.set("480p_2",{width:640,height:480,frameRate:30,bitrate:1e3}),nv.set("720p",{width:1280,height:720,frameRate:5,bitrate:1200}),nv.set("720p_2",{width:1280,height:720,frameRate:30,bitrate:3e3}),nv.set("1080p",{width:1920,height:1080,frameRate:5,bitrate:1600}),nv.set("1080p_2",{width:1920,height:1080,frameRate:30,bitrate:4e3});var rv=new Map;rv.set("standard",{sampleRate:48e3,channelCount:1,bitrate:40}),rv.set("high",{sampleRate:48e3,channelCount:1,bitrate:128});var iv=function(e){function t(e){var n;vt(this,t);var r=St({},e,{isRemote:!1,type:"local"});return e.screen&&(r.mirror=!1),(n=Et(this,kt(t).call(this,r))).video_=e.video,n.audio_=e.audio,n.cameraId_=e.cameraId,n.cameraGroupId_="",n.facingMode_=e.facingMode,n.microphoneId_=e.microphoneId,n.microphoneGroupId_="",n.videoSource_=e.videoSource,n.audioSource_=e.audioSource,n.screen_=e.screen,n.screenSource_=e.screenSource,n.screenAudio_=e.screenAudio,n.audioProfile_={sampleRate:48e3,channelCount:1,bitrate:40},n.videoProfile_={width:640,height:480,frameRate:15,bitrate:900},n.screenProfile_={width:1920,height:1080,frameRate:5,bitrate:1600},n.videoBitrate_=n.screen_?1600:900,n.videoSetting_=null,n.mutedFlag_=0,n.installEvents(),n}var n,r,i,o,a,s,c;return bt(t,km),_t(t,[{key:"installEvents",value:function(){ym.on(bm,this.onStreamPublished,this)}},{key:"uninstallEvents",value:function(){ym.off(bm,this.onStreamPublished,this)}},{key:"initialize",value:function(){var e=this;return new Promise(function(t,n){if(If())n(new vh({code:fh.INVALID_OPERATION,message:"not supported in http protocol, please use https protocol"}));else{if(void 0===e.audio_){var r=new MediaStream;return void 0!==e.audioSource_&&(r.addTrack(e.audioSource_),e.updateAudioPlayingState(!0)),void 0!==e.videoSource_&&(r.addTrack(e.videoSource_),e.updateVideoPlayingState(!0)),e.setMediaStream(r),af({eventType:$s,kind:"custom"}),t()}e.screen_?(e.log_.info("initialize stream audio: "+e.audio_+" screenAudio: "+e.screenAudio_+" screen: "+e.screen_),Ym({audio:e.audio_,screenAudio:e.screenAudio_,microphoneId:e.microphoneId_,screenSource:e.screenSource_,width:e.screenProfile_.width,height:e.screenProfile_.height,frameRate:e.screenProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount}).then(function(n){return e.setMediaStream(n),e.updateAudioPlayingState(e.audio_||e.screenAudio_),e.updateVideoPlayingState(!0),e.listenForScreenSharingStopped(e.getVideoTrack()),e.setVideoContentHint("detail"),e.updateDeviceIdInUse(),af({eventType:$s,kind:"getDisplayMedia"}),t()}).catch(function(t){sf({eventType:$s,kind:"getDisplayMedia",error:t}),e.log_.error("getDisplayMedia error observed "+t),n(t)})):(e.log_.info("initialize stream audio: "+e.audio_+" video: "+e.video_),Km({audio:e.audio_,video:e.video_,facingMode:e.facingMode_,cameraId:e.cameraId_,microphoneId:e.microphoneId_,width:e.videoProfile_.width,height:e.videoProfile_.height,frameRate:e.videoProfile_.frameRate,sampleRate:e.audioProfile_.sampleRate,channelCount:e.audioProfile_.channelCount}).then(function(n){return"getSettings"in MediaStreamTrack.prototype&&(e.videoSetting_=n.getVideoTracks().length>0&&n.getVideoTracks()[0].getSettings()),e.setMediaStream(n),e.updateAudioPlayingState(e.audio_),e.updateVideoPlayingState(e.video_),e.updateDeviceIdInUse(),e.log_.debug("gotStream hasAudio: "+e.hasAudio()+" hasVideo: "+e.hasVideo()),af({eventType:$s,kind:"getUserMedia"}),t()}).catch(function(t){sf({eventType:$s,kind:"getUserMedia",error:t}),e.log_.error("getUserMedia error observed "+t+" https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-10-error-code-tips.html"),n(t)}))}})}},{key:"listenForScreenSharingStopped",value:function(e){var t=this;e.addEventListener("ended",function(e){t.log_.info("screen sharing was stopped because the video track is ended"),t.emitter_.emit(tm)})}},{key:"muteAudio",value:function(){var e=Ct(kt(t.prototype),"muteAudio",this).call(this);return e&&(this.log_.info("localStream mute audio"),this.sendMutedFlag(dc,!0)),e}},{key:"muteVideo",value:function(){var e=Ct(kt(t.prototype),"muteVideo",this).call(this);return e&&(this.log_.info("localStream mute video"),this.sendMutedFlag(lc,!0)),e}},{key:"unmuteAudio",value:function(){var e=Ct(kt(t.prototype),"unmuteAudio",this).call(this);return e&&(this.log_.info("localStream unmute audio"),this.sendMutedFlag(dc,!1)),e}},{key:"unmuteVideo",value:function(){var e=Ct(kt(t.prototype),"unmuteVideo",this).call(this);return e&&(this.log_.info("localStream unmute video"),this.sendMutedFlag(lc,!1)),e}},{key:"sendMutedFlag",value:function(e,t){this.setMutedFlag(e,t);var n=this.getConnection();if(n){n.sendMutedFlag(this.mutedFlag_);var r=n.getUserId(),i=n.getTinyId(),o="".concat(t?"mute":"unmute"," local ").concat(e," track");Yh(r,{eventId:e===dc?t?bh:Rh:t?kh:wh,eventDesc:o,timestamp:Eo(),userId:r,tinyId:i})}}},{key:"setMutedFlag",value:function(e,t){e===dc?t?this.mutedFlag_|=4:this.mutedFlag_&=-5:t?this.mutedFlag_|=1:this.mutedFlag_&=-2,this.log_.info("set ".concat(e," muted state: [").concat(t?"mute":"unmute","]"))}},{key:"setAudioProfile",value:function(e){var t;"object"===ht(e)?t=e:void 0===(t=rv.get(e))&&(t=rv.get("standard")),this.log_.info("setAudioProfile: "+JSON.stringify(t)),this.audioProfile_=t}},{key:"setVideoProfile",value:(c=mt(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.connection_||Ef()){e.next=2;break}throw new vh({code:fh.NOT_SUPPORTED,message:"not supported on current browser"});case 2:if(vc(t)?n=St({},this.videoProfile_,t):bc(t)&&(n=tv.get(t),Sc(n)&&(n=tv.get("480p"))),this.log_.info("setVideoProfile "+JSON.stringify(n)),!(r=this.getVideoTrack())){e.next=8;break}return e.next=8,r.applyConstraints(n);case 8:if(this.videoBitrate_===n.bitrate){e.next=13;break}if(!this.connection_){e.next=12;break}return e.next=12,this.connection_.setBandwidth(n.bitrate,"video");case 12:this.videoBitrate_=n.bitrate;case 13:this.videoProfile_=n;case 14:case"end":return e.stop()}},e,this)})),function(e){return c.apply(this,arguments)})},{key:"getVideoBitrate",value:function(){return this.videoBitrate_}},{key:"getAudioBitrate",value:function(){return this.audioProfile_.bitrate}},{key:"setScreenProfile",value:function(e){var t=e;"object"!==ht(e)&&void 0===(t=nv.get(e))&&(t=nv.get("1080p")),this.log_.info("setScreenProfile "+JSON.stringify(e)),this.screenProfile_=t,this.videoBitrate_=t.bitrate}},{key:"getVideoProfile",value:function(){return this.screen_?this.screenProfile_:this.videoProfile_}},{key:"getAudioProfile",value:function(){return this.audioProfile_}},{key:"setVideoContentHint",value:function(e){var t=this.getVideoTrack();t&&"contentHint"in t&&(this.log_.info("set video track contentHint to: "+e),t.contentHint=e,t.contentHint!==e&&this.log_.warn("Invalid video track contentHint: "+e))}},{key:"switchDevice",value:(s=mt(regeneratorRuntime.mark(function e(t,n){var r,i,o,a,s,c,u,d,l,p,h,f,m,v;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.screen_){e.next=2;break}throw new vh({code:fh.INVALID_OPERATION,message:"switch device is not supported in screen sharing"});case 2:if(!("audio"===t&&this.microphoneId_===n||"video"===t&&this.cameraId_===n||this.audioSource_||this.videoSource_)){e.next=4;break}return e.abrupt("return");case 4:if("audio"===t&&(this.microphoneId_=n,this.audio_||(this.audio_=!0)),"video"===t&&("user"===n||"environment"===n?this.facingMode_=n:this.cameraId_=n,this.video_||(this.video_=!0)),this.getMediaStream()){e.next=8;break}return e.abrupt("return");case 8:return this.log_.info("switchDevice "+t+" to: "+n),"video"===t&&(ha||la||ga)&&(r=this.getVideoTrack())&&r.stop(),"audio"===t&&ga&&(i=this.getAudioTrack(),o=this.getMicrophoneTrackMixed(),i&&i.stop(),o&&o.stop()),e.next=13,Km({audio:this.audio_&&"audio"===t,video:this.video_&&"video"===t,facingMode:this.facingMode_,cameraId:this.cameraId_,microphoneId:this.microphoneId_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 13:return a=e.sent,s=null,"audio"===t?(c=a.getAudioTracks()[0])&&this.isAudioTrackMixed()?(u=this.getAudioTrack(),d=Rv.AudioMixerPlugin.getAudioTrackMap(),l=Rv.AudioMixerPlugin.mix({targetTrack:c,sourceList:d.get(u.id).sourceList}),s=l):s=c:s=a.getVideoTracks()[0],e.next=18,this.replaceTrack_(s);case 18:return this.updateDeviceIdInUse(),(p=this.getConnection())&&(h=p.getUserId(),f=p.getTinyId(),m=Ih,v="switch camera","audio"===t&&(m=xh,v="switch microphone"),Yh(h,{eventId:m,eventDesc:v,timestamp:Eo(),userId:h,tinyId:f})),this.log_.info("switch ".concat("audio"===t?"microphone":"camera"," success ")),e.abrupt("return");case 23:case"end":return e.stop()}},e,this)})),function(e,t){return s.apply(this,arguments)})},{key:"addTrack",value:(a=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.getMediaStream()){e.next=3;break}throw new vh({code:fh.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 3:if(!("audio"===t.kind&&n.getAudioTracks().length>0||"video"===t.kind&&n.getVideoTracks().length>0)){e.next=5;break}throw new vh({code:fh.INVALID_OPERATION,message:"A Stream has at most one audio track and one video track"});case 5:if("video"===t.kind&&"getSettings"in MediaStreamTrack.prototype&&(r=t.getSettings(),!this.videoSetting_||r.width===this.videoSetting_.width&&r.height===this.videoSetting_.height||this.log_.warn("video resolution of the track (".concat(r.width," x ").concat(r.height,") shall be kept the same as the previous: ").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,". It may cause abnormal Cloud Recording."))),n.addTrack(t),"audio"===t.kind?(this.audio_=!0,this.updateAudioPlayingState(!0)):(this.video_=!0,this.updateVideoPlayingState(!0)),!(i=this.getConnection())){e.next=18;break}return e.next=12,i.addTrack(t);case 12:o=i.getUserId(),a=i.getTinyId(),s=gh,c="add video track to current published stream","audio"===t.kind&&(s=_h,c="add audio track to current published stream"),Yh(o,{eventId:s,eventDesc:c,timestamp:Eo(),userId:o,tinyId:a});case 18:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"removeTrack",value:(o=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("audio"!==t.kind){e.next=2;break}throw new vh({code:fh.INVALID_PARAMETER,message:"remove audio track is not supported"});case 2:if(n=this.getMediaStream()){e.next=5;break}throw new vh({code:fh.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 5:if(-1!==n.getTracks().indexOf(t)){e.next=7;break}throw new vh({code:fh.INVALID_PARAMETER,message:"the track to be removed is not being publishing"});case 7:if(1!==n.getTracks().length){e.next=9;break}throw new vh({code:fh.INVALID_OPERATION,message:"remove the only video track is not supported, please use replaceTrack or muteVideo"});case 9:if(n.removeTrack(t),"audio"===t.kind?(this.audio_=!1,this.updateAudioPlayingState(!1)):(this.video_=!1,this.updateVideoPlayingState(!1)),!(r=this.getConnection())){e.next=21;break}return e.next=15,r.removeTrack(t);case 15:i=r.getUserId(),o=r.getTinyId(),a=yh,s="remove video track from current published stream","audio"===t.kind&&(a=Sh,s="remove audio track from current published stream"),Yh(i,{eventId:a,eventDesc:s,timestamp:Eo(),userId:i,tinyId:o});case 21:case"end":return e.stop()}},e,this)})),function(e){return o.apply(this,arguments)})},{key:"replaceTrack",value:(i=mt(regeneratorRuntime.mark(function e(n){var r,i,o,a,s,c,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.getMediaStream()){e.next=3;break}throw new vh({code:fh.INVALID_OPERATION,message:"the local stream is not initialized yet"});case 3:if(!("audio"===n.kind&&r.getAudioTracks().length<=0||"video"===n.kind&&r.getVideoTracks().length<=0)){e.next=5;break}throw new vh({code:fh.INVALID_PARAMETER,message:"try to replace ".concat(n.kind," track but there's no previous ").concat(n.kind," being published")});case 5:if("video"===n.kind&&"getSettings"in MediaStreamTrack.prototype&&(i=n.getSettings(),!this.videoSetting_||i.width===this.videoSetting_.width&&i.height===this.videoSetting_.height||this.log_.warn("video resolution of the track (".concat(i.width," x ").concat(i.height,") shall be kept the same as the previous: ").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,". It may cause abnormal Cloud Recording."))),"audio"===n.kind?(r.removeTrack(r.getAudioTracks()[0]),r.addTrack(n),Ct(kt(t.prototype),"restartAudio",this).call(this)):(r.removeTrack(r.getVideoTracks()[0]),r.addTrack(n),Ct(kt(t.prototype),"restartVideo",this).call(this)),!(o=this.getConnection())){e.next=17;break}return e.next=11,o.replaceTrack(n);case 11:a=o.getUserId(),s=o.getTinyId(),c=Ph,u="replace video track from current published stream","audio"===n.kind&&(c=Ah,u="replace audio track from current published stream"),Yh(a,{eventId:c,eventDesc:u,timestamp:Eo(),userId:a,tinyId:s});case 17:case"end":return e.stop()}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"updateStream",value:(r=mt(regeneratorRuntime.mark(function e(t){var n,r,i,o,a,s,c,u,d,l,p,h,f,m,v,g;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaStream_){e.next=2;break}return e.abrupt("return");case 2:return this.log_.info("updateStream() try to recover local stream"),e.prev=3,e.next=6,Rv.getCameras();case 6:return n=e.sent,e.next=9,Rv.getMicrophones();case 9:if(r=e.sent,i=this.audio_&&t.audio,(o=this.video_&&t.video)&&0===n.length&&(o=!1,this.log_.info("updateStream() video flag is true, but no camera detected, set video to false")),i&&0===r.length&&(i=!1,this.log_.info("updateStream() audio flag is true, but no microphone detected, set audio to false")),!1!==i||!1!==o){e.next=17;break}return this.log_.info("updateStream() both audio and video are false, recover stream aborted"),e.abrupt("return");case 17:return a=this.mediaStream_.getAudioTracks().map(function(e){return e.enabled}).includes(!1),s=this.mediaStream_.getVideoTracks().map(function(e){return e.enabled}).includes(!1),c=t&&n.findIndex(function(e){return e.deviceId===t.cameraId})>=0,u=t&&r.findIndex(function(e){return e.deviceId===t.microphoneId})>=0,e.next=23,Km({audio:i,video:o,cameraId:c?t.cameraId:void 0,microphoneId:u?t.microphoneId:void 0,facingMode:this.facingMode_,width:this.videoProfile_.width,height:this.videoProfile_.height,frameRate:this.videoProfile_.frameRate,sampleRate:this.audioProfile_.sampleRate,channelCount:this.audioProfile_.channelCount});case 23:d=e.sent,l=d.getTracks(),p=0;case 26:if(!(p<l.length)){e.next=44;break}if("audio"!==(h=l[p]).kind||!this.isAudioTrackMixed()){e.next=39;break}if(f=this.getAudioTrack(),m=Rv.AudioMixerPlugin.getAudioTrackMap(),(v=m.get(f.id)).hasMicrophone){e.next=35;break}return h.stop(),e.abrupt("continue",41);case 35:return g=Rv.AudioMixerPlugin.mix({targetTrack:h,sourceList:v.sourceList}),e.next=38,this.replaceTrack_(g);case 38:return e.abrupt("continue",41);case 39:return e.next=41,this.replaceTrack_(h);case 41:p++,e.next=26;break;case 44:a&&(this.log_.info("updateStream() prev audio tracks is muted, keep current audio tracks muted"),this.doEnableTrack("audio",!1)),s&&(this.log_.info("updateStream() prev video tracks is muted, keep current video tracks muted"),this.doEnableTrack("video",!1)),this.updateDeviceIdInUse(),af({eventType:ec}),this.log_.info("updateStream() recover local stream successfully"),e.next=56;break;case 51:e.prev=51,e.t0=e.catch(3),sf({eventType:ec,error:e.t0}),this.log_.error("updateStream() failed to recover local stream, "+e.t0),this.emitter_.emit(nm,new vh({code:fh.DEVICE_AUTO_RECOVER_FAILED,message:e.t0.message}));case 56:case"end":return e.stop()}},e,this,[[3,51]])})),function(e){return r.apply(this,arguments)})},{key:"replaceTrack_",value:(n=mt(regeneratorRuntime.mark(function e(n){var r,i,o,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.mediaStream_.getAudioTracks(),i=this.mediaStream_.getVideoTracks(),!("audio"===n.kind&&r.length<=0||"video"===n.kind&&i.length<=0)){e.next=5;break}return this.log_.info("there is no previous ".concat(n.kind," track, replacement ignored")),e.abrupt("return");case 5:if("audio"===n.kind?(this.mediaStream_.removeTrack(r[0]),this.mediaStream_.addTrack(n),Ct(kt(t.prototype),"restartAudio",this).call(this)):("getSettings"in MediaStreamTrack.prototype&&(o=n.getSettings(),!this.videoSetting_||o.width===this.videoSetting_.width&&o.height===this.videoSetting_.height||this.log_.warn("the resolution of video track to be replaced (".concat(o.width," x ").concat(o.height,") is different from the previous video settings (").concat(this.videoSetting_.width," x ").concat(this.videoSetting_.height,"). It may cause a cloud recording exception"))),this.mediaStream_.removeTrack(i[0]),this.mediaStream_.addTrack(n),Ct(kt(t.prototype),"restartVideo",this).call(this)),!(a=this.getConnection())){e.next=10;break}return e.next=10,a.replaceTrack(n);case 10:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"updateDeviceIdInUse",value:function(){var e=this;if(!this.mediaStream_)return this.cameraId_="",this.cameraGroupId_="",this.microphoneId_="",void(this.microphoneGroupId_="");"getSettings"in MediaStreamTrack.prototype&&this.mediaStream_.getTracks().forEach(function(t){if("audio"===t.kind&&e.isAudioTrackMixed()){var n=e.getMicrophoneTrackMixed();if(n){var r=n.getSettings(),i=r.deviceId,o=r.groupId;i&&(e.microphoneId_=i,e.microphoneGroupId_=o)}}else{var a=t.getSettings(),s=a.deviceId,c=a.groupId;s&&("audio"===t.kind?(e.microphoneId_=s,e.microphoneGroupId_=c):"video"!==t.kind||e.screen_||(e.cameraId_=s,e.cameraGroupId_=c))}});var t=this.mediaStream_.getAudioTracks(),n=this.mediaStream_.getVideoTracks();t&&0===t.length&&(this.microphoneId_="",this.microphoneGroupId_=""),n&&0===n.length&&(this.cameraId_="",this.cameraGroupId_="")}},{key:"isAudioTrackMixed",value:function(){if(Rv.AudioMixerPlugin){var e=Rv.AudioMixerPlugin.getAudioTrackMap(),t=this.getAudioTrack();if(e&&t&&e.has(t.id))return!0}return!1}},{key:"getMicrophoneTrackMixed",value:function(){if(Rv.AudioMixerPlugin){var e=Rv.AudioMixerPlugin.getAudioTrackMap(),t=this.getAudioTrack();if(e&&t&&e.has(t.id)){var n=e.get(t.id);return n.hasMicrophone?n.microphoneTrack:null}}return null}},{key:"getScreen",value:function(){return this.screen_}},{key:"getVideo",value:function(){return this.video_}},{key:"getAudio",value:function(){return this.audio_}},{key:"getCameraId",value:function(){return this.cameraId_}},{key:"getMicrophoneId",value:function(){return this.microphoneId_}},{key:"getMicrophoneGroupId",value:function(){return this.microphoneGroupId_}},{key:"onStreamPublished",value:function(e){if(e.localStream===this){var t=this.getAudioTrack(),n=this.getVideoTrack();if(t){var r=!t.enabled;this.setMutedFlag(dc,r)}if(n){var i=!n.enabled;this.setMutedFlag(lc,i)}this.connection_&&this.connection_.sendMutedFlag(this.mutedFlag_)}}},{key:"setAudioVolume",value:function(e){Ct(kt(t.prototype),"setAudioVolume",this).call(this,e)}},{key:"close",value:function(){Ct(kt(t.prototype),"close",this).call(this),this.uninstallEvents()}}]),t}(),ov=vs.IS_ELECTRON,av=vs.IS_XWEB;function sv(e){if(!ov&&!av)return e=window.localStorage?localStorage.getItem(e)||sessionStorage.getItem(e):(e=document.cookie.match(new RegExp("(?:^|;\\s)"+e+"=(.*?)(?:;\\s|$)")))?e[1]:""}function cv(e,t,n){if(!ov&&!av&&window.localStorage)try{n?localStorage.setItem(e,t):sessionStorage.setItem(e,t)}catch(r){}}function uv(e,t){var n,r={};if(void 0===t)var i=window.location,o=i.host,a=i.pathname,s=i.search.substr(1),c=i.hash;else o=(i=t.match(/\w+:\/\/((?:[\w-]+\.)+\w+)(?::\d+)?(\/[^\?\\"'\|:<>]*)?(?:\?([^'"\\<>#]*))?(?:#(\w+))?/i)||[])[1],a=i[2],s=i[3],c=i[4];return void 0!==c&&(c=c.replace(/"|'|<|>/gi,"M")),s&&function(){for(var e=s.split("&"),t=0,n=e.length;t<n;t++)if(-1!=e[t].indexOf("=")){var i=e[t].indexOf("="),o=e[t].slice(0,i);i=e[t].slice(i+1),r[o]=i}}(),s=function(){if(void 0===s)return s;for(var t=s.split("&"),n=[],r=0,i=t.length;r<i;r++)if(-1!=t[r].indexOf("=")){var o=t[r].indexOf("="),a=t[r].slice(0,o);o=t[r].slice(o+1),e.ignoreParams&&-1!=e.ignoreParams.indexOf(a)||n.push(a+"="+o)}return n.join("&")}(),c&&function(){for(var e=0==c.indexOf("#")?c.substr(1).split("&"):c.split("&"),t=0,r=e.length;t<r;t++)if(-1!=e[t].indexOf("=")){var i=e[t].indexOf("="),o=e[t].slice(0,i);if(i=e[t].slice(i+1),"adtag"===o.toLowerCase()){n=i;break}}}(),{host:o,path:a,search:s,hash:c,param:r,adtag:n}}function dv(e){var t,n=uv(e),r={dm:n.host,pvi:"",si:"",url:n.path,arg:encodeURIComponent(n.search||"").substr(0,512),ty:0};return r.pvi=function(){var t=new Date((new Date).getTime()+63072e6).toGMTString();if(e.userReport){var n=sv("pgv_uid");n&&n==e.user.user_id||(r.ty=1,cv("pgv_uid",e.user.user_id,t)),n=e.user.user_id}else(n=sv("pgv_pvi"))||(r.ty=1,cv("pgv_pvi",n=lv(),t));return n}(),r.si=((t=sv("pgv_si"))||cv("pgv_si",t=lv("s")),t),r.url=function(){var t=n.path;return e.senseQuery&&(t+=n.search?"?"+encodeURIComponent(n.search||"").substr(0,512):""),e.senseHash&&(t+=n.hash?encodeURIComponent(n.hash):""),t}(),r}function lv(e){for(var t=[0,1,2,3,4,5,6,7,8,9],n=10;1<n;n--){var r=Math.floor(10*Math.random()),i=t[r];t[r]=t[n-1],t[n-1]=i}for(n=r=0;5>n;n++)r=10*r+t[n];return(e||"")+(r+"")+ +new Date}function pv(e){return{r2:e.sid}}function hv(e){var t={};if(e){var n,r=[];for(n in e)e.hasOwnProperty(n)&&r.push(encodeURIComponent(n)+"="+encodeURIComponent(e[n]));e=r.join(";"),t.ext=e}return t}function fv(e){var t=uv(e,document.referrer);return e=uv(e),{rdm:t.host,rurl:t.path,rarg:encodeURIComponent(t.search||"").substr(0,512),adt:e.param.ADTAG||e.param.adtag||e.param.CKTAG||e.param.cktag||e.param.PTAG||e.param.ptag||e.adtag}}function mv(){try{var e=navigator,t=screen||{width:"",height:"",colorDepth:""},n={scr:t.width+"x"+t.height,scl:t.colorDepth+"-bit",lg:(e.language||e.userLanguage).toLowerCase(),tz:(new Date).getTimezoneOffset()/60}}catch(r){return{}}return n}var vv,gv,_v,yv,Sv,bv={conf:{},version:"2.0.19",init:function(e){var t={sid:0,cid:0,autoReport:0,senseHash:0,senseQuery:0,userReport:0,performanceMonitor:0,ignoreParams:[]};if(e)for(var n in e)e.hasOwnProperty(n)&&t.hasOwnProperty(n)&&(t[n]=e[n]);this.conf=t,this.conf.autoReport&&this.pgv()},pgv:function(){var e=this.conf,t=[],n=this.version;if(e.sid)if(!e.userReport||e.user&&e.user.user_id&&!parseInt(e.user.user_id,10)!==conf.user.user_id){for(var r=0,i=[dv(e),fv(e),pv(e),mv(),hv({version:n}),{random:+new Date}],o=i.length;r<o;r++)for(var a in i[r])i[r].hasOwnProperty(a)&&t.push(a+"="+(void 0===i[r][a]?"":i[r][a]));var s=function(e){e="https://pingtas.qq.com/webview/pingd?"+e.join("&").toLowerCase();var t=new Image;t.onload=t.onerror=t.onabort=function(){t=t.onload=t.onerror=t.onabort=null},t.src=e};s(t),e.performanceMonitor&&(t=function(){for(var t=function(){if(window.performance){var e=window.performance.timing,t={value:e.domainLookupEnd-e.domainLookupStart},n={value:e.connectEnd-e.connectStart},r={value:e.responseStart-(e.requestStart||e.responseStart+1)},i=e.responseEnd-e.responseStart;e.domContentLoadedEventStart?0>i&&(i=0):i=-1,e={domainLookupTime:t,connectTime:n,requestTime:r,resourcesLoadedTime:{value:i},domParsingTime:{value:e.domContentLoadedEventStart?e.domInteractive-e.domLoading:-1},domContentLoadedTime:{value:e.domContentLoadedEventStart?e.domContentLoadedEventStart-e.fetchStart:-1}}}else e="";return e}(),r=[],i=[],o=0,a=[dv(e),{r2:e.cid},mv(),{random:+new Date}],c=a.length;o<c;o++)for(var u in a[o])a[o].hasOwnProperty(u)&&i.push(u+"="+(void 0===a[o][u]?"":a[o][u]));for(u in t)t.hasOwnProperty(u)&&("domContentLoadedTime"==u?i.push("r3="+t[u].value):r.push(t[u].value));t=hv({pfm:r.join("_"),version:n}),i.push("ext="+t.ext),s(i)},void 0!==window.performance&&void 0!==window.performance.timing&&0!=window.performance.timing.loadEventEnd?t():window.attachEvent?window.attachEvent("onload",t):window.addEventListener&&window.addEventListener("load",t,!1))}else console.log("MTA H5分析错误提示：您选择了用户统计uv，请设置业务的user_id，需为int类型");else console.log("MTA H5分析错误提示：您没有设置sid")},clickStat:function(e,t){var n=this.conf,r=[],i=dv(n),o=pv(n);if(n.cid){i.dm="taclick",i.url=e,o.r2=n.cid,o.r5=function(e){e=void 0===e?{}:e;var t,n=[];for(t in e)e.hasOwnProperty(t)&&n.push(t+"="+encodeURIComponent(e[t]));return n.join(";")}(t);var a=0;for(i=(n=[i,fv(n),o,mv(),hv({version:this.version}),{random:+new Date}]).length;a<i;a++)for(var s in n[a])n[a].hasOwnProperty(s)&&r.push(s+"="+(void 0===n[a][s]?"":n[a][s]));r="https://pingtas.qq.com/webview/pingd?"+r.join("&");var c=new Image;c.onload=c.onerror=c.onabort=function(){c=c.onload=c.onerror=c.onabort=null},c.src=r}else console.log("MTA H5分析错误提示：您没有设置cid,请到管理台开通自定义事件并更新统计代码")},clickShare:function(e){var t,n,r=this.conf,i=uv(r),o=void 0===(i=i.param.CKTAG||i.param.ckatg)?[]:i.split(".");if(r.cid){i=[];var a=dv(r),s=pv(r);for(a.dm="taclick_share",a.url="mtah5-share-"+e,s.r2=r.cid,s.r5=(n=[],2===(t=o).length&&"mtah5_share"==t[0]&&n.push(t[0]+"="+t[1]),n.join(";")),e=0,a=(r=[a,fv(r),s,mv(),hv({version:this.version}),{random:+new Date}]).length;e<a;e++)for(var c in r[e])r[e].hasOwnProperty(c)&&i.push(c+"="+(void 0===r[e][c]?"":r[e][c]));c="https://pingtas.qq.com/webview/pingd?"+i.join("&");var u=new Image;u.onload=u.onerror=u.onabort=function(){u=u.onload=u.onerror=u.onabort=null},u.src=c}else console.log("MTA H5分析错误提示：您没有设置cid,请到管理台开通自定义事件并更新统计代码")}},kv=new(function(){function e(){vt(this,e),this.init()}return _t(e,[{key:"report",value:function(e,t){try{bv.clickStat(e,t)}catch(wv){}}},{key:"stat",value:function(){try{bv.pgv()}catch(wv){}}},{key:"init",value:function(){try{bv.init({sid:"500699039",cid:"500699088",autoReport:1,senseHash:0,senseQuery:0,performanceMonitor:0,ignoreParams:[]})}catch(wv){}}}]),e}()),Rv={VERSION:"4.8.5",Logger:{LogLevel:{TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,NONE:5},setLogLevel:function(e){Hn.setLogLevel(e)},enableUploadLog:function(){Hn.enableUploadLog()},disableUploadLog:function(){Hn.disableUploadLog()}},checkSystemRequirements:(Sv=mt(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return Hn.warn("breaking change occurs on TRTC.checkSystemRequirements from v4.7.0, refer to: https://trtc-1252463788.file.myqcloud.com/web/docs/TRTC.html#.checkSystemRequirements"),e.next=3,Sf();case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e)})),function(){return Sv.apply(this,arguments)}),isScreenShareSupported:function(){return kf()},getDevices:(yv=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!If()&&!Cf()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.map(function(e,t){var n=e.label;e.label||(n=e.kind+"_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 6:case"end":return e.stop()}},e)})),function(){return yv.apply(this,arguments)}),getCameras:(_v=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!If()&&!Cf()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter(function(e){return"videoinput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="camera_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 6:case"end":return e.stop()}},e)})),function(){return _v.apply(this,arguments)}),getMicrophones:(gv=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!If()&&!Cf()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter(function(e){return"audioinput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="microphone_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 6:case"end":return e.stop()}},e)})),function(){return gv.apply(this,arguments)}),getSpeakers:(vv=mt(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.mediaDevices.enumerateDevices();case 2:return t=e.sent,e.abrupt("return",t.filter(function(e){return"audiooutput"===e.kind}).map(function(e,t){var n=e.label;e.label||(n="speaker_"+t);var r={label:n,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(r.groupId=e.groupId),r}));case 4:case"end":return e.stop()}},e)})),function(){return vv.apply(this,arguments)}),createClient:function(e){if(!(""!==e.userId.trim()))throw new vh({code:fh.INVALID_PARAMETER,message:"userId cannot be all spaces"});kv.report("sdkAppID",{value:e.sdkAppId}),kv.report("version",{value:this.VERSION}),Hn.getLogLevel()!=Rv.Logger.LogLevel.NONE&&zc&&(zc=!1,console.info("******************************************************************************"),console.info("*   欢迎使用 TRTC Web SDK - 腾讯云实时音视频通信"),console.info("*   API 文档：https://trtc-1252463788.file.myqcloud.com/web/docs/index.html"),console.info("*   版本更新日志：https://cloud.tencent.com/document/product/647/38958"),console.info("*   反馈问题：https://github.com/tencentyun/TRTCSDK/issues"),console.info("******************************************************************************"),Hn.info("TRTC Web SDK Version: 4.8.5"),Hn.info("UserAgent: "+navigator.userAgent),Hn.info("URL of current page: "+location.href));var t={version:this.VERSION};return new qm(St({},t,e))},createStream:function(e){if(void 0!==e.screen&&e.screen&&void 0===e.audio&&(e.audio=!1),!(void 0===e.audio&&void 0===e.video||void 0===e.audioSource&&void 0===e.videoSource))throw new vh({code:fh.INVALID_PARAMETER,message:"LocalStream must be created by createStream() with either audio/video or audioSource/videoSourcebut can not be mixed with audio/video and audioSource/videoSource"});if(void 0!==e.screen&&!0===e.screen&&!0===e.video)throw new vh({code:fh.INVALID_PARAMETER,message:"screen/video options are mutually exclusive, they can not be both true"});if(e.audio&&e.screenAudio)throw new vh({code:fh.INVALID_PARAMETER,message:"audio/screenAudio options are mutually exclusive, they can not be both true"});if(!0!==e.screen&&!0===e.screenAudio)throw new vh({code:fh.INVALID_PARAMETER,message:"screenAudio options are configurable while screen options is true"});if(void 0!==e.screen&&!0===e.screen&&!this.isScreenShareSupported())throw new vh({code:fh.INVALID_OPERATION,message:"screen capture is not supported, please use the latest chrome browser"});return new iv(e)}};return Rv});
