
版本号`major.minor.patch`具体规则如下：
  - major：主版本号，如有重大版本重构则该字段递增，通常各主版本间接口不兼容。
  - minor：次版本号，各次版本号间接口保持兼容，如有接口新增或优化则该字段递增。
  - patch：补丁号，如有功能改善或缺陷修复则该字段递增。

>!
> - 建议您及时更新至最新版本，以便获得更好的产品稳定性及在线支持。
> - 版本升级注意事项请参见：[升级指引](https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-14-update-guideline.html)。

## Version 4.8.5 @ 2021.01.29

**Improvement**

- [Client.setTurnServer](https://trtc-1252463788.file.myqcloud.com/web/docs/Client.html#setTurnServer) 支持设置多个 turn server。
- 优化 userId 校验逻辑。

**Bug Fixed**

- 修复推流后，偶现 mute 状态不准确的问题。

## Version 4.8.4 @ 2021.01.15

**Improvement**

- [LocalStream.setVideoProfile](https://trtc-1252463788.file.myqcloud.com/web/docs/LocalStream.html#setVideoProfile) 接口支持动态调用。
- 优化仪表盘数据上报逻辑。
- 优化自动播放受限时的处理逻辑，参考：[自动播放受限处理建议](https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-11-advanced-auto-play-policy.html)。
- 优化设备插拔自动恢复失败时的处理逻辑，参考：[DEVICE_AUTO_RECOVER_FAILED](https://trtc-1252463788.file.myqcloud.com/web/docs/module-ErrorCode.html#.DEVICE_AUTO_RECOVER_FAILED)。

**Bug Fixed**

- 修复重新推流后，偶现 mute 状态不准的问题。

## Version 4.8.3 @ 2021.01.07

**Improvement**

- 优化进房接口 roomId 参数校验逻辑。

**Bug Fixed**

- 修复 v4.8.2 版本缺少第三方依赖的问题。
- 修复只订阅音频时，偶现播放无声的问题。
- 修复 iOS 自动播放受限后，偶现 resume 无声及 getAudioLevel 为0的问题。

## Version 4.8.2 @ 2020.12.31

**Breaking Change**

- 删除已废弃接口：setDefaultMuteRemoteStreams，请使用 [client.unsubscribe](https://trtc-1252463788.file.myqcloud.com/web/docs/Client.html#unsubscribe) 接口替代。

**Improvement**

- 优化进房接口 roomId 参数校验逻辑，详情请参见 [接口文档](https://trtc-1252463788.file.myqcloud.com/web/docs/Client.html#join) 及 [升级指引](https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-14-update-guideline.html)。
- 优化 peer-join 及 peer-leave 事件通知时机。

**Bug Fixed**

修复退房时，偶现报错 `Cannot read property 'isConnected' of null` 的问题。


## Version 4.8.1 @ 2020.12.25

**Bug Fixed**

- 修复 Windows 偶现听不到远端用户声音的问题。
- 修复 client.getRemoteVideoStats() 接口返回空数据的问题。

## Version 4.8.0 @ 2020.12.18

**Feature**

- 支持云端混流转码。
- 全平台支持字符串房间号，参考：[TRTC.createClient 的 userDefineRecordId 参数](https://trtc-1252463788.file.myqcloud.com/web/docs/TRTC.html#.createClient)。

**Improvement**

- 优化 h264 支持度检测逻辑。
- 优化设备切换逻辑。
- 优化 hasAudio/hasVideo 接口的状态判断逻辑。

**Bug Fixed**

- 修复断网重连偶现失败报错的问题。
- 修复 iOS Safari 频繁 add/remove track 黑屏问题。

## Version 4.7.1 @ 2020.11.27

**Improvement**

- 优化媒体设备变更时（例如：接口松动、设备插拔等）的自动恢复采集逻辑。
- 新增错误码：[DEVICE_AUTO_RECOVER_FAILED](https://trtc-1252463788.file.myqcloud.com/web/docs/module-ErrorCode.html#.DEVICE_AUTO_RECOVER_FAILED)，可用于设备恢复失败时进行提示。

**Bug Fixed**

- 修复 Chrome/87 版本偶现报错黑屏的问题。
- 修复 Native 推摄像头 + 屏幕分享，Web 重复订阅、取消订阅，屏幕分享流消失的问题。

## Version 4.7.0 @ 2020.11.20

**Feature**

支持桌面端 Firefox M56+ 及桌面端 Edge M80+。

**Breaking Change**

[TRTC.checkSystemRequirements](https://trtc-1252463788.file.myqcloud.com/web/docs/TRTC.html#.checkSystemRequirements) 返回详细检测结果，具体请参见 [接口文档](https://trtc-1252463788.file.myqcloud.com/web/docs/TRTC.html#.checkSystemRequirements) 及 [升级指引](https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-14-update-guideline.html)。

**Improvement**

- 优化上行码率控制逻辑。
- 优化获取媒体设备重试逻辑。
- 优化 Websocket 重连逻辑。
- 优化设备变更时的自动恢复推流逻辑，支持混音状态下插拔麦克风自动恢复推流。


## Version 4.6.7 @ 2020.11.05

**Bug Fixed**

- 修复 Chrome 开启硬件加速时，拉流观看偶现花屏的问题。
- 修复 iOS 微信内置浏览器无法进房拉流的问题。

## Version 4.6.6 @ 2020.10.23

**Improvement**

- 优化上行 peerConnection 重连逻辑。
- 优化下行 peerConnection 重连逻辑。
- 优化 TRTC.checkSystemRequirements 检测逻辑。
- 支持 Safari 屏幕分享，具体请参见 [屏幕分享使用教程](https://trtc-1252463788.file.myqcloud.com/web/docs/tutorial-06-advanced-screencast.html)。

**Bug Fixed**

修复因自动播放策略限制，手动恢复音频播放后，getAudioLevel 值为0的问题。

## Version 4.6.5 @ 2020.10.14

**Improvement**

- 优化 WebSocket 信令通道重连逻辑，提升连接稳定性。
- 优化日志输出逻辑。

**Bug Fixed**

- 修复 Chrome 重新订阅后，getAudioLevel 接口返回值为0的问题。
- 修复 Safari 重新订阅后，播放无声的问题。
- 修复使用 replaceTrack 替换上行音频轨道后，getLocalVideoStats 接口返回 undefined 的问题。
- 修复移动设备通话过程中，切换网络类型，偶现 WebSocket 连接断开的问题。

## Version 4.6.4 @ 2020.09.24

**Improvement**

退房后停止网络质量统计。

**Bug Fixed**

- 修复 Chrome 56 进房报错的问题。
- 修复移动端推旁路出现画面旋转的问题。
- 修复纯音频推流时云端录制异常的问题。
- 修复因分辨率不一致导致摄像头拔出后，自动恢复推流失败的问题。

## Version 4.6.3 @ 2020.08.28

**Improvement**

- 优化兼容性检测逻辑。
- 优化日志上报逻辑。
- 优化上行码率控制逻辑。

## Version 4.6.2 @ 2020.08.14

**Improvement**

- 优化上行码率调控逻辑。
- 优化 switchRole 参数校验逻辑。
- 优化上行网络质量计算逻辑。
- 优化错误提示信息。
- 检测当前推流采集设备变更时，自动恢复推流状态。

**Bug Fixes**

修复 unpublish 成功后，立即重新 publish 失败报错的问题。

## Version 4.6.1 @ 2020.07.28

**Improvement**

- [TRTC.isScreenShareSupported](https://trtc-1252463788.file.myqcloud.com/web/docs/TRTC.html#.isScreenShareSupported) Safari 不支持屏幕分享。
- 完善 subscribe & unsubscribe 接口的参数校验逻辑。
- 增加网络质量日志。

**Bug Fixes**

- 修复当未授权媒体设备，且 TRTC.createStream 接口传入的设备 ID 为空串时，SDK 报 OverconstrainedError 的问题。
- 修复上行 peerConnection 断开时没有打印日志的问题。

## Version 4.6.0 @ 2020.07.16

**Feature**

增加 NETWORK_QUALITY 事件。

## Version 4.5.0 @ 2020.07.02

**Feature**

createStream 接口增加 screenAudio 参数。

**Bug Fixes**

- 修复 Android 浏览器中回声消除不起作用的问题。
- 修复 getTransportStats 接口返回的 rtt 值为 NAN 的问题。

## Version 4.4.0 @ 2020.05.28

**Feature**

支持 Chrome >= 74 屏幕分享采集系统（Windows）或者当前 Tab 页面（Mac）的声音。
  
## Version 4.3.14 @ 2020.04.29

**Bug Fixes**

修复小程序音频 muted unmute 事件。
  
## Version 4.3.13 @ 2020.04.16

**Improvement**

优化可用性检测逻辑。

## Version 4.3.12 @ 2020.04.13

**Bug Fixes**

修复一个潜在的 RTCPeerConnection 状态变化异常。
  
## Version 4.3.11 @ 2020.03.28

**Improvement**

增加手机 QQ 浏览器检测，手机 QQ 浏览器暂时无法支持 TRTC 桌面浏览器 SDK。

**Bug Fixes**

修复 Boolean 返回值类型。

## Version 4.3.10 @ 2020.03.17

**Improvement**

- 优化环境检测逻辑。
- RtcError 增加 name code。

## Version 4.3.9 @ 2020.03.13

**Improvement**

- 增加部署环境自动检测。
- 优化日志。

## Version 4.3.8 @ 2020.02.24

**Improvement**

createClient 增加 streamId userdefinerecordid 字段。

## Version 4.3.7 @ 2020.02.21

**Improvement**

屏幕分享时切换设备抛出异常。

**Bug Fixes**

- 切换设备时释放 MediaStream，解决设备占用问题。
- 订阅接口增加处理潜在错误。

## Version 4.3.6 @ 2020.02.05

**Bug Fixes**

调整 Stream.resume() 音视频播放顺序，修复 iOS 上微信浏览器自动播放异常问题

## Version 4.3.5 @ 2020.02.05

**Improvement**

增加 publish 超时检查，提高信令发送成功率

## Version 4.3.4 @ 2020.01-06

**Improvement**
 
升级 core-js 至 v3.6.1。
  
**Bug Fixes**

- unpublish 超时后向外部抛出异常事件。
- 修复第三方库引起 V8 负优化问题。

## Version 4.3.3  @ 2019.12.25

**Improvement**

- 增加主动检测环境是否支持 webrtc 能力
- 优化 sdp 响应机制
- 优化上报逻辑

**Bug Fixes**

修复 turn URL 协议格式

## Version 4.3.2 @ 2019.12.09

**Improvement**

- 增加下行连接 ICE 断开自动重连机制。
- 去除 STUN 打洞环节，增加内网用户连接成功率及提高连接速度。
- 日志上报时间戳统一使用服务器校正后的 UTC 时间。
- 优化 ICE 错误上报。
- 增加更多关键事件上报到 avmonitor 监控。

**Bug Fixes**

- 修复 WebSocket 信令通道1005异常重连及重连错误处理。
- 修复下行丢包率上报问题。

## Version 4.3.1 @ 2019.11.23

**Improvement**

增加通话过程中上行链路 ICE 断开自动重连机制。

**Bug Fixes**

修复 STUN 打洞失败后 host 公网 IP 类型 ICE Candidate 不生效问题。

## Version 4.3.0 @ 2019.11.15

**Feature**

增加 Client.getTransportStats() API。

**Improvement**

- 增加更详细的上报日志。
- 事件解除绑定支持通配符。
- 增加连接超时时间至5s。
- 增加发布超时时间至5s。

**Bug Fixes**

修复因 zone.js 修改原型链导致 SDK 判断异常的问题。

## Version 4.2.0 @ 2019.11.04

**Feature**

增加 Client.off() 接口取消客户端事件绑定。

**Improvement**

- 通话状态统计优化。
- Client.publish() 增加权限检查。
- Stream.play()/resume() 增加自动播放错误提示。

**Bug Fixes**

LocalStream.switchDevice() 切换摄像头黑屏问题修复。

## Version 4.1.1 @ 2019.10.24

**Bug Fixes**

- 修复日志丢失问题。
- 修复断网重连远端用户丢失问题。

## Version 4.1.0 @ 2019.10.17

**Feature**

- Stream.play() 接口支持传入 HTMLDivElement 对象。
- 增加音频码率调控设置，开发者可通过 LocalStream.setAudioProfile() 设置音频属性，目前支持两种 Profile：standard 和 high。

**Bug Fixes**

- 修复旧版本 Chrome 上的 WebAudio Context 数量受限问题。
- 修复 replaceTrack() 未重启本地音视频播放器问题。
- 修复 LocalStream.setScreenProfile() 自定义属性设置未生效问题。
- 修复 audio/video player 重启及状态上报问题。

## Version 4.0.0 @ 2019.10.11

TRTC 桌面浏览器 SDK 重构版本，提供 Client/Stream 模式的接口，各对象职责更明确，语义更简洁明了。
重构版本与旧版本不兼容，除接口改动之外，还提供如下功能：

- 视频属性 （分辨率、帧率及码率）控制完全由 App 通过 SDK 的 LocalStream.setVideoProfile() 接口设置，不再支持老版本通过腾讯云控制台的“画面设定 （Spear Role）”。
- SDK 在 Stream 对象中封装了音视频播放器，音视频播放完全由 SDK 控制。
- 提供远端流的订阅与取消订阅功能，开发者可以通过 Client.subscribe()/unsubscribe() 接口灵活控制远端流的音频、视频或音视频数据流的接收。
