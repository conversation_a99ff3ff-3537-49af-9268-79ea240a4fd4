<template>
  <div :class="{'has-logo':showLogo}">
    <el-header class="v_header">
      <logo v-if="showLogo" class="logo" :collapse="false" />
      <navbar />
    </el-header>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
// import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
import { isExternal } from '@/utils/validate'
import _path from 'path'
import { Navbar } from '../../components'
// import { getDoctorVip } from '@/api/system/user'
export default {
  components: { Navbar, Logo },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {
      doctroVip: true
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  created() {
    console.log(this.permission_routes, 'permission_routes')
    // getDoctorVip().then(response => {
    //   console.log(response)
    //   this.doctroVip = response
    //   // this.doctroVip = false
    // })
  },
  methods: {
    toastVip() {
      this.$alert('您还没有激活会员或会员已到期，请登录APP端激活会员', '', {
        confirmButtonText: '知道了',
        callback: action => {
        }
      })
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(basePath, routePath) {
      console.log(basePath, routePath)
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(basePath)) {
        return basePath
      }
      // console.log(_path.resolve(basePath, routePath))
      // return;
      return _path.resolve(basePath, routePath)
    }
  }
}
</script>
<style lang="scss" scoped>
.has-logo{
  background: #fff;
}
.mqtt_alert {
  height: 30px;
  margin: auto;
  width: 30%;
}
</style>
