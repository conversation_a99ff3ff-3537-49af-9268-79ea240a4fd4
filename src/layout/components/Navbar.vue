<template>
  <div class="navbar">
    <div class="right-menu">
      <!-- <template v-if="device!=='mobile'">
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
      </template> -->
      <img src="/static/images/full_screen.png" alt="" style="width:20px;height:20px;margin-bottom:7px;" @click="click">
      <el-dropdown
        id="user-setting"
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
        size="medium"
        :hide-on-click="false"
      >
        <div class="avatar-wrapper">
          {{ userInfo.name }}&nbsp;&nbsp;医生
          <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item class="down_default">
            <div style="width: 100%;" @click="logout">
              <i class="el-icon-scissors" />
              退出登录
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
// import Screenfull from '@/components/Screenfull'
import screenfull from 'screenfull'
import { mapGetters } from 'vuex'
import API from '@/api/system/login'
export default {
  components: {},
  data() {
    return {
      isFullscreen: false,
      status: 1,
      value: true,
      dialogVisible: false,
      img: '',
      setPasswordDialogVisible: false,
      form: {
        password: '',
        code: ''
      },
      formRules: {
        code: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '请填写正确验证码'
          }
        ],
        password: [{ required: true, trigger: 'change', message: '请填写密码' }]
      },
      isSendCode: false,
      pResult: {
        msg: ''
      },
      codetext: '发送验证码',
      codetime: 0,
      signMode: false,
      isClick: false,
      isDestroy: false
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'device', 'userInfo']),
    show: {
      get() {
        return this.$store.state.settings.showRightPanel
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showRightPanel',
          value: val
        })
      }
    }
  },
  watch: {
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    click() {
      if (!screenfull.enabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        })
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.enabled) {
        screenfull.on('change', this.change)
      }
    },
    destroy() {
      if (screenfull.enabled) {
        screenfull.off('change', this.change)
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      // this.$router.push({ path:'/login'})
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('system/user/logout').then(() => {
          location.reload() // 为了重新实例化vue-router对象 避免bug
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.zx_round ,.zm_round{
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #00C692;
  display: inline-block;
  margin-right: 5px;
}
.zm_round {
  background: #CCCCCC;
}
.doctor_tag {
  margin-left: 40px;
  line-height: 25px;
  height: 25px;
  padding: 0 10px;
}
.is_online {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
}
.select_status {
  & .is_online {
    padding: 10px 80px 10px 10px;
  }
}
.mgr_a {
  margin-right: auto;
}
.down_qm {
  padding: 0;
  & .el-icon-lollipop {
    background: url(../../assets/image/qm.png) center no-repeat;
    background-size: 15px;
    margin-right: 10px;
    width: auto;
  }
  & .el-icon-lollipop:before{
      content: "替";
      font-size: 14px;
      visibility: hidden;
  }
  & /deep/ .el-submenu__title {
    padding: 0 15px;
    padding-left: 15px !important;
  }
  & .el-menu-item {
    padding: 0 15px 0 65px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.down_default {
  color: #333333;
  font-size: 14px;
  font-weight:500;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  &:before {
    content:none;
  }
  & .el-icon-office-building {
    background: url(../../assets/image/b_a.png) center no-repeat;
    background-size: cover;
    margin-right: 10px;
  }
  & .el-icon-office-building:before{
      content: "替";
      font-size: 14px;
      visibility: hidden;
  }
  & .el-icon-scissors {
    background: url(../../assets/image/exit.png) center no-repeat;
    background-size: cover;
  }
  & .el-icon-scissors:before{
      content: "替";
      font-size: 14px;
      visibility: hidden;
  }
}
.down_hos {
  color: #333333;
  font-size: 14px;
  font-weight:500;
  margin: 0;
  padding: 15px;

  &:before {
    content:none;
  }
  & .hospital {
    color: #999999;
    font-size: 12px;
  }
  & .doctor_ks {
    display: flex;
  }
}
.el-icon-office-building {
  margin: 0;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  & .pwd_dlg {
    & /deep/ .el-dialog__header {
      padding: 0;
    }
    & .el-form-item {
      height: 32px;
      line-height: 32px;
      & /deep/ .el-input__inner {
        height: 32px;
        line-height: 32px;
      }
    }
    & .el-form-item.pwd-form-item /deep/ input[type="text"] + span {
      & .el-icon-view {
        background: url(../../assets/image/eyes.png) center no-repeat;
        background-size: 16px;
      }
      & .el-icon-view:before {
        content: "替";
        font-size: 14px;
        visibility: hidden;
      }
    }
    & .el-form-item.pwd-form-item /deep/ input[type="password"] + span {
      & .el-icon-view {
        background: url(../../assets/image/eyes_close.png) center no-repeat;
        background-size: 16px;
      }
      & .el-icon-view:before {
        content: "替";
        font-size: 14px;
        visibility: hidden;
      }
    }
    & .sub_btn {
      & .el-button {
        width: 100%;
        padding: 10px 15px;
        font-size: 14px;
      }
    }
    & .code {
      & /deep/ .el-form-item__content {
        display: flex;
      }
      & .el-button {
        margin-left: 16px;
      }
    }
    & .tips_code {
      padding: 0 0 15px;
      color: #999999;
      font-size: 12px;
    }
  }
  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 60px;
    & .btn_mqtt {
      vertical-align: text-bottom;
      height: 100%;
      display: inline-block;
    }
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        position: relative;
        font-size: 14px;
        color: #333;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
