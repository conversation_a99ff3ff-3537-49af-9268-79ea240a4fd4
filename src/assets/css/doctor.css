.mgb_10 {
  margin-bottom: 10px;
}
.mgb_20 {
  margin-bottom: 20px;
}
.mgt_20 {
  margin-top: 20px;
}
.red {
  color: #FF3B30;
}
.fw {
  font-weight: 600;
}

.cut span:not(:last-child):after {
  content: '，';
}
.line-break {
  white-space: pre-line;
  word-break: break-word;
}
.app-container {
  height: 100%;
  background-color: #F5F5F5;
}
.app-container .i_disabled {
  color: #cccccc;
}
.app-container .break {
  word-break: break-word;
}
.app-container .main {
  margin: auto 0;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  top: 20px;
  background-color: #ffffff;
  border-radius: 4px;
  font-size: 14px;
}
.app-container .main .scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.app-container .main .scroll::-webkit-scrollbar-track {
  background: #FFFFFF;
  border-radius: 2px;
}
.app-container .main .scroll::-webkit-scrollbar-thumb {
  background: #dddddd;
  border-radius: 8px;
}
.app-container .main .scroll::-webkit-scrollbar-thumb:hover {
  background: #888;
}
.app-container .main .scroll::-webkit-scrollbar-corner {
  background: #FFFFFF;
}
.app-container .main .main_content .main_header {
  color: #333;
  line-height: 60px;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  justify-content: space-between;
  height: 60px;
  align-items: center;
}
.app-container .main .main_content .main_header .el-button--info.is-plain {
  color: #333333;
  background: #f4f4f5;
  border-color: #d3d4d6;
}
.app-container .main .main_content .main_header .el-button--info.is-plain:hover,
.app-container .main .main_content .main_header .el-button--info.is-plain:focus {
  color: #333333;
  background: #f4f4f5;
  border-color: #d3d4d6;
}
.app-container .main .main_content .main_header .el-button--info.is-plain.is-disabled {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}
.app-container .main .main_content .main_header h2 {
  color: #333333;
  font-size: 20px;
  padding-right: 20px;
}
.app-container .main .main_content .main_header .source-end {
  display: flex;
  align-items: center;
}
.app-container .main .main_content .main_header .source-end .time {
  color: #FF3B30;
}
.app-container .main .main_content .main_header /deep/ .el-icon-circle-close {
  background: url(../image/end_chat.png) center no-repeat;
  background-size: cover;
}
.app-container .main .main_content .main_header /deep/ .el-icon-circle-close:before {
  content: "替";
  font-size: 14px;
  visibility: hidden;
}
.app-container .main .main_content .main_header /deep/ .el-icon-circle-close-disabled {
  background: url(../image/end_chat_gray.png) center no-repeat;
  background-size: cover;
}
.app-container .main .main_content .main_header /deep/ .el-icon-circle-close-disabled:before {
  content: "替";
  font-size: 14px;
  visibility: hidden;
}
.app-container .main .main_content .el-footer {
  position: relative;
}
.app-container .main .main_content .el-footer .msg_tips {
  position: absolute;
  font-size: 12px;
  color: #999999;
  border-radius: 4px;
  box-shadow: 2px 2px 14px #cccccc;
  padding: 2px 5px;
  margin-top: -32px;
  top: 0;
  right: 10px;
}
.app-container .main .main_content .el-footer .msg_tips::after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ffffff;
  right: 15px;
}
.app-container .main .main_content .el-footer .is-disabled {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}
.app-container .main .main_content .el-footer .el-header {
  height: 30px !important;
}
.app-container .main .main_content .el-footer .el-main {
  padding: 5px;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner {
  height: 125px;
  border: 0;
  background: #ffffff;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner::-webkit-scrollbar-track {
  background: #FFFFFF;
  border-radius: 2px;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner::-webkit-scrollbar-thumb {
  background: #dddddd;
  border-radius: 8px;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner::-webkit-scrollbar-thumb:hover {
  background: #888;
}
.app-container .main .main_content .el-footer .el-main .chat_content /deep/ .el-textarea__inner::-webkit-scrollbar-corner {
  background: #FFFFFF;
}
.app-container .main .main_content .el-main .box-card {
  margin-bottom: 16px;
  width: 400px;
  border: 0;
  background: #F5F5F5;
  border-radius: 4px;
  color: #333333;
  font-size: 14px;
}
.app-container .main .main_content .el-main .box-card /deep/ .el-card__header {
  border: 0;
  padding: 16px 16px 0 16px;
  font-size: 16px;
  font-weight: 600;
}
.app-container .main .main_content .el-main .box-card /deep/ .el-card__body {
  padding: 16px;
}
.app-container .main .main_content .el-main .box-card .p_info {
  background: #ffffff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 10px;
}
.app-container .main .main_content .el-main .box-card .p_info > div:not(:last-child) {
  margin-bottom: 10px;
}
.app-container .main .main_content .el-main .box-card .p_info .title {
  width: 120px;
  color: #666666;
  display: inline-block;
  flex: none;
}
.app-container .main .main_content .el-main .box-card .p_info .content span:not(:last-child):after {
  content: '，';
}
.app-container .main .main_content .el-main .box-card .p_info .disease_wt {
  margin: 0 -7px;
}
.app-container .main .main_content .el-main .box-card .p_info .disease_wt .disease_too {
  width: 40px;
  margin: 0 7px 7px;
}
.app-container .main .main_content .el-main .box-card .p_info > div {
  display: flex;
}
.app-container .main .main_content .el-main .box-card .p_disease .title {
  width: 70px;
  color: #666666;
  display: inline-block;
  flex: none;
}
.app-container .main .main_content .el-main .box-card .p_disease > div {
  display: flex;
}
.app-container .main .main_content .el-main .box-card-right {
  width: 150px;
  min-height: 200px;
  margin: 0 0 10px auto;
  border: 0;
}
.app-container .main .main_content .el-main .box-card-right .image {
  width: 100%;
}
.app-container .main .main_content .el-main .box-card-left {
  width: 150px;
  min-height: 200px;
  margin: 0 auto 10px 0;
  border: 0;
}
.app-container .main .main_content .el-main .box-card-left .image {
  width: 100%;
}
.app-container .main .main_content .el-main .time {
  font-size: 12px;
  color: #999999;
  text-align: center;
  padding: 16px 0;
}
.app-container .main .main_content .el-main .right_chat {
  color: #333333;
  text-align: right;
}
.app-container .main .main_content .el-main .right_chat span {
  padding: 16px;
  background: rgba(0, 198, 146, 0.2);
  border-radius: 4px;
  display: inline-block;
}
.app-container .main .main_content .el-main .left_chat {
  color: #333333;
}
.app-container .main .main_content .el-main .left_chat span {
  padding: 16px;
  background: #FAFAFA;
  border-radius: 4px;
  display: inline-block;
}
.app-container .main .main_content .el-main .reason_chat {
  color: #333333;
  text-align: right;
}
.app-container .main .main_content .el-main .reason_chat .chat {
  padding: 16px;
  background: rgba(0, 198, 146, 0.2);
  border-radius: 4px;
  display: inline-block;
}
.app-container .main .main_content .el-main .reason_chat .chat div {
  text-align: left;
  line-height: 25px;
}
.app-container .main .main_content .el-main .center_success.center_error i {
  color: #FF3B30;
  margin-right: 8px;
}
.app-container .main .main_content .el-main .center_success.center_end i {
  color: #CDCDCD;
  margin-right: 8px;
}
.app-container .main .main_content .el-main .center_success {
  color: #333333;
  text-align: center;
}
.app-container .main .main_content .el-main .center_success span {
  padding: 16px;
  background: rgba(0, 198, 146, 0.2);
  border-radius: 4px;
  display: inline-block;
}
.app-container .main .main_content .el-main .center_success /deep/ .el-icon-check {
  background: url(../image/checked.png) center no-repeat;
  background-size: 14px;
  margin-right: 8px;
}
.app-container .main .main_content .el-main .center_success /deep/ .el-icon-check:before {
  content: "替";
  font-size: 14px;
  visibility: hidden;
}
.app-container .main .right .el-header {
  border-left: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
  color: #333333;
  line-height: 60px;
  font-size: 20px;
  font-weight: 600;
}
.app-container .main .right .el-footer {
  border-top: 1px solid #eeeeee;
  border-left: 1px solid #eeeeee;
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  height: 60px !important;
}
.app-container .main .right .el-footer .is-disabled {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}
.app-container .main .right .el-main {
  border-left: 1px solid #eeeeee;
  color: #666666;
}
.app-container .main .right .el-main .patient_info {
  padding-bottom: 20px;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 20px;
  color: #333333;
}
.app-container .main .right .el-main .patient_info .label_i {
  color: #666666;
}
.app-container .main .right .el-main .label_cf {
  display: flex;
  justify-content: space-between;
}
.app-container .main .right .el-main .label_cf .addDrug {
  color: #00C692;
}
.app-container .main .right .el-main .drug_item {
  background: #FAFAFA;
  border-radius: 4px;
  padding: 16px;
  margin: 10px 0;
}
.app-container .main .right .el-main .drug_item .drugs_msg {
  display: flex;
}
.app-container .main .right .el-main .drug_item .drugs_msg .avatar {
  flex: none;
  margin-right: 10px;
  width: 50px;
  height: 50px;
  border-radius: 6px;
}
.app-container .main .right .el-main .drug_item .drugs_msg .right {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}
.app-container .main .right .el-main .drug_item .drugs_msg .right .name {
  font-weight: 600;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.app-container .main .right .el-main .drug_item .drugs_msg .right .usage {
  font-size: 12px;
  color: #999999;
}
.app-container .main .right .el-main .drug_item .cn_drugs_top {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.app-container .main .right .el-main .drug_item .cn_drugs_top .el-form-item {
  margin: 0 5px;
}
.app-container .main .right .el-main .drug_item .cn_drugs_top .el-form-item /deep/ .el-form-item__error {
  width: 110px;
}
.app-container .main .right .el-main .drug_item .cn_drugs_top .cn_input {
  width: 56px;
}
.app-container .main .right .el-main .divider_rp {
  border-top: 1px solid #dddddd;
  padding: 15px 0 10px;
}
.app-container .main .right .el-main .cn_drugs {
  display: flex;
  align-items: center;
}
.app-container .main .right .el-main .cn_drugs .cn_drugs_name {
  margin-bottom: 18px;
}
.app-container .main .right .el-main .cn_drugs .el-input-number--mini {
  line-height: 30px;
  width: 120px;
}
.app-container .main .right .el-main .cn_drugs .el-input-number--mini /deep/ span {
  background: transparent;
}
.app-container .main .right .el-main .cn_drugs:last-child .el-form-item,
.app-container .main .right .el-main .cn_drugs:last-child .cn_drugs_name {
  margin-bottom: 0;
}
.app-container .main .right .el-main .drugs .unit_tips {
  font-size: 10px;
  color: #cccccc;
  vertical-align: text-bottom;
}
.app-container .main .right .el-main .drugs .max-input {
  width: 100%;
}
.app-container .main .right .el-main .drugs .midd-input {
  width: 100%;
  padding-right: 10px;
}
.app-container .main .right .el-main .drugs .each_dose .el-select {
  padding-right: 0;
}
.app-container .main .right .el-main .drugs .el-form-item.form-item-last {
  margin-bottom: 0;
  height: auto;
  line-height: auto;
}
.app-container .main .right .el-main .drugs .el-form-item.form-item-last /deep/ .el-form-item__label {
  padding-left: 10px;
}
.app-container .main .right .el-main .drugs .el-form-item.form-item-right-pd /deep/ .el-form-item__content {
  padding-right: 10px;
}
.app-container .main .right .el-main .drugs .el-form-item.form-item-no-required /deep/ .el-form-item__label {
  padding-left: 10px;
}
.app-container .main .right .el-main .drugs .el-form-item.ts_input /deep/ input {
  background-color: #ffffff;
}
.app-container .main .right .el-main .drugs .el-form-item.ts_input /deep/ .is-disabled input {
  background-color: #FAFAFA;
}
.app-container .main .right .el-main .drugs .el-form-item {
  height: 32px;
  line-height: 32px;
  width: 100%;
  display: flex;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ input {
  border: 1px solid #dddddd;
  height: 32px;
  line-height: 32px;
  background: #FAFAFA;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ textarea {
  border: 1px solid #dddddd;
  background: #FAFAFA;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ input::-webkit-outer-spin-button,
.app-container .main .right .el-main .drugs .el-form-item /deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ input[type="number"] {
  -moz-appearance: textfield;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ .el-form-item__label {
  height: 32px;
  line-height: 32px;
  color: #666666;
  font-weight: 500;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ .el-form-item__content {
  flex: 1;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ .el-form-item__content .el-input-group--append {
  border: 1px solid #dddddd;
  background: #FAFAFA;
  border-radius: 4px;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ .el-form-item__content .el-input-group--append input {
  border: 0;
  background-color: transparent;
}
.app-container .main .right .el-main .drugs .el-form-item /deep/ .el-form-item__content .el-input-group--append .el-input-group__append {
  background-color: transparent;
  color: #333333;
}
.app-container .main .right .el-main .drugs /deep/ .el-input-group__append {
  background-color: #fff;
  border: none;
  padding: 0 10px;
}
.app-container .main .right .el-main .form-item-zd-tag {
  border: 1px solid #eeeeee;
  border-radius: 4px;
}
.app-container .main .right .el-main .form-item-zd-tag /deep/ .el-form-item__content {
  display: flex;
  flex-wrap: wrap;
}
.app-container .main .right .el-main .form-item-zd-tag /deep/ .el-form-item__content /deep/ input {
  border: none;
}
.app-container .main .right .el-main .form-item-zd-tag .zd_tag {
  display: flex;
  flex-wrap: wrap;
  padding: 0 5px;
}
.app-container .main .right .el-main .form-item-zd-tag .zd_tag .el-tag {
  margin: 4px 2px 0;
}
.app-container .main .right .el-main .form-item-top {
  margin-top: 5px;
  width: 100%;
  display: flex;
}
.app-container .main .right .el-main .form-item-top /deep/ .el-form-item__label {
  text-align: left;
  height: 32px;
  line-height: 32px;
  color: #666;
  font-weight: 500;
}
.app-container .main .right .el-main .form-item-top /deep/ .el-form-item__content {
  width: 100%;
}
.app-container .main .left {
  border-right: 1px solid #eeeeee;
  padding: 0;
  font-size: 14px;
}
.app-container .main .left .item_line {
  background: #F5F5F5;
  margin: 20px 20px 0;
  border-radius: 4px;
  color: #666666;
  font-size: 12px;
  width: -webkit-fill-available;
  padding: 10px;
  text-align: center;
}
.app-container .main .left .el-menu {
  border: none;
}
.app-container .main .left .memu_item.close {
  filter: grayscale(100%);
}
.app-container .main .left .memu_item.close .name {
  color: #999999 !important;
}
.app-container .main .left .memu_item {
  border-bottom: 1px solid #eeeeee;
  display: flex;
  height: auto !important;
  padding: 15px 5px 15px 20px !important;
}
.app-container .main .left .memu_item /deep/ .el-badge__content.is-fixed {
  top: 10px;
  right: 20px;
}
.app-container .main .left .memu_item .item {
  padding: 0;
}
.app-container .main .left .memu_item .item /deep/ .el-badge__content {
  background-color: #FF3B30;
}
.app-container .main .left .memu_item .el-avatar {
  flex: none;
  border-radius: 5px;
}
.app-container .main .left .memu_item .item_right {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  margin-left: 10px;
  overflow: hidden;
  padding-right: 20px;
  flex: 1;
}
.app-container .main .left .memu_item .item_right .inn {
  line-height: initial;
  -webkit-line-clamp: 1;
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.app-container .main .left .memu_item .item_right .right_bottom {
  color: #999999;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-content: center;
}
.app-container .main .left .memu_item .item_right .right_bottom > .text {
  overflow: hidden;
  text-overflow: ellipsis;
}
.app-container .main .left .memu_item .item_right .right_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.app-container .main .left .memu_item .item_right .right_top .name {
  color: #333333;
  max-width: 70px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden!important;
  text-overflow: ellipsis!important;
}
.app-container .main .left .memu_item .item_right .right_top .time {
  font-size: 14px;
  color: #999999;
}
.app-container .main .left .memu_item .item_right .right_top .new {
  color: #FF3B30;
}
.app-container .main .left .el-menu-item.is-active {
  background-color: #00C692;
}
.app-container .main .left .el-menu-item.is-active .name {
  color: #ffffff !important;
}
.app-container .main .left .el-menu-item.is-active .time {
  color: #ffffff !important;
}
.app-container .main .left .el-menu-item.is-active .right_bottom {
  color: #ffffff !important;
}
.app-container .main .main_footer {
  border-top: 1px solid #eee;
  color: #333;
  padding: 0;
  /* line-height: 100px; */
  height: 230px !important;
}
.app-container .main .main_footer .el-header {
  line-height: 30px;
}
.app-container .main .main_footer .el-footer {
  line-height: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 5px;
}
.app-container .main .main_footer .el-footer .el-button {
  margin: 0 10px;
}
.app-container .main .main_footer .el-footer .tips {
  color: #999999;
  font-size: 12px;
}
.app-container .main .main_footer .el-container {
  height: 100%;
}
.app-container .main .main_footer .el-container .el-header /deep/ .el-icon-picture {
  background: url(../image/select_img.png) center no-repeat;
  background-size: 14px;
  margin-right: 8px;
}
.app-container .main .main_footer .el-container .el-header /deep/ .el-icon-picture:before {
  content: "替";
  font-size: 14px;
  visibility: hidden;
}
.app-container .main .el-aside {
  color: #333;
}
.app-container .main.avatar {
  flex: none;
}
.app-container .dialog_reason /deep/ input::-webkit-input-placeholder {
  /* WebKit browsers */
  font-size: 14px;
}
.app-container .dialog_reason .reason_s {
  padding-top: 10px;
  margin: 0 -10px;
}
.app-container .dialog_reason .reason_s .tag {
  background: #F5F5F5;
  padding: 5px 10px;
  color: #666666;
  border-radius: 4px;
  margin: 0 10px 10px;
  display: inline-block;
  border: 1px solid #F5F5F5;
  cursor: pointer;
}
.app-container .dialog_reason .reason_s .tag:active {
  color: #00C692;
  background: rgba(0, 198, 146, 0.1);
  border: 1px solid #00C692;
}
.app-container .dialog_reason .reason_s .tag.on {
  color: #00C692;
  background: rgba(0, 198, 146, 0.1);
  border: 1px solid #00C692;
}
.form-option-drug {
  height: auto;
  display: flex;
  padding: 10px 15px;
}
.form-option-drug .right {
  line-height: normal;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.form-option-drug .right .name {
  font-size: 14px;
  color: #333333;
}
.form-option-drug .right .r-bottom {
  font-size: 12px;
  color: #999999;
}
.form-option-drug .drug-image {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}
.form-option-drug.hover {
  background-color: rgba(0, 198, 146, 0.1);
}
.submit-button-d {
  padding: 0 15px;
  height: 32px;
  font-size: 14px;
}
.tabUl {
  margin: 20px 0;
  width: 100%;
  display: flex;
  justify-content: center;
}
.tabli {
  width: 90px;
  height: 40px;
  background: #fff;
  text-align: center;
  line-height: 40px;
  border: 1px solid #eee;
}
.active {
  background: #07c8eb;
  color: #fff;
}
.listUl {
  width: 100%;
  padding: 0 20px;
}
.listLi {
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  padding: 20px;
  background: #eee;
  margin-bottom: 20px;
}
.row {
  margin-top: 15px;
}
.wid {
  width: 800px;
}
.flexBox {
  display: flex;
  justify-content: space-between;
}
.span {
  margin-right: 30px;
}
