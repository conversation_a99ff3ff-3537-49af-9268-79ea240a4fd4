@import "common.less";

#videoFrame {
  left: calc(50% - 188px);
  width: 375px;
  height: 667px;
  background-color: #000000;
  border-radius: 8px;
  position: relative;
  cursor: move;
  overflow: hidden;
  z-index: 999 !important;

  #remoteVideoSurface {
    position: absolute;
    width: 375px;
    height: 667px;
    left: 0;
    top: 0;

    div {
      width: 375px !important;
      height: 667px !important;
    }
  }

  #localVideoSurface {
    width: 68px;
    height: 102px;
    background: #d8d8d8;
    border-radius: 2px;
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    overflow: hidden;
  }

  #operationBlock {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 43px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .time {
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 17px;
    }

    .hangupIcon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-top: 18px;
      cursor: pointer;
    }
  }
}

#webRTCRequestDialog {
  left: 20px;
  top: (calc(100% - 224px));
  width: 300px;
  height: 170px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  filter: blur(0px);
  padding: 10px;
  cursor: move;
  z-index: 999 !important;

  .requestInfo {
    display: flex;
  }

  .requestName {
    padding-left: 10px;
    display: flex;
    flex-direction: column;
  }

  .avatar {
    width: 40px;
    height: 40px;
  }
  .patientName {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
  }
  .inviteLabel {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 22px;
  }

  .operationBlock {
    display: flex;
    position: absolute;
    bottom: 10px;
    right: 10px;

    span {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }

    .rejcet {
      width: 80px;
      height: 32px;
      background: #ff3b30;
      border-radius: 4px;
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 8px;
        margin-left: 10px;
        margin-right: 10px;
      }

    }
    .accept {
      width: 80px;
      height: 32px;
      background: #1fb923;
      border-radius: 4px;
      margin-left: 10px;
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-left: 10px;
        margin-right: 10px;
      }
    }
  }
}

.videoIcon {
  width: 16px;
  height: 16px;
}

.videoEndFlagIcon {
  width: 24px;
  height: 14px;
  margin-left: 10px;
  margin-bottom: -2px;
}
