@import "common.less";
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url(/static/images/bg_img.png);
  background-size: cover;
}

.title {
  margin: 0px auto 70px auto;
  color: @c_333;
  font-size: @fs24;
  font-weight: 600;
}
.main {
  min-width: 80%;
  min-height: 70%;
  background-color: #ffffff;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  border-radius:4px;
  box-shadow:0px 0px 60px 0px rgba(0,0,0,0.15);
  & .logo {
    position: absolute;
    top: 60px;
    left: 60px;
    font-size: @fs16;
    color: @c_333;
    font-weight: 500;
    display: flex;
    align-items: center;
    img {
      width: 30px;
      margin-right: 10px;
    }
  }
  & .left {
    img {
      width: 500px;
    }
  }
  .login-form {
    border-radius: 6px;
    background: #ffffff;
    padding: 25px 25px 5px 25px;
    width: 400px;
    .el-input {
      height: 38px;
      & /deep/ input {
        height: 38px;
        border: none;
        border-bottom: 1px solid @color_1;
        padding: 0;
        font-size: @fs14;
        &::-webkit-input-placeholder {
          color: @c_input;
        }
        &:-webkit-autofill {
          -webkit-box-shadow: 0 0 0px 1000px white inset !important;
        }
      }
    }
    & .code {
      margin-top: 70px;
      & /deep/ .el-form-item__content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid @color_1;
        & /deep/ input {
          height: 38px;
          border: none;
          padding: 0;
          font-size: @fs14;
        }
      }
      & .send_code {
        flex: none;
        color: @c_text;
        cursor: pointer;
        font-size: @fs14;
      }
      & .send_code.disabled {
        color: @c_input;
        cursor: default;
      }
    }
    & .login_btn {
      margin-top: 40px;
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
    & /deep/ .el-form-item__error {
      margin-top: 5px;
      color: @c_error;
      font-size: @fs12;
    }
    & /deep/ .el-icon-circle-close {
      color: @c_error;
      font-size: @fs18;
    }
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  display: inline-block;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
