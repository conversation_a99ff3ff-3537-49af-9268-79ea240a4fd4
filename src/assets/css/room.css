*{
    margin: 0;
    padding: 0;
}
html{
    width: 100%;
    height: 100%;
}
body{
    width: 100%;
    height: 100%;
    background-color: #fff;
    font-family: Futura,sans-serif;
}
video{
    background-color: #d8d8d8;
}
/* @media screen and (min-width:960px) {
    video{
        width: auto !important;
        height: 100% !important;
        position: inherit !important;
    }
    .video-div{
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
@media screen and (max-width:960px) {
    video{
        width: 100% !important;
        height: auto !important;
        position: inherit !important;
    }
    .video-div{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
} */
#room-root{
    width: 100%;
    height: 100%;
    /* min-width: 1500px; */
    min-height: 700px;
    display: none;
    align-items: flex-start;
    background-color: #f0f0f0
}
.member{
    cursor: default;
    border-bottom-style: solid;
    border-width: 1px;
    border-bottom-color: #f0f0f0;
}
#video-grid{
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(3, 1fr);
    grid-template-areas: 'm m m a b'
                         'm m m c d'
                         'e f g h i';
}
#video-grid>div{
    position: relative;
    padding: 10px;
}
#main-video{
    width: 100%;
    height: 100%;
    padding: 10px;
    grid-area: 1/1/3/4;
}
.video-box{
    width: 100%;
    height: 100%;
}
.mask{
    width: 100%;
    height: 100%;
    position: absolute;
    font-size: 14px; 
    color: #888888; 
    z-index: 9; 
    justify-content: center;
    top: 0;
    left: 0;
}
div[id^=player] {
    background-color: #d8d8d8 !important;
}