@color_main: #00ffff;
@color_main2: #00ffee;
@color_1: #eeeeee;
@color_2: #dddddd;
@c_333: #333333;
@c_999: #999999;
@c_input: #cccccc;
@c_text: #00C692;
@c_error: #FF3B30;
@bg_color: #F5F5F5;
@bg_color2: #FAFAFA;
@scoll_color: @color_2;
@c_fff: #ffffff;
@c_666: #666666;
@c_ccc: #cccccc;
@fs12: 12px;
@fs13: 13px;
@fs14: 14px;
@fs15: 15px;
@fs16: 16px;
@fs17: 17px;
@fs18: 18px;
@fs19: 19px;
@fs20: 20px;
@fs24: 24px;
.mgb_10 {
  margin-bottom: 10px;
}
.mgb_20 {
  margin-bottom: 20px;
}
.red {
  color: @c_error;
}
.fw {
  font-weight: 600;
}
& .cut span:not(:last-child):after {
  content: '，';
}
.line-break {
  white-space: pre-line;
  word-break: break-word;
}

.flex {
  /* 转为弹性盒模型  */
  display: flex;
}
.flex_wrap {
  /* 转为弹性盒模型并自动换行  */
  display: flex;
  flex-wrap: wrap;
}

.flex_b {
  /* 垂直底部对齐  */
  display: flex;
  align-items: flex-end;
}
.flex_tb {
  /* 垂直两端对齐  */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex_tb_c {
  /* 多行垂直两端对齐，水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.flex_line_c {
  /* 多行垂直水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}
.flex_line_c_m {
  /* 多行垂直居中，水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.flex_line_end {
  /* 多行垂直起点在下沿 */
  display: flex;
  flex-direction: column-reverse;
}
.flex_lr {
  /* 水平两端对齐，剩余空间平均分布 */
  display: flex;
  justify-content: space-between;
}

.flex_lr_m {
  /* 水平两端对齐，剩余空间平均分布，垂直居中 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_c_m {
  /* 垂直水平居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_c {
  /* 水平居中 */
  display: flex;
  justify-content: center;
}

.flex_m {
  /* 垂直居中 */
  display: flex;
  align-items: center;
}

.flex_nosize {
  /* 子元素不自动 */
  flex-shrink: 0;
}

.flex_autosize {
  /* 子元素自动宽度 */
  flex-grow: 1;
}

.flex_inline {
  /* 转为行内弹性盒模型  */
  display: inline-flex;
}
.flex_end {
  /* 尾部对其 */
  display: flex;
  justify-content: flex-end;
}

.flex0{
  flex: 0;
  min-width: 0;
}
.flex1{
  flex: 1;
  min-width: 0;
}
.flex_none {
  flex:none;
}
.red {
  color: @c_error;
}
.pt_10{
  padding-top: 10px;
}
.pb_10{
  padding-bottom: 10px;
}
.pt_20{
  padding-top: 20px;
}
.pl_10 {
  padding-left: 10px;
}
.tr{
  text-align: right;
}
.tc{
  text-align: center;
}
.w_100 {
  width: 100%;
}
.b {
  font-weight: bold;
}
.line {
  text-decoration: underline;
  cursor: pointer;
  color: @c_text;
}