@import "common.less";
.app-container {
  height: 100%;
  background-color: @bg_color;
  .i_disabled {
    color: @c_ccc;
  }
  .break {
    word-break: break-word;
  }
  & .main { 
    margin: auto 0;
    position: absolute;
    left: 20px;
    right: 20px;
    bottom: 20px;
    top: 20px;
    background-color: #ffffff;
    border-radius: 4px;
    font-size: @fs14;
    .scroll::-webkit-scrollbar{
      width:8px;
      height:8px;
    }
    .scroll::-webkit-scrollbar-track{
      background: #FFFFFF;
      border-radius:2px;
    }
    .scroll::-webkit-scrollbar-thumb{
      background: @scoll_color;
      border-radius:8px;
    }
    .scroll::-webkit-scrollbar-thumb:hover{
      background: #888;
    }
    .scroll::-webkit-scrollbar-corner{
      background: #FFFFFF;
    }
    & .main_content {
      // min-width: 500px;
      & .main_header {
        color: #333;
        line-height: 60px;
        border-bottom: 1px solid @color_1;
        display: flex;
        justify-content: space-between;
        height: 60px;
        align-items: center;
        & .el-button--info.is-plain {
          color: @c_333;
          background: #f4f4f5;
          border-color: #d3d4d6;
        }
        & .el-button--info.is-plain:hover, & .el-button--info.is-plain:focus {
          color: @c_333;
          background: #f4f4f5;
          border-color: #d3d4d6;
        }
        & .el-button--info.is-plain.is-disabled {
          color: #bcbec2;
          background-color: #f4f4f5;
          border-color: #e9e9eb;
        }
        & h2 {
          color: @c_333;
          font-size: @fs20;
          padding-right: 20px;
        }
        & .source-end {
          display: flex;
          align-items: center;
          & .time {
            color: @c_error;
          }
        }
        & /deep/ .el-icon-circle-close {
          background: url(../image/end_chat.png) center no-repeat;
          background-size: cover;
        }
        & /deep/ .el-icon-circle-close:before{
            content: "替";
            font-size: 14px;
            visibility: hidden;
        }
        & /deep/ .el-icon-circle-close-disabled {
          background: url(../image/end_chat_gray.png) center no-repeat;
          background-size: cover;
        }
        & /deep/ .el-icon-circle-close-disabled:before{
          content: "替";
          font-size: 14px;
          visibility: hidden;
        }
      }
      & .el-footer {
        position: relative;
        & .msg_tips {
          position: absolute;
          font-size: @fs12;
          color: @c_999;
          border-radius: 4px;
          box-shadow: 2px 2px 14px @c_ccc;
          padding: 2px 5px;
          margin-top: -32px;
          top: 0;
          right: 10px;
          &::after {
            content: ' ';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid @c_fff;
            right: 15px;
          }
        }
        & .is-disabled {
          color: #bcbec2;
          background-color: #f4f4f5;
          border-color: #e9e9eb;
        }
        & .el-header {
          height: 30px !important;
        }
        & .el-main {
          padding: 5px;
          & .chat_content {
            /deep/ .el-textarea__inner {
              height: 125px;
              border: 0;
              background: @c_fff;
              &::-webkit-scrollbar{
                width:8px;
                height:8px;
              }
              &::-webkit-scrollbar-track{
                background: #FFFFFF;
                border-radius:2px;
              }
              &::-webkit-scrollbar-thumb{
                background: @scoll_color;
                border-radius:8px;
              }
              &::-webkit-scrollbar-thumb:hover{
                background: #888;
              }
              &::-webkit-scrollbar-corner{
                background: #FFFFFF;
              }
            }
          }
        }

      }
      & .el-main {
        & .box-card {
          margin-bottom: 16px;
          width: 400px;
          border: 0;
          background: @bg_color;
          border-radius:4px;
          color: @c_333;
          font-size: @fs14;
          & /deep/ .el-card__header {
            border: 0;
            padding: 16px 16px 0 16px;
            font-size: @fs16;
            font-weight: 600;
          }
          & /deep/ .el-card__body {
            padding: 16px;
          }
          & .p_info {
            background: @c_fff;
            border-radius: 4px;
            padding: 16px;
            margin-bottom: 10px;
            & > div:not(:last-child) {
              margin-bottom: 10px;
            }
            & .title {
              width: 120px;
              color: @c_666;
              display: inline-block;
              flex: none;
            }
            & .content span:not(:last-child):after {
              content: '，';
            }
            & .disease_wt {
              margin: 0 -7px;
              & .disease_too {
                width: 40px;
                margin: 0 7px 7px;
              }
            }
            & > div {
              display: flex;
            }
          }
          & .p_disease {
            & .title {
              width: 70px;
              color: @c_666;
              display: inline-block;
              flex: none;
            }
            & > div {
              display: flex;
            }
          }
        }
        & .box-card-right {
          width: 150px;
          min-height: 200px;
          margin: 0 0 10px auto;
          border: 0;
          & .image {
            width: 100%;
          }
        }
        & .box-card-left {
          width: 150px;
          min-height: 200px;
          margin: 0 auto 10px 0;
          border: 0;
          & .image {
            width: 100%;
          }
        }
        & .time {
          font-size: @fs12;
          color: @c_999;
          text-align: center;
          padding: 16px 0;
        }
        & .right_chat {
          color: @c_333;
          text-align: right;
          & span {
            padding: 16px;
            background: rgba(0,198,146,0.2);
            border-radius: 4px;
            display: inline-block;
          }
        }
        & .left_chat {
          color: @c_333;
          & span {
            padding: 16px;
            background: @bg_color2;
            border-radius: 4px;
            display: inline-block;
          }
        }
        & .reason_chat {
          color: @c_333;
          text-align: right;
          & .chat {
            display: inline-block;
            padding: 16px;
            background: rgba(0,198,146,0.2);
            border-radius: 4px;
            display: inline-block;
            & div {
              text-align: left;
              line-height: 25px;
            }
          }
        }
        & .center_success.center_error {
          & i {
            color: @c_error;
            margin-right: 8px;
          }
        }
        & .center_success.center_end {
          & i {
            color: #CDCDCD;
            margin-right: 8px;
          }
        }
        & .center_success {
          color: @c_333;
          text-align: center;
          & span {
            padding: 16px;
            background: rgba(0,198,146,0.2);
            border-radius: 4px;
            display: inline-block;

          }
          & /deep/ .el-icon-check {
            background: url(../image/checked.png) center no-repeat;
            background-size: 14px;
            margin-right: 8px;
          }
          & /deep/ .el-icon-check:before{
              content: "替";
              font-size: 14px;
              visibility: hidden;
          }
        }
      }
    }
    & .right {
      & .el-header {
        border-left: 1px solid @color_1;
        border-bottom: 1px solid @color_1;
        color: @c_333;
        line-height: 60px;
        font-size: @fs20;
        font-weight: 600;
      }
      & .el-footer {
        border-top: 1px solid @color_1;
        border-left: 1px solid @color_1;
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        height: 60px !important;
        & .is-disabled {
          color: #bcbec2;
          background-color: #f4f4f5;
          border-color: #e9e9eb;
        }
      }
      & .el-main {
        border-left: 1px solid @color_1;
        color: @c_666;
        & .patient_info {
          padding-bottom: 20px;
          border-bottom: 1px solid @color_1;
          margin-bottom: 20px;
          color: @c_333;
          & .label_i {
            color: @c_666;
          }
        }
        & .label_cf {
          display: flex;
          justify-content: space-between;
          & .addDrug {
            color: @c_text;
          }
        }
        & .drug_item {
          background: @bg_color2;
          border-radius: 4px;
          padding: 16px;
          margin: 10px 0;
          & .drugs_msg {
            display: flex;
            & .avatar {
              flex: none;
              margin-right: 10px;
              width: 50px;
              height: 50px;
              border-radius: 6px;
            }
            .right {
              display: flex;
              justify-content: center;
              flex-direction: column;
              width: 100%;
              & .name {
                font-weight: 600;
                color: @c_333;
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
              & .usage {
                font-size: 12px;
                color: @c_999;
              }
            }
          }
          & .cn_drugs_top {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            & .el-form-item {
              margin: 0 5px;
              & /deep/ .el-form-item__error {
                width: 110px;
              }
            }
            & .cn_input {
              width: 56px;
            }
          }
        }
        & .divider_rp {
          border-top: 1px solid @color_2;
          padding: 15px 0 10px;
        }
        & .cn_drugs {
          display: flex;
          align-items: center;
          & .cn_drugs_name {
            margin-bottom: 18px;
          }
          & .el-input-number--mini {
            line-height: 30px;
            width: 120px;
            & /deep/ span {
              background: transparent;
            }
          }
        }
        & .cn_drugs:last-child {
          & .el-form-item, & .cn_drugs_name {
            margin-bottom: 0;
          }
        }
        & .drugs {
          & .unit_tips {
            font-size: 10px;
            color: @c_ccc;
            vertical-align: text-bottom;
          }
          & .max-input {
            width: 100%;
          }
          & .midd-input {
            width: 100%;
            padding-right: 10px;
          }
          & .min-input {
            // width: 100px;
          }
          & .each_dose .el-select {
            padding-right: 0;
          }
          & .el-form-item.form-item-last {
            margin-bottom: 0;
            height: auto;
            line-height: auto;
            /deep/ .el-form-item__label {
              padding-left: 10px;
            }
          }
          & .el-form-item.form-item-right-pd {
            /deep/ .el-form-item__content {
              padding-right: 10px;
            }
          }
          & .el-form-item.form-item-no-required {
            /deep/ .el-form-item__label {
              padding-left: 10px;
            }
          }
          & .el-form-item.ts_input /deep/ input {
            background-color: #ffffff;
          }
          & .el-form-item.ts_input /deep/ .is-disabled input {
            background-color: @bg_color2;
          }
          & .el-form-item {
            // margin-bottom: 10px;
            height: 32px;
            line-height: 32px;
            width: 100%;
            display: flex;
            & /deep/ input {
              border:1px solid @color_2;
              height: 32px;
              line-height: 32px;
              background: @bg_color2;
            }
            & /deep/ textarea {
              border:1px solid @color_2;
              background: @bg_color2;
            }
            & /deep/ input::-webkit-outer-spin-button,
            & /deep/ input::-webkit-inner-spin-button {
              -webkit-appearance: none;
            }
            & /deep/ input[type="number"]{
              -moz-appearance: textfield;
            }
            & /deep/ .el-form-item__label {
              height: 32px;
              line-height: 32px;
              color: @c_666;
              font-weight: 500;
            }
            & /deep/ .el-form-item__content {
              flex: 1;
              & .el-input-group--append {
                border:1px solid @color_2;
                background: @bg_color2;
                border-radius: 4px;
                input {
                  border: 0;
                  background-color: transparent;
                }
                .el-input-group__append {
                  background-color: transparent;
                  color: @c_333;
                }
              }
            }
          }
          & /deep/ .el-input-group__append{
            background-color: #fff;
            border: none;
            padding: 0 10px;
          }

        }
        & .form-item-zd-tag {
          border: 1px solid @color_1;
          border-radius: 4px;
          & /deep/ .el-form-item__content {
            display: flex;
            flex-wrap: wrap;
            & /deep/ input {
              border: none;
            }
          }
          & .zd_tag {
            display: flex;
            flex-wrap: wrap;
            padding: 0 5px;
            & .el-tag {
              margin: 4px 2px 0;
            }
          }
        }
        & .form-item-top {
          margin-top: 5px;
          width: 100%;
          display: flex;
          & /deep/ .el-form-item__label {
            text-align: left;
            height: 32px;
            line-height: 32px;
            color: #666;
            font-weight: 500;
          }
          & /deep/ .el-form-item__content {
            width: 100%;
            & .el-tag:nth-last-child(-n+2) {
              // background-color: red;
              // color: #FFFFFF;
            }
          }
        }
      }
    }

    & .left {
      border-right: 1px solid @color_1;
      padding: 0;
      font-size: @fs14;
      & .item_line {
        background: @bg_color;
        margin: 20px 20px 0;
        border-radius:4px;
        color: @c_666;
        font-size: @fs12;
        width: -webkit-fill-available;
        padding: 10px;
        text-align: center;
      }
      & .el-menu {
        border: none;
      }
      & .memu_item.close{
        filter: grayscale(100%);
        & .name {
          color: @c_999 !important;
        }
      }
      & .memu_item {
        border-bottom: 1px solid @color_1;
        display: flex;
        height: auto !important;
        padding: 15px 5px 15px 20px !important;
        & /deep/ .el-badge__content.is-fixed {
          top: 10px;
          right: 20px;
        }
        & .item {
          padding: 0;
          & /deep/ .el-badge__content {
            background-color: @c_error;
          }
        }
        & .el-avatar {
          flex: none;
          border-radius: 5px;
        }
        & .item_right {
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          margin-left: 10px;
          overflow: hidden;
          padding-right: 20px;
          flex: 1;
          & .inn{
            line-height: initial;
            -webkit-line-clamp: 1;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
          }
          & .right_bottom {
            color: @c_999;
            font-size: @fs12;
            display: flex;
            justify-content: space-between;
            align-content: center;
            >.text{
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          & .right_top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            & .name {
              color: @c_333;
              max-width: 70px;
              display:inline-block;
              white-space: nowrap;
              overflow: hidden!important;
              text-overflow: ellipsis!important;
            }
            & .time {
              font-size: @fs14;
              color: @c_999;
            }
            & .new {
              color: @c_error;
            }
          }
        }
      }
      & .el-menu-item.is-active {
        background-color: @c_text;
        & .name {
          color: @c_fff !important;
        }
        & .time {
          color: @c_fff !important;
        }
        & .right_bottom {
          color: @c_fff !important;
        }
      }
    }
    & .main_footer {
      border-top: 1px solid #eee;
      color: #333;
      padding: 0;
      /* line-height: 100px; */
      height: 230px !important;
      & .el-header {
        line-height: 30px;
      }
      & .el-footer {
        line-height: 30px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 5px;
        & .el-button {
          margin: 0 10px;
        }
        & .tips {
          color: @c_999;
          font-size: @fs12;
        }
      }
      & .el-container {
        height: 100%;
        & .el-header {
          & /deep/ .el-icon-picture {
            background: url(../image/select_img.png) center no-repeat;
            background-size: 14px;
            margin-right: 8px;
          }
          & /deep/ .el-icon-picture:before{
              content: "替";
              font-size: 14px;
              visibility: hidden;
          }
        }
      }
    }
    & .el-aside {
      color: #333;
    }
    &.avatar {
      flex: none;
    }
  }
  & .dialog_reason {
    & /deep/ input::-webkit-input-placeholder { /* WebKit browsers */
      font-size: @fs14;
    }
    & .reason_s {
      padding-top: 10px;
      margin: 0 -10px;
      & .tag {
        background: @bg_color;
        padding: 5px 10px;
        color: @c_666;
        border-radius: 4px;
        margin: 0 10px 10px;
        display: inline-block;
        border:1px solid @bg_color;
        cursor: pointer;
      }
      & .tag:active {
        color: @c_text;
        background:rgba(0,198,146,0.1);
        border:1px solid @c_text;
      }
      & .tag.on {
        color: @c_text;
        background:rgba(0,198,146,0.1);
        border:1px solid @c_text;
      }
    }
  }
  .meet-item {
    margin-bottom: 15px;
    .el-form-item--mini.el-form-item {
      margin-bottom: 0;
    }
    .time-select {
      width: 100px;
    }
    .date-select{
      width: 150px;
    }
  }
  .meeting-dialog {
    /deep/ .el-dialog__header {
      text-align: center;
      font-weight: bold;
      font-size: 24px;
    }
    .meeting-info {
      padding-left: 30px;
      .inner-item {
        padding-left: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
      }
      h3 {
        // margin-block-start: 0;
        font-size: 16px;
      }
    }
  }
  .icon-refresh-w{
    float: right;
    margin-top: -50px;
  }
  .active-color {
    color: rgb(236, 128, 12);
  }
}
.form-option-drug {
  height: auto;
  display: flex;
  padding: 10px 15px;
  & .right {
    line-height: normal;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    & .name {
      font-size: @fs14;
      color: @c_333;
    }
    & .r-bottom {
      font-size: @fs12;
      color: @c_999;
    }
  }
  & .drug-image {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
}
.form-option-drug.hover {
  background-color: rgba(0, 198, 146, 0.1);
}

.submit-button-d{
  padding: 0 15px;
  height: 32px;
  font-size: 14px;
}

.tabUl{
  margin: 20px 0;
  width: 100%;
  display: flex;
  justify-content: center;
}
.tabli{
  width: 90px;
  height: 40px;
  background: #fff;
  text-align: center;
  line-height: 40px;
  border: 1px solid #eee;
}
.active {
  background: rgb(7, 200, 235);
  color: #fff;
}
.listUl{
  width: 100%;
  padding: 0 20px;
}
.listLi{
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  padding: 20px;
  background: #eee;
  margin-bottom: 20px;
}
.row{
  margin-top: 15px;
}
.wid{
}
.flexBox{
  display: flex;
  justify-content: space-between;
}
.span{
  margin-right: 30px;
}
.mask{
  width: 600px;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  background: #fff;;
  box-shadow: -2px 0px 12px 0px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: 0.3s ease-in-out;
  padding: 30px 20px;
  h2{
    font-size: 20px;
    color: #333;
    display: flex;
    justify-content: space-between;
    i{
      cursor: pointer;
    }
  }
}
.maskTran{
  display: block;
  transform: translateX(0);
}
#iframe_chat{
  width:100%;
  height: 93%;
}
