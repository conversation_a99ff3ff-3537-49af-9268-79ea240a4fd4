<template>
  <el-select
    v-model="selectValue"
    :placeholder="placeholder"
    :disabled="disabled"
    clearable
    @change="handleSelect"
  >
    <el-option v-for="item in dictData" :key="item.code" :label="item.value" :value="item.code" />
  </el-select>
</template>

<script>
import { getDict } from '@/api/system/dict'
export default {
  name: 'DictSelect',
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      selectValue: null,
      dictData: []
    }
  },
  watch: {
    value: function(val) {
      this.selectValue = this.value
    }
  },
  mounted() {
    this.getDict()
    this.selectValue = this.value
  },
  methods: {
    // 获取数据
    getDict() {
      getDict(this.type).then(response => {
        this.dictData = response
      })
    },
    handleSelect() {
      if (this.selectValue === '') {
        this.$emit('change', null)
      } else {
        this.$emit('change', this.selectValue)
      }
    }
  }
}
</script>
