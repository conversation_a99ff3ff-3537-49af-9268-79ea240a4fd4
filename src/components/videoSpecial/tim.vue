<template>
  <div id="uploadShow">
    <div style="height:70px;width:100%;overflow:hidden;">
      <div style="width:100%;display:flex;align-items:center;height:100%;">
        <div style="flex-direction:row;justify-content:center;align-item:center;">
          <el-upload
            class="avatar-uploader"
            :action="action"
            :headers="myHeaders"
            :data="updata"
            :show-file-list="false"
            :on-success="handleSuccess"
            accept="image/png, image/gif, image/jpeg, image/jpg"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div style="width:50px;height:20px;"></div>
        </div>
      </div>
    </div>
    <div style="width:100%;overflow:hidden;margin-top:10px;">
      <div style="display:flex;align-items:center;height:100%;overflow-y:inherit">
        <div v-for="(item, index) in imglist" :key="index" style="flex-direction:row;justify-content:center;align-item:center;margin-left:5px;">
          <el-image
            v-if="item.roomId == roomId"
            style="width:50px;height:50px;margin-right:5px;flex:none;"
            :src="item.path"
            fit
            @click="zoomIn(item.path)"
          />
          <div style="font-size:12px;width:50px;height:20px;text-align:center;line-height:20px;">{{ item.doctorName?item.doctorName:item.patientName }}</div>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" :modal-append-to-body="true" :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'
import { getToken } from '@/utils/auth'
import API from '@/api/doctor/index'
import { mapGetters } from 'vuex'
export default {
  name: 'Tim',
  data() {
    return {
      tim: null,
      myHeaders: { Authorization: getToken(), _o: '6', _p: '2', _w: '1' },
      action: process.env.VUE_APP_BASE_API + '/vc/video/consult/sendGroupMsg',
      key: '',
      // imglist: [],
      uploadImglist: [],
      sdkAppId: '',
      userSig: '',
      userId: '',
      videoConsultId: '',
      roomId: '',
      doctorId: '',
      updata: {
        roomId: '',
        isDoctorSend: '',
        userId: ''
      },
      patientName: '',
      doctorName: '',
      dialogImageUrl: '',
      dialogVisible: false

    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapGetters({ imglist: 'messages' })
  },
  watch: {
    // messages(oldval, newval) {
    // console.log(oldval, newval, 356)
    // }
  },
  mounted() {
    this.sdkAppId = sessionStorage.getItem('sdkAppId') * 1
    this.userSig = sessionStorage.getItem('userSig')
    this.userId = sessionStorage.getItem('userId')
    this.roomId = this.$route.query.roomId.toString()
    this.doctorId = sessionStorage.getItem('doctorId')
    this.videoConsultId = sessionStorage.getItem('videoConsultId')
    this.doctorName = this.$route.query.doctorName
    this.patientName = this.$route.query.patientName
    this.updata = {
      roomId: this.roomId,
      isDoctorSend: 1,
      userId: this.userId
    }
    this.getImgRecord()
    this.Init()
  },
  beforeDestroy() {
    this.logout()
  },
  methods: {
    //获取图片消息记录
    getImgRecord() {
      const params = {
        doctorId: this.doctorId,
        videoConsultId: this.videoConsultId
      }
      console.log(params, 661)
      API.accepts(params).then(response => {
        console.log(response, 660)
        const imgRecordArr = []
        for (const i in response) {
          if (response[i].type === 2) {
            const obj = JSON.parse(response[i].content)
            imgRecordArr.push(obj)
          }
        }
        sessionStorage.setItem('acceptsData', JSON.stringify(imgRecordArr))
      })
    },
    // 初始化方法tim
    Init() {
      this.key = sessionStorage.getItem('key')
      this.sdkAppId = sessionStorage.getItem('sdkAppId')
      this.userSig = sessionStorage.getItem('userSig')
      this.userId = sessionStorage.getItem('userId')
      const userId = this.userId.split('-')
      const param = {
        userId: userId[2],
        token: getToken()
      }
      API.getMqtt(param).then(response => {
        const params = {}
        const id = this.userInfo.id
        this.END_TIME = 30
        params.clientId = 'c_pc_' + id
        params.dnHost = response.dnHost
        params.cleanSession = response.cleanSession
        params.connectionTimeout = response.connectionTimeout
        params.privatePushTopicName = response.privatePushTopicName
        params.msgTopicName = response.msgTopicName
        params.willTopicName = response.willTopicName
        params.keepAliveInterval = response.keepAliveInterval
        params.sessionTimeout = response.sessionTimeout
        params.statusTopicName = response.statusTopicName
        console.log('initMqtt', params)
        this.$store.dispatch('mqtt/initMqtt', params)
        console.log('连接成功～')
      })
      const options = {
        SDKAppID: this.sdkAppId
      }
      const tim = TIM.create(options)
      this.tim = tim
      tim.setLogLevel(0)
      tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin })
      console.log(TIM.EVENT, '================TIM.EVENT===============', { userID: this.userId, userSig: this.userSig }, options)
      // tim.on(TIM.EVENT.SDK_READY, this._onIMReady)
      // tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.receiveMsg)
      tim.login({ userID: this.userId, userSig: this.userSig })
    },
    // 退出
    logout() {
      this.tim.logout()
    },
    // 接收和发送群组
    _onIMReady() {
      console.log('_onIMReady')
      this.tim.searchGroupByID(this.roomId).then(imResponse => {
        console.log(imResponse, '===========searchGroupByID============')
        this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
          console.log(imResponse, '===========joinGroup============')
        })
      }).catch(e => {
        console.log(e, '=========eeee==searchGroupByID============')
        this.tim.createGroup({
          groupID: this.roomId + '',
          name: this.roomId + '',
          type: TIM.TYPES.GRP_MEETING
        }).then(imResponse => {
          console.log(imResponse, '===========createGroup============')
          this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
            console.log(imResponse, '===========joinGroup============')
          })
        }).catch(e => {
          if (e.code === 10021) {
            console.log('群已存在，直接进群', e)
            this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
              console.log(imResponse, '===========joinGroup============')
            })
          }
        })
      })

    },
    // 图片上传
    handleSuccess(res, file) {
      if (res.code === 0) {
        this.$message.success('发送成功!')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片预览
    zoomIn(item) {
      this.dialogImageUrl = item
      this.dialogVisible = true
    }
  }

}
</script>
<style lang="less" scoped>
  .main {
    & .iframe_chat {
      width: 100%;
      height: 100vh;
    }
  }
  #detailBtn{
    display: none;
  }
  #uploadShow{
    display: block;
    width: 100%;
    padding: 10px 20px;
    background: #eee;
    position:fixed;left:0;bottom:0;
    z-index: 1000;
  }
  #uploadShow .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  #uploadShow .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  #uploadShow .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border: 1px dashed #d9d9d9;
  }
  #uploadShow .avatar-uploader-icon:hover {
    border-color: #409EFF;
  }
  #uploadShow .avatar {
    width: 70px;
    height: 70px;
    display: block;
  }
  #closeBtn{display: none;}
  #onLineRecord{display: none;}
  #offLineRecord{display: none;}
</style>
