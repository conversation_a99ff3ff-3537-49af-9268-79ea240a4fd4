<template>
  <div style="height:100%;background:#fff;align-items: flex-start;">
    <!-- 登录页面 -->
    <div id="video_setting">
      <div id="login-root">
        <!-- 登录卡片 -->
        <div id="login-card" class="card">
          <!-- 顶部三个蓝条 -->
          <div class="row-div topLine"><div v-for="index of 3" :key="index"></div></div>
          <!-- 腾讯云logo -->
          <div class="row-div head-title">视频问诊</div>
          <!-- 用户名 房间号 登录按钮-->
          <div class="col-div infoBox">
            <div class="form-group bmd-form-group is-filled">就诊人：<span id="inquirerName">{{ inquirerName }}</span></div>
            <div class="form-group bmd-form-group is-filled">性别：<span id="inquirerGender">{{ inquirerGender }}</span></div>
            <div class="form-group bmd-form-group is-filled">年龄：<span id="inquirerAgeStr">{{ inquirerAgeStr }}</span></div>
            <!-- 登录 -->
            <button id="login-btn" type="button" class="btn btn-raised btn-primary" @click="joinRoom()">进入诊室
              <!-- <div class="ripple-container"></div> -->
            </button>
            <!-- 摄像头 麦克风 -->
            <div class="row-div fotIcon">
              <img
                id="camera"
                src="/static/img/camera.png"
                onClick="event.cancelBubble = true"
                data-toggle="popover"
                data-placement="top"
                title=""
                data-content=""
              >
              <!-- 选择摄像头 -->
              <div id="camera-option" style="display: none"></div>
              <div style="width: 100px"></div>
              <img
                id="microphone"
                src="/static/img/mic.png"
                onClick="event.cancelBubble = true"
                data-toggle="popover"
                data-placement="top"
                title=""
                data-content=""
              >
              <!-- 选择麦克风 -->
              <div id="mic-option" style="display: none"></div>
            </div>
            <!-- 设备检测按钮 -->
            <div id="device-testing-btn" class="device-testing-btn" :style="{color:deviceTestingBtn}" @click="startDeviceConnect()">
              <div class="device-icon">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-device"></use>
                </svg>
              </div>
              设备检测
            </div>
            <div v-if="DCL" id="device-connect-list" class="device-connect-list">
              <div id="connect-camera" class="connect icon-normal" :style="{'color':hasCameraConnect?'green':'red'}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-cameraIcon"></use>
                </svg>
              </div>
              <div id="connect-voice" class="connect icon-normal" :style="{'color':hasVoiceConnect?'green':'red'}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-voice"></use>
                </svg>
              </div>
              <div id="connect-mic" class="connect icon-normal" :style="{'color':hasMicConnect?'green':'red'}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-microphone"></use>
                </svg>
              </div>
              <div id="connect-network" class="connect icon-normal" :style="{'color':hasNetworkConnect?'green':'red'}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-network"></use>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 设备检测界面弹窗 -->
      <div v-show="deviceTestingRoot" id="device-testing-root">
        <!-- 设备检测卡片 -->
        <div class="device-testing-card">
          <!-- 设备检测准备界面 -->
          <div v-show="deviceTestingPrepare" id="device-testing-prepare" class="device-testing-prepare">
            <div class="testing-title">设备连接</div>
            <div class="testing-prepare-info">设备检测前请务必给当前页面开放摄像头，麦克风权限哦~</div>
            <div ref="deviceDisplay" class="device-display">
              <div id="device-camera" class="device icon-normal" :class="{'connect-success':deviceClass.connectSuccess || cameraStatus === 1,'connect-fail':deviceClass.connectFail || cameraStatus===2}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-cameraIcon"></use>
                </svg>
              </div>
              <div id="device-voice" class="device icon-normal" :class="{'connect-success':deviceClass.connectSuccess || voiceStatus === 1 ,'connect-fail':deviceClass.hasVoiceConnect || voiceStatus === 2}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-voice"></use>
                </svg>
              </div>
              <div id="device-mic" class="device icon-normal" :class="{'connect-success':deviceClass.connectSuccess || micStatus === 1 ,'connect-fail':deviceClass.hasMicConnect || micStatus===2}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-microphone"></use>
                </svg>
              </div>
              <div id="device-network" class="device icon-normal" :class="{'connect-success':deviceClass.connectSuccess || networkStatus === 1,'connect-fail':deviceClass.hasNetworkConnect || networkStatus === 2}">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-network"></use>
                </svg>
              </div>
            </div>
            <div id="device-loading" ref="deviceLoading" class="loading-background">
              <div class="device-loading"></div>
            </div>
            <!-- 连接结果提示 -->
            <div class="connect-info">
              <!-- 连接结果 -->
              <div id="connect-info" ref="connectInfo"></div>
              <!-- 错误icon及错误解决指引 -->
              <div id="connect-attention-container" ref="CAC" class="connect-attention-container" style="display: none;">
                <div id="connect-attention-icon" class="connect-attention-icon">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-warn"></use>
                  </svg>
                </div>
                <div id="connect-attention-info" class="connect-attention-info" style="display: none;">
                  {{ errorMessage }}
                </div>
              </div>
            </div>
            <!-- 设备连接页面button -->
            <div class="testing-btn-display">
              <div v-if="!againBtn" id="start-test-btn" class="test-btn start-test " :class="[startGray?'start-gray':'']" @click="detection()">开始检测</div>
              <div v-if="againBtn" id="connect-again-btn" class="test-btn connect-again" @click="startDeviceConnect()">重新连接</div>
            </div>
          </div>
          <!-- 设备检测tab页 -->
          <div v-show="deviceTab" id="device-testing" class="device-testing">
            <div class="device-testing-title">
              <div id="camera-testing" class="icon-gray testing" :class="[iconActive>=1?'icon-blue complete':'']">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-cameraIcon"></use>
                </svg>
              </div>
              <div id="voice-testing" class="icon-gray testing" :class="[iconActive>=2?'icon-blue complete':'']">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-voice"></use>
                </svg>
              </div>
              <div id="mic-testing" class="icon-gray testing" :class="[iconActive>=3?'icon-blue complete':'']">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-microphone"></use>
                </svg>
              </div>
              <div id="network-testing" class="icon-gray testing" :class="[iconActive>=4?'icon-blue complete':'']">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-network"></use>
                </svg>
              </div>
            </div>
            <!-- 设备检测-摄像头检测 -->
            <div v-show="iconActive===1" id="camera-testing-body" class="testing-body" style="display: none;">
              <div class="device-list camera-device-list">
                <div class="select-title" style="display: block;">摄像头选择</div>
                <div class="select-list" style="display: block;">
                  <select id="camera-select" v-model="cameraSelected" name="select" class="device-select">
                    <option v-for="(item,i) in cameraList" :key="i" :value="item.deviceId">{{ item.label }}</option>
                  </select>
                </div>
              </div>
              <div v-if="iconActive===1" id="camera-video" class="camera-video"></div>
              <div class="testing-info-container">
                <div class="testing-info">是否可以清楚的看到自己？</div>
                <div class="button-list">
                  <div id="camera-fail" class="fail-button" @click="cameraStatusFun(false)">看不到</div>
                  <div id="camera-success" class="success-button" @click="cameraStatusFun(true)">可以看到</div>
                </div>
              </div>
            </div>
            <!-- 设备检测-播放器检测 -->
            <div v-show="iconActive===2" id="voice-testing-body" class="testing-body" style="display: none;">
              <div class="device-list camera-device-list">
                <div class="select-title" style="display: block;">扬声器选择</div>
                <div class="select-list" style="display: block;">
                  <select id="voice-select" v-model="voiceSelected" name="select" class="device-select" @change="voiceSelectFun">
                    <option v-for="(item,i) in voiceDevicesList" :key="i" :value="item.deviceId">{{ item.label }}</option>
                  </select>
                </div>
              </div>
              <div class="audio-control">
                <div class="audio-control-info">请调高设备音量, 点击播放下面的音频试试～</div>
                <audio
                  id="audio-player"
                  src="https://trtc-1252463788.cos.ap-guangzhou.myqcloud.com/web/assets/bgm-test.mp3"
                  controls
                ></audio>
              </div>
              <div class="testing-info-container">
                <div class="testing-info">是否可以听到声音？</div>
                <div class="button-list">
                  <div id="voice-fail" class="fail-button" @click="voiceStatusFun(false)">听不到</div>
                  <div id="voice-success" class="success-button" @click="voiceStatusFun(true)">可以听到</div>
                </div>
              </div>
            </div>
            <!-- 设备检测-麦克风检测 -->
            <div v-show="iconActive===3" id="mic-testing-body" class="testing-body" style="display: none;">
              <div class="device-list camera-device-list">
                <div class="select-title" style="display: block;">麦克风选择</div>
                <div class="select-list" style="display: block;">
                  <select id="mic-select" v-model="micSelected" name="select" class="device-select" @change="micSelectFun">
                    <option v-for="(item,i) in micList" :key="i" :value="item.deviceId">{{ item.label }}</option>
                  </select>
                </div>
              </div>
              <div class="mic-testing-container">
                <div class="mic-testing-info">对着麦克风说'哈喽'试试～</div>
                <div id="mic-bar-container" class="mic-bar-container">
                  <div v-for="item of 28" :key="item" class="mic-bar" :class="volume>=item?'active':''"></div>
                </div>
                <div id="audio-container"></div>
              </div>
              <div class="testing-info-container">
                <div class="testing-info">是否可以看到音量图标跳动？</div>
                <div class="button-list">
                  <div id="mic-fail" class="fail-button" @click="micStatusFun(false)">看不到</div>
                  <div id="mic-success" class="success-button" @click="micStatusFun(true)">可以看到</div>
                </div>
              </div>
            </div>
            <!-- 设备检测-硬件及网速检测 -->
            <div v-show="iconActive===4" id="network-testing-body" class="testing-body" style="display: none;">
              <div class="testing-index-list">
                <div class="testing-index-group">
                  <div class="testing-index">操作系统</div>
                  <div id="system"><div>{{ systemName }}</div></div>
                </div>
                <div class="testing-index-group">
                  <div class="testing-index">浏览器版本</div>
                  <div id="browser"><div>{{ browserName }}</div></div>
                </div>
                <div class="testing-index-group">
                  <div class="testing-index">屏幕共享能力</div>
                  <div id="screen-share">{{ screenShare }}</div>
                </div>
                <div class="testing-index-group">
                  <div class="testing-index">网络质量</div>
                  <div id="uplink-network" :class="[networkLoading?'network-loading':'']">{{ networkText }}</div>
                </div>
              </div>
              <div class="testing-footer">
                <div v-show="reportBtn" id="testing-report-btn" class="test-btn" @click="showTestingReport">查看检测报告</div>
              </div>
            </div>
          </div>
          <!-- 设备检测报告 -->
          <div v-show="devTestReport" id="device-testing-report" class="device-testing-report" style="display: none;">
            <div class="testing-title">检测报告</div>
            <!-- 检测报告内容 -->
            <div class="device-report-list">
              <!-- 摄像头报告信息 -->
              <div class="device-report camera-report">
                <div class="device-info">
                  <div class="report-icon">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-cameraIcon"></use>
                    </svg>
                  </div>
                  <div v-if="cameraTestingResult.device" id="camera-name" class="device-name">{{ cameraTestingResult.device.label }}</div>
                </div>
                <div id="camera-testing-result" class="camera-testing-result" :style="{'color':cameraTestingResult.statusResult?'green':'red'}">{{ cameraTestingResult.statusResult?'正常':'异常' }}</div>
              </div>
              <!-- 扬声器报告信息 -->
              <div v-if="!noVoiceDevice" id="voice-report" class="device-report voice-report">
                <div class="device-info">
                  <div class="report-icon">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-voice"></use>
                    </svg>
                  </div>
                  <div v-if="voiceTestingResult.device" id="voice-name" class="device-name">{{ voiceTestingResult.device.label }}</div>
                </div>
                <div id="voice-testing-result" class="voice-testing-result" :style="{'color':voiceTestingResult.statusResult?'green':'red'}">{{ voiceTestingResult.statusResult?'正常':'异常' }}</div>
              </div>
              <!-- 麦克风报告信息 -->
              <div class="device-report mic-report">
                <div class="device-info">
                  <div class="report-icon">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-microphone"></use>
                    </svg>
                  </div>
                  <div v-if="micTestingResult.device" id="mic-name" class="device-name">{{ micTestingResult.device.label }}</div>
                </div>
                <div id="mic-testing-result" class="mic-testing-result" :style="{'color':voiceTestingResult.statusResult?'green':'red'}">{{ voiceTestingResult.statusResult?'正常':'异常' }}</div>
              </div>
              <!-- 网络报告信息 -->
              <div class="device-report network-report">
                <div class="device-info">
                  <div class="report-icon">
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-network"></use>
                    </svg>
                  </div>
                  <div id="network-name" class="device-name">网络质量</div>
                </div>
                <div id="network-testing-result" class="network-testing-result" :style="{'color':Number(networkTestingResult.upLinkNetwork) > 3 ? 'red' : 'green'}">{{ NETWORK_QUALITY[String(networkTestingResult.upLinkNetwork)] }}</div>
              </div>
            </div>
            <div class="device-report-footer">
              <div id="testing-again" class="device-report-btn testing-agin" @click="testAgain">重新检测</div>
              <div id="testing-finish" class="device-report-btn testing-finish" @click="testFinish">完成检测</div>
            </div>
          </div>
          <!-- 设备检测关闭按钮 -->
          <div id="device-testing-close-btn" class="device-testing-close-btn" @click="testFinish">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-closeIcon"></use>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import '@/assets/js/iconfont.js'
import { getOS, getBrowser } from '@/utils/isMobile.js'
import TRTC from 'trtc-js-sdk'
export default {
  data() {
    return {
      hasCameraDevice: false,
      hasMicDevice: false,
      hasVoiceDevice: false,
      hasCameraConnect: '',
      hasVoiceConnect: '',
      hasMicConnect: '',
      hasNetworkConnect: '',
      isFileProtocol: '',
      noVoiceDevice: '',
      deviceTestingRoot: false,
      deviceTestingPrepare: false,
      curTestingPageId: '',
      iconActive: 0,
      deviceTestingBtn: '',
      deviceClass: {
        connectSuccess: false,
        connectFail: false
      },
      cameraStatus: '',
      voiceStatus: '',
      micStatus: '',
      networkStatus: '',
      againBtn: false,
      startGray: true,
      errorMessage: '',
      DCL: false,
      timeout: null,
      completedTestingPageIdList: [],
      deviceTab: false,
      cameraTestingResult: {},
      voiceTestingResult: {},
      micTestingResult: {},
      networkTestingResult: {},
      localStream: null,
      client: null,
      networkQualityNum: 0,
      cameraList: [], //摄像头列表
      cameraSelected: '', //默认选择的摄像头
      // testNumber: 0, //设备监听tab 控制音频 摄像头 麦克风 网络
      voiceDevicesList: [], //扬声器列表
      voiceSelected: '', //默认选择的扬声器
      micList: [], //麦克风列表
      micSelected: '', //麦克风默认选择
      volume: 0, //音量高亮效果
      volumeTimer: null, //监听音量变化定时器
      reportBtn: true, //检测报告按钮,
      networkLoading: true, //网络检测>>网络质量loading
      networkText: '', //网络检测>>网路质量文本
      systemName: '', //操作系统
      browserName: '', //浏览器版本
      screenShare: '', //是否支持屏幕共享
      trtcConfig: {
        sdkAppId: null,
        userSig: null,
        userId: null,
        roomId: null
      },
      NETWORK_QUALITY: {
        '0': '未知',
        '1': '极佳',
        '2': '较好',
        '3': '一般',
        '4': '差',
        '5': '极差',
        '6': '断开'
      },
      devTestReport: false, //检测结果
      inquirerAgeStr: '',
      inquirerGender: '',
      inquirerName: ''
    }
  },
  async mounted() {
    this.trtcConfig.sdkAppId = sessionStorage.getItem('sdkAppId') * 1
    this.trtcConfig.userSig = sessionStorage.getItem('userSig')
    this.trtcConfig.userId = sessionStorage.getItem('userId')
    this.inquirerAgeStr = sessionStorage.getItem('inquirerAgeStr')
    this.inquirerGender = sessionStorage.getItem('inquirerGender')
    this.inquirerName = sessionStorage.getItem('inquirerName')
    this.trtcConfig.roomId = this.$route.query.roomId
    this.isFileProtocol = location.protocol === 'file:'
    this.isSafari()
    await this.getDevicesInfo()
    this.deviceDialogInit()
  },
  methods: {
    // 加入诊室
    joinRoom() {
      this.$emit('changeType', 2)
    },
    // 初始化设备信息弹窗提示
    deviceDialogInit() {
      if (!localStorage.getItem('txy_device_testing')) {
        localStorage.setItem('txy_device_testing', Date.now())
        this.startDeviceConnect()
      } else {
        // 在首页展示设备连接结果
        this.showDeviceStatus()
        if (!(this.hasCameraConnect && this.hasMicConnect)) {
          navigator.mediaDevices
            .getUserMedia({ video: this.hasCameraDevice, audio: this.hasMicDevice })
            .then(() => {
              if (this.hasCameraDevice) this.hasCameraConnect = true
              if (this.hasMicDevice) this.hasMicConnect = true
              // 更新首页popover的option list
              // getDevicesList()
              // 展示连接结果
              this.showDeviceStatus()
            })
            .catch(err => {
              console.log('getUserMedia err', err.name, err.message)
              this.handleGetUserMediaError(err)
            })
        }
      }
    },
    // 首页展示设备链接结果
    showDeviceStatus() {
      var that = this
      that.DCL = true
      that.timeout = setTimeout(() => {
        that.DCL = false
      }, 3000)
      if (!(that.hasCameraConnect && that.hasVoiceConnect && that.hasMicConnect && that.hasNetworkConnect)) {
        that.deviceTestingBtn = 'red'
      } else {
        that.deviceTestingBtn = 'green'
      }
    },
    // 判断是否有网络
    isOnline() {
      return new Promise(resolve => {
        try {
          const xhr = new XMLHttpRequest()
          xhr.onload = function() {
            resolve(true)
          }
          xhr.onerror = function() {
            resolve(false)
          }
          xhr.open('GET', '/TRTC/data/mock.json', true)
          xhr.send()
        } catch (err) {
          // console.log(err);
        }
      })
    },
    // 判断是否是safari浏览器
    isSafari() {
      const isSafari =
        /Safari/.test(navigator.userAgent) &&
        !/Chrome/.test(navigator.userAgent) &&
        !/CriOS/.test(navigator.userAgent) &&
        !/FxiOS/.test(navigator.userAgent) &&
        !/EdgiOS/.test(navigator.userAgent)
      const isFirefox = /Firefox/i.test(navigator.userAgent)
      this.noVoiceDevice = isSafari || isFirefox
    },
    async startDeviceConnect() {
      var that = this
      // 重置class属性 用于图标样式的显示
      that.deviceClass = { connectSuccess: true, connectFail: true }
      that.cameraStatus = ''
      that.voiceStatus = ''
      that.micStatus = ''
      that.networkStatus = ''
      that.$refs.deviceLoading.style.display = 'none'
      // ***结束重置***
      await this.isSafari()
      await this.getDevicesInfo()
      // 显示设备检测弹窗
      this.deviceTestingRoot = true
      this.deviceTestingPrepare = true
      this.curTestingPageId = 'device-testing-prepare'
      // this.initTestingTabTitle() //暂时没发现有啥用，后续优化删除
      // 在设备检测弹窗显示设备连接信息
      const showDeviceConnectInfo = function() {
        if (!(that.hasCameraConnect && that.hasVoiceConnect && that.hasMicConnect && that.hasNetworkConnect)) {
          that.deviceTestingBtn = 'red'
        } else {
          that.deviceTestingBtn = 'green'
        }
        // 隐藏设备连接失败提示
        that.$refs.CAC.style.display = 'none'
        // 设备连接中
        that.$refs.deviceLoading.style.display = 'block'
        that.$refs.connectInfo.style.color = '#cccccc'
        that.$refs.connectInfo.innerHTML = '设备正在连接中，请稍等'
        that.deviceClass = {
          connectSuccess: false,
          connectFail: false
        }
        that.againBtn = false
        that.startGray = true
        // 设备连接结束，展示连接结果
        setTimeout(() => {
          that.$refs.deviceLoading.style.display = 'none'
          that.deviceDisplay = true
          that.deviceClass = { connectSuccess: false, connectFail: false }
          that.cameraStatus = that.hasCameraConnect ? 1 : 2
          that.voiceStatus = that.hasVoiceConnect ? 1 : 2
          that.micStatus = that.hasMicConnect ? 1 : 2
          that.networkStatus = that.hasNetworkConnect ? 1 : 2
          let connectInfo = ''
          // 设备检测结果（包括麦克风检测，摄像头检测，扬声器检测，网络检测）
          const connectResult = that.hasCameraConnect && that.hasVoiceConnect && that.hasMicConnect && that.hasNetworkConnect
          if (connectResult) {
            console.log('设备链接成功')
            that.$refs.connectInfo.innerHTML = '设备及网络连接成功，请开始设备检测'
            that.$refs.connectInfo.style.color = '#32CD32'
            that.againBtn = false
            that.startGray = false
          } else {
            console.log('设备链接不成功')
            // 有设备或者网络连接不成功，展示连接失败提示
            connectInfo = that.getDeviceConnectInfo()
            that.$refs.connectInfo.innerHTML = connectInfo
            that.$refs.connectInfo.style.color = 'red'
            // 切换按钮状态
            that.againBtn = true
          }
        }, 2000)
      }
      showDeviceConnectInfo()
      // 如果有设备未连接，唤起请求弹窗
      if (!(this.hasCameraConnect && this.hasMicConnect)) {
        navigator.mediaDevices
          .getUserMedia({ video: this.hasCameraDevice, audio: this.hasMicDevice })
          .then(() => {
            if (this.hasCameraDevice) this.hasCameraConnect = true
            if (this.hasMicDevice) this.hasMicConnect = true
            // 更新首页popover的option list
            // getDevicesList()
            // 显示设备连接信息
            showDeviceConnectInfo()
          })
          .catch(err => {
            console.log('getUserMedia err', err.name, err.message)
            this.handleGetUserMediaError(err)
          })
      }
    },
    async getDevicesInfo() {
      const cameraList = await TRTC.getCameras()
      const micList = await TRTC.getMicrophones()
      const voiceList = await TRTC.getSpeakers()
      this.hasCameraDevice = cameraList.length > 0
      this.hasMicDevice = micList.length > 0
      this.hasVoiceDevice = voiceList.length > 0
      cameraList.forEach(camera => {
        if (camera.deviceId.length > 0) {
          this.hasCameraConnect = true
        }
      })
      micList.forEach(mic => {
        if (mic.deviceId.length > 0) {
          this.hasMicConnect = true
        }
      })
      // 如果是无法进行扬声器检测的浏览器，设置为true
      if (this.noVoiceDevice) {
        this.hasVoiceDevice = true
        this.hasVoiceConnect = true
      } else {
        this.hasVoiceConnect = voiceList.length > 0
      }
      // 本地打开使用 navigator.onLine 的结果，https打开使用 isOnline() 的检测结果
      // CORS policy: Cross origin requests are only supported for protocol schemes: http, data, chrome, chrome-extension, chrome-untrusted, https;
      this.hasNetworkConnect = this.isFileProtocol ? navigator.onLine : await this.isOnline()
    },
    getDeviceConnectInfo() {
      var that = this
      const deviceFailAttention =
        '1. 若浏览器弹出提示，请选择“允许”<br>' +
        '2. 若杀毒软件弹出提示，请选择“允许”<br>' +
        '3. 检查系统设置，允许浏览器访问摄像头及麦克风<br>' +
        '4. 检查浏览器设置，允许网页访问摄像头及麦克风<br>' +
        '5. 检查摄像头/麦克风是否正确连接并开启<br>' +
        '6. 尝试重新连接摄像头/麦克风<br>' +
        '7. 尝试重启设备后重新检测'
      const networkFailAttention =
        '1. 请检查设备是否联网<br>' + '2. 请刷新网页后再次检测<br>' + '3. 请尝试更换网络后再次检测'
      let connectInfo = '连接出错，请重试'
      // 第一步：浏览器未检测到摄像头/麦克风/扬声器设备的提示
      if (!(that.hasCameraDevice && that.hasMicDevice && that.hasVoiceDevice)) {
        connectInfo = `未检测到${that.hasCameraDevice ? '' : '【摄像头】'}${
          that.hasVoiceDevice ? '' : '【扬声器】'
        }${that.hasMicDevice ? '' : '【麦克风】'}设备，请检查设备连接`
        return connectInfo
      }
      // 第二步：浏览器未拿到摄像头/麦克风权限的提示
      if (!(that.hasCameraConnect && that.hasMicConnect)) {
        connectInfo = that.hasNetworkConnect
          ? '请允许浏览器及网页访问摄像头/麦克风设备'
          : '请允许浏览器及网页访问摄像头/麦克风设备，并检查网络连接'
        // 显示设备连接失败引导
        that.$refs.CAC.style.display = 'block'
        that.errorMessage = deviceFailAttention
        return connectInfo
      }
      // 第三步：浏览器检测未连接网络的提示
      if (!that.hasNetworkConnect) {
        connectInfo = '网络连接失败，请检查网络连接'
        // 显示设备连接失败引导
        that.$refs.CAC.style.display = 'block'
        that.errorMessage = networkFailAttention
        return connectInfo
      }
      return connectInfo
    },
    // 处理getUserMedia的错误
    handleGetUserMediaError(error) {
      switch (error.name) {
        case 'NotReadableError':
          // 当系统或浏览器异常的时候，可能会出现此错误，您可能需要引导用户重启电脑/浏览器来尝试恢复。
          alert(
            '暂时无法访问摄像头/麦克风，请确保系统授予当前浏览器摄像头/麦克风权限，并且没有其他应用占用摄像头/麦克风'
          )
          return
        case 'NotAllowedError':
          alert('用户/系统已拒绝授权访问摄像头或麦克风')
          return
        case 'NotFoundError':
          // 找不到摄像头或麦克风设备
          alert('找不到摄像头或麦克风设备')
          return
        case 'OverConstrainedError':
          alert(
            '采集属性设置错误，如果您指定了 cameraId/microphoneId，请确保它们是一个有效的非空字符串'
          )
          return
        default:
          alert('初始化本地流时遇到未知错误, 请重试')
          return
      }
    },
    // 开始检测设备信息
    detection() {
      if (this.startGray) return
      this.deviceTestingPrepare = false
      this.deviceTab = true
      this.iconActive = 0
      this.startCameraTesting()
    },
    /*** 抽离createStream的公共处理函数*/
    async createLocalStream(constraints, container) {
      this.localStream = TRTC.createStream(constraints)
      try {
        await this.localStream.initialize()
      } catch (error) {
        this.handleGetUserMediaError(error)
      }
      container && this.localStream.play(container)
    },
    /*** 摄像头设备测试*/
    async startCameraTesting() {
      this.curTestingPageId = 'camera-testing-body'
      this.iconActive = 1
      this.completedTestingPageIdList.push('camera-testing-body')
      this.completedTestingPageIdList = [...new Set(this.completedTestingPageIdList)]
      await this.updateCameraDeviceList()
      // 创建本地视频流
      await this.createLocalStream(
        {
          audio: false,
          video: true,
          cameraId: this.cameraTestingResult.device.deviceId
        },
        'camera-video'
      )
    },
    /**摄像头检测页-检测展示摄像头设备选择列表*/
    async  updateCameraDeviceList() {
      const cameraDevices = await TRTC.getCameras()
      this.cameraList = cameraDevices
      // 如果有用户设备选择缓存，优先使用缓存的deviceId
      const cacheCameraDevice = cameraDevices.filter(
        camera => camera.deviceId === localStorage.getItem('txy_webRTC_cameraId')
      )
      if (cacheCameraDevice.length > 0) {
        this.cameraSelected = localStorage.getItem('txy_webRTC_cameraId')
        this.cameraTestingResult.device = cacheCameraDevice[0]
      } else {
        this.cameraSelected = cameraDevices[0].deviceId
        this.cameraTestingResult.device = cameraDevices[0]
      }
    },
    // 摄像头看到or看不到
    cameraStatusFun(type) {
      this.cameraTestingResult.statusResult = type
      this.localStream.close()
      // safari和firefox浏览器跳过扬声器检测
      this.noVoiceDevice ? this.startMicTesting() : this.startVoiceTesting()
    },
    // 播放器设备测试
    async startVoiceTesting() {
      // this.testNumber = 2
      this.curTestingPageId = 'voice-testing-body'
      this.iconActive = 2
      this.completedTestingPageIdList.push('voice-testing-body')
      this.completedTestingPageIdList = [...new Set(this.completedTestingPageIdList)]
      await this.updateVoiceDeviceList()
    },
    //初始化更新扬声器设备组
    async updateVoiceDeviceList() {
      // 获取扬声器设备并展示在界面中
      const voiceDevices = await TRTC.getSpeakers()
      this.voiceDevicesList = voiceDevices
      // 如果有用户设备选择缓存，优先使用缓存的deviceId
      const cacheVoiceDevice = voiceDevices.filter(
        mic => mic.deviceId === localStorage.getItem('txy_webRTC_voiceId')
      )
      if (cacheVoiceDevice.length > 0) {
        this.voiceSelected = localStorage.getItem('txy_webRTC_voiceId')
        this.voiceTestingResult.device = cacheVoiceDevice[0]
      } else {
        this.voiceSelected = voiceDevices[0].deviceId
        this.voiceTestingResult.device = voiceDevices[0]
      }
    },
    // 扬声器是否能听到声音
    voiceStatusFun(type) {
      this.voiceTestingResult.statusResult = type
      const audioPlayer = document.querySelector('#audio-player')
      if (!audioPlayer.paused) {
        audioPlayer.pause()
      }
      this.startMicTesting()
    },
    // 切换扬声器设备
    async voiceSelectFun() {
      const newVoiceId = this.voiceSelected
      localStorage.setItem('txy_webRTC_voiceId', newVoiceId)
      var label = this.voiceDevicesList.filter((item) => item.deviceId === newVoiceId).label
      this.voiceTestingResult.device = {
        label: label,
        deviceId: newVoiceId,
        kind: 'audiooutput'
      }
      const audioPlayer = document.querySelector('#audio-player')
      await audioPlayer.setSinkId(newVoiceId)
    },
    // 麦克风声音测试
    async  startMicTesting() {
      this.curTestingPageId = 'mic-testing-body'
      this.iconActive = 3
      this.completedTestingPageIdList.push('mic-testing-body')
      this.completedTestingPageIdList = [...new Set(this.completedTestingPageIdList)]
      await this.updateMicDeviceList()
      // 创建本地音频流
      await this.createLocalStream(
        {
          audio: true,
          microphoneId: this.micTestingResult.device.deviceId,
          video: false
        },
        'audio-container'
      )

      // 监听音量，并量化显示出来
      this.volumeTimer = setInterval(() => {
        const volume = this.localStream.getAudioLevel()
        const num = Math.ceil(28 * volume)
        for (let i = 0; i < num; i++) {
          this.volume = i
        }
      }, 100)
    },
    async  updateMicDeviceList() {
      // 展示麦克风设备选择
      const micDevices = await TRTC.getMicrophones()
      // 暂时不用 ，如果需要在TRTC中的common.js中引入该方法
      // const isAndroid = getOS().type === 'mobile' && getOS().osName === 'Android'
      // // 如果是安卓设备，不允许切换麦克风(切换麦克风存在获取不到音量的情况)
      // if (isAndroid) {
      //   micDevices = [].concat(micDevices[0])
      // }
      this.micList = micDevices
      // 如果有用户设备选择缓存，优先使用缓存的deviceId
      const cacheMicDevice = micDevices.filter(
        mic => mic.deviceId === localStorage.getItem('txy_webRTC_micId')
      )
      // isAndroid 判断是下一行代码的 || 判断暂时不用
      if (cacheMicDevice.length === 0) {
        this.micSelected = micDevices[0].deviceId
        this.micTestingResult.device = micDevices[0]
      } else {
        this.micSelected = localStorage.getItem('txy_webRTC_micId')
        this.micTestingResult.device = cacheMicDevice[0]
      }
    },
    // 切换麦克风设备
    async micSelectFun() {
      const newMicID = this.micSelected
      localStorage.setItem('txy_webRTC_micId', newMicID)
      var label = this.voiceDevicesList.filter((item) => item.deviceId === newMicID).label
      this.micTestingResult.device = {
        label: label,
        deviceId: newMicID,
        kind: 'audioinput'
      }
      await this.localStream.switchDevice('audio', newMicID)
    },
    // 麦克风是否可用
    micStatusFun(type) {
      this.micTestingResult.statusResult = type
      this.localStream.close()
      this.startNetworkTesting()
    },
    // 网络是否可用
    async startNetworkTesting() {
      this.iconActive = 4
      this.reportBtn = false
      this.curTestingPageId = 'network-testing-body'
      this.completedTestingPageIdList.push('network-testing-body')
      this.completedTestingPageIdList = [...new Set(this.completedTestingPageIdList)]
      this.networkQualityNum = 0
      this.networkLoading = true
      this.networkText = ''
      this.systemName = getOS().osName
      this.browserName = getBrowser().browser + '-' + getBrowser().version
      this.screenShare = TRTC.isScreenShareSupported() ? '支持' : '不支持'
      this.client = TRTC.createClient({ mode: 'rtc', ...this.trtcConfig })
      this.client.on('network-quality', event => {
        this.networkQualityNum++
        // 收到3次'network-quality'事件的时候认为拿到了网络实际质量
        if (this.networkQualityNum === 3) {
          this.networkTestingResult.upLinkNetwork = event.uplinkNetworkQuality
          this.networkTestingResult.downLinkNetwork = event.downlinkNetworkQuality
          this.networkLoading = false
          this.networkText = this.NETWORK_QUALITY[String(this.networkTestingResult.upLinkNetwork)]
          this.reportBtn = true
          this.client && this.client.leave()
          this.client && this.client.off('network-quality')
        }
      })
      await this.client.join({
        roomId: parseInt(this.trtcConfig.roomId)
      })
      await this.createLocalStream(
        {
          audio: true,
          video: false
        },
        'audio-container'
      )
      await this.client.publish(this.localStream)
      // 音频轨道静音
      this.localStream.muteAudio()
    },
    //查看检测报告
    showTestingReport() {
      this.deviceTab = false
      this.devTestReport = true
      this.curTestingPageId = 'device-testing-report'
    },
    // 重新检测
    testAgain() {
      this.devTestReport = false
      this.startDeviceConnect()
      this.completedTestingPageIdList = []
    },
    // 检测完成
    testFinish() {
      this.deviceTestingRoot = false
      this.deviceTab = false
      this.devTestReport = false
      // $(`#${curTestingPageId}`).hide()
      this.curTestingPageId = ''
      this.completedTestingPageIdList = []
      // 停止摄像头/麦克风的流采集并释放摄像头/麦克风设备
      this.localStream && this.localStream.close()
      this.client && this.client.leave()
      this.client && this.client.off('network-quality')
      // 停止播放器的音乐
      const audioPlayer = document.querySelector('#audio-player')
      if (!audioPlayer.paused) {
        audioPlayer.pause()
      }
      audioPlayer.currentTime = 0
    }
  }

}
</script>
<style src="@/assets/css/index.css"  scoped></style>
<style src="@/assets/css/room.css"  scoped></style>
<style src="@/assets/css/bootstrap-material-design.min.css"  scoped></style>
<style scoped>
#connect-info{
  max-width: 60%;
}
.fotIcon{
  width: 100%; height: 105px; justify-content: center
}
#login-btn{
  margin-top: 24px !important;
  width: 100%;
  height: 40px;
}
  .infoBox{
    width: 320px;
  }
  .infoBox .form-group{
    width: 100%;
    height: 50px;
    font-size: 18px;
  }
  #video_setting{
    margin: 0 auto;
    margin-top: 50px;
  }
  .mic-bar{
    width: 10px ;
    height: 30px ;
    border: 1px solid #cccccc ;
    border-radius: 1px ;
  }
  .device-name{
    line-height: 24px;
  }
  .topLine{  width: 100%;  height: 10px;
  }
  .topLine div{ width: 33.333%;height: 100%;}
  .topLine div:nth-child(1){
    background:rgb(0, 110, 255) ;
  }
  .topLine div:nth-child(2){
    background:rgb(0, 164, 255) ;
  }
  .topLine div:nth-child(3){
    background:rgb(90, 213, 224) ;
  }
  .head-title{width: 100%;height: 100px;line-height: 100px;font-size: 23px;justify-content: center}
</style>
