<template>
  <div>
    <el-button id="detailBtn" style="position:fixed;bottom:50px;right:42%;z-index:1001;" @click="goDetail">查看病例/病情</el-button>
    <!-- 查看病例/病情 -->
    <el-dialog :visible.sync="detailsVisible" :title="title" width="70%">
      <!-- 病情 -->
      <div v-show="typeShow1">
        <div>
          <h4>基本信息</h4>
          <div style="width:100%;background:#F9F9F9;padding:10px 0 10px 20px;">
            <p>姓名：{{ detail.patientName }}</p>
            <p>性别：{{ detail.patientGender == 0 ? '女' : '男' }}</p>
            <p>年龄：{{ detail.patientAgeStr }}</p>
            <p>就诊医院：{{ detail.offlineHospital }}</p>
            <p>就诊科室：{{ detail.offlineDepartment }}</p>
            <p>就诊医生：{{ detail.offlineDoctor }}</p>
            <p>就诊诊断：{{ detail.offlineDiagnosis }}</p>
          </div>
        </div>
        <div>
          <h4>线下病历/处方</h4>
          <div v-if="offlineERImgs !== null" style="display:flex;">
            <div v-for="(item, index) in offlineERImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>PDF文件</h4>
          <div v-if="offlineERPdfs !== null">
            <div v-for="(item, index) in offlineERPdfs" :key="index">
              <!-- <div @click="opendPdf(item)" style="margin-bottom:5px;cursor:pointer">{{item.name}}</div> -->
              <el-link :href="item.filepath" target="_blank">{{ item.name }}</el-link>
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>检查图片</h4>
          <div v-if="offlineDiagnosisImgs !== null" style="display:flex;">
            <div v-for="(item, index) in offlineDiagnosisImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>病情主诉</h4>
          <el-form>
            <el-form-item label="">{{ detail.description ? detail.description : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>持续时长</h4>
          <el-form>
            <el-form-item label="">{{ detail.diseaseCycle ? detail.diseaseCycle : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>患处图片</h4>
          <div v-if="descriptionImgs !== null" style="display:flex;">
            <div v-for="(item, index) in descriptionImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>服用药物</h4>
          <el-form>
            <el-form-item label="">{{ detail.drugs ? detail.drugs : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>药盒照片</h4>
          <div v-if="drugsImgs !== null" style="display:flex;">
            <div v-for="(item, index) in drugsImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>过敏史</h4>
          <el-form>
            <el-form-item label="">{{ detail.allergy ? detail.allergy : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>既往史</h4>
          <el-form>
            <el-form-item label="">{{ detail.pastHistory ? detail.pastHistory : '无' }}</el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 病历 -->
      <div v-show="typeShow2">
        <div>
          <h4>基本信息</h4>
          <div style="display:flex;justify-content:space-between;background:#F9F9F9;">
            <div style="background:#F9F9F9;padding:10px 0 10px 20px;">
              <p>姓名：{{ casedetail.name }}</p>
              <p>性别：{{ casedetail.gender == 0 ? '女' : '男' }}</p>
              <p>年龄：{{ casedetail.ageStr }}</p>
              <p>科室：{{ casedetail.department }}</p>
              <p>时间：{{ signInfo.signTime }}</p>
            </div>
            <img src="/logo/ic_seal.png" style="width:150px;height:150px;" />
          </div>
        </div>
        <div>
          <h4>主诉</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.mainComplaint ? casedetail.mainComplaint : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>现病史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.presentDisease ? casedetail.presentDisease : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>既往史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.pastHistory ? casedetail.pastHistory : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>过敏史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.allergy ? casedetail.allergy : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>家庭史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.pastFamily ? casedetail.pastFamily : '无' }}</el-form-item>
          </el-form>
        </div>
        <div v-if="casedetail.gender == 0">
          <h4>月经史</h4>
          <el-form>
            <div style="margin-bottom:6px;">月经情况：{{ menstrual.status ? menstrual.status : '无' }}</div>
            <div style="margin-bottom:6px;">初潮年龄：{{ menstrual.firstAge ? menstrual.firstAge+'岁' : '无' }}</div>
            <div style="margin-bottom:6px;">月经周期：{{ menstrual.cycle ? menstrual.cycle+'天' : '无' }}</div>
            <div style="margin-bottom:6px;">行经天数：{{ menstrual.processDays ? menstrual.processDays+'天' : '无' }}</div>
            <div style="margin-bottom:6px;">是否痛经：{{ menstrual.dysmenorrhea == 1 ? '是' : '否' }}</div>
            <div v-if="menstrual.dysmenorrhea == 1" style="margin-bottom:6px;">痛经部位：{{ menstrual.part }}</div>
          </el-form>
        </div>
        <div>
          <h4>检查指标</h4>
          <el-form>
            <div style="margin-bottom:6px;">体温：{{ casedetail.temperature ? casedetail.temperature+'度' : '无' }}</div>
            <div style="margin-bottom:6px;">体重：{{ casedetail.weight ? casedetail.weight+'kg' : '无' }}</div>
            <div style="margin-bottom:6px;">心率：{{ casedetail.heartRete ? casedetail.heartRete+'bpm' : '无' }}</div>
            <div style="margin-bottom:6px;">收缩压：{{ casedetail.systolic ? casedetail.systolic+'mmHg' : '无' }}</div>
            <div style="margin-bottom:6px;">舒张压：{{ casedetail.diastole ? casedetail.diastole+'mmHg' : '无' }}</div>
            <div style="margin-bottom:6px;">阳性体征：{{ casedetail.positiveSigns ? casedetail.positiveSigns : '无' }}</div>
            <div style="margin-bottom:6px;">必要的阴性体征：{{ casedetail.negativeSigns ? casedetail.negativeSigns : '无' }}</div>
            <div style="margin-bottom:6px;">更多检查结果：{{ casedetail.moreExamin ? casedetail.moreExamin : '无' }}</div>
          </el-form>
        </div>
        <div>
          <h4>诊断</h4>
          <div v-for="(item, index) in casedetail.diagnosisList" :key="index">
            <div style="margin-bottom:5px;">{{ item }}</div>
          </div>
        </div>
        <div>
          <h4>治疗意见</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.treatmentOptions ? casedetail.treatmentOptions : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>其他</h4>
          <div v-if="casedetailImgs !== null" style="display:flex;">
            <div v-for="(item, index) in casedetailImgs" :key="index">
              <img :src="item.imgUrl" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item.imgUrl)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>医生</h4>
          <el-form>
            <el-form-item label="">{{ signInfo.doctorName ? signInfo.doctorName : '无' }}</el-form-item>
            <img :src="signInfo.sealImage" style="width:100px;" />
          </el-form>
        </div>
        <div>
          <h4>医院</h4>
          <el-form>
            <el-form-item label="">{{ signInfo.hospitalName ? signInfo.hospitalName : '无' }}</el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import API from '@/api/doctor/index'
export default {
  data() {
    return {
      // ***
      detailsVisible: false,
      typeShow1: false,
      typeShow2: false,
      caseId: null,
      diseaseId: null,
      title: '病历详情',
      detail: {},
      casedetail: {},
      menstrual: {},
      signInfo: {},
      offlineERImgs: null,
      offlineERPdfs: null,
      offlineDiagnosisImgs: null,
      descriptionImgs: null,
      drugsImgs: null,
      casedetailImgs: null
    }
  },
  mounted() {
    this.caseId = sessionStorage.getItem('caseId')
    this.diseaseId = sessionStorage.getItem('diseaseId')
  },
  methods: {
    // 视频问诊页面查看详情
    goDetail() {
      this.detailsVisible = true
      console.log(this.diseaseId)
      if (this.diseaseId) {
        this.typeShow1 = true
        this.typeShow2 = false
        this.title = '病情详情'
        const params = {
          diseaseId: this.diseaseId
        }
        API.getDiseaseDetail(params).then(res => {
          this.detail = res
          if (res.offlineERImgs.length !== 0) {
            this.offlineERImgs = res.offlineERImgs
          } else {
            this.offlineERImgs = null
          }
          if (res.offlineDiagnosisImgs.length !== 0) {
            this.offlineDiagnosisImgs = res.offlineDiagnosisImgs
          } else {
            this.offlineDiagnosisImgs = null
          }
          if (res.descriptionImgs.length !== 0) {
            this.descriptionImgs = res.descriptionImgs
          } else {
            this.descriptionImgs = null
          }
          if (res.drugsImgs.length !== 0) {
            this.drugsImgs = res.drugsImgs
          } else {
            this.drugsImgs = null
          }
          if (res.offlineERPdfs.length !== 0) {
            this.offlineERPdfs = res.offlineERPdfs
          } else {
            this.offlineERPdfs = null
          }
        })
      }
      if (this.caseId) {
        this.typeShow1 = false
        this.typeShow2 = true
        this.title = '病历详情'
        const params = {
          recoreId: this.caseId
        }
        API.getCaseDetail(params).then(res => {
        // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 0) {
            res.drCaseVO.menstrual.status = '未初潮'
          }
          // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 1) {
            res.drCaseVO.menstrual.status = '已初潮'
          }
          // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 2) {
            res.drCaseVO.menstrual.status = '已绝经'
          }
          this.casedetail = res.drCaseVO
          this.menstrual = res.drCaseVO.menstrual
          this.signInfo = res.signInfo
          if (res.drCaseVO.imgList.length !== 0) {
            this.casedetailImgs = res.drCaseVO.imgList
          } else {
            this.casedetailImgs = null
          }
        })
      }
    }
  }
}
</script>
