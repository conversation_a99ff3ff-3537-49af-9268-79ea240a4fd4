<template>
  <div id="room-root" class="col-div">
    <!-- 头部 -->
    <div class="row-div card">
      <div class="logo">
        <div class="logoText">视频通话</div>
      </div>
      <div class="logout">
        <img id="logout-btn" src="/static/img/logout.png" alt="" @click="leaveRoom()">
      </div>
      <div id="header-roomId">诊室编号: {{ roomId }}</div>
    </div>
    <!-- content -->
    <div class="row-div roomContent">
      <!-- 人员列表 -->
      <div class="col-div memberMain">
        <div class="col-div card box">
          <div id="member-list" class="col-div">
            <!-- member -->
            <div id="member-me">
              <div v-for="(item,i) in personnel" :key="i" class="row-div member">
                <div class="member-id">{{ item.type===1?'患者':'医生' }}：{{ item.name }}</div>
                <div class="row-div memberIcon">
                  <img class="member-video-btn" :src="item.camera?'/static/img/camera-on.png':'/static/img/camera-off.png'" alt="">
                  <div style="width: 18px"></div>
                  <div class="memberMic-on">
                    <img class="member-audio-btn" :src="item.mic?'/static/img/mic-on.png':'/static/img/mic-off.png'" alt="">
                    <div class="volume-level" :style="item.height">
                      <img alt="" src="/static/img/mic-green.png">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 视频网格 -->
      <div id="video-grid" ref="videoGrid">
        <!-- 主视频 -->
        <div id="main-video" ref="mainVideo" class="video-box col-div">
          <!-- 主视频控制按钮 -->
          <div class="video-box">
            <template v-for="(item,i) in remoteStream">
              <div v-if="item.type==1" :key="i" class="video-box" style="width:100%;height:100%">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <div class="distant-stream" v-html="item.base">
                </div>
              </div>
            </template>
          </div>

          <div id="mask_main" class="mask col-div" style="display:none">
            <!-- “摄像头未开启”遮罩 -->
            <div class="videoMask"><div></div></div>
            <div class="text">患者未进入诊室</div>
          </div>
        </div>
        <!-- 小视频 -->
        <div class="videoList">
          <div class="video-box small-video">
            <div id="main-video-btns" class="row-div">
              <img id="video-btn" :src="closeCamera?'/static/img/big-camera-on.png':'/static/img/big-camera-off.png'" alt="" @click.stop="muteVideo()">
              <img id="mic-btn" :src="closeMic?'/static/img/big-mic-on.png':'/static/img/big-mic-off.png'" alt="" @click.stop="muteMic()">
            </div>
            <div id="local_stream" class="local-stream"></div>
            <div v-show="!closeCamera" id="mask_main" class="mask col-div">
              <!-- “摄像头未开启”遮罩 -->
              <div class="videoMask"><div></div></div>
              <img src="/static/img/camera-max.png" alt="">
              <div class="text">摄像头未打开</div>
            </div>
          </div>
          <template v-for="(item,i) in remoteStream">
            <div v-if="item.type==2" :key="i" class="video-box small-video" @click.prevent="videoItem(i)">
              <!-- eslint-disable-next-line vue/no-v-html -->
              <div :class="item?'distant-stream':''" v-html="item.base">
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TRTC from 'trtc-js-sdk'
import API from '@/api/doctor/index'
// import { getBrowser } from '@/utils/isMobile.js'
export default {
  name: 'VideoSpecial',
  props: {
    isMeeting: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      userId: '', //用户id --可更改
      roomId: '', //房间号--加入相同房间才能聊
      client: '', //客户端服务
      remoteStream: [], //远方播放流
      localStream: null, //本地流
      sdkAppId: '', //sdkID
      userSig: '', //签名
      ShowlocalStream: false, //主摄像头视频是否加载完毕
      closeCamera: true, //本地摄像头是否开启
      closeMic: true, //本地麦克风是否开启,
      doctorName: '', //房间初始医生姓名
      personnel: [], //人员列表
      timer: [], //所有定时器
      remoteStreams: [], //远端流数组
      videoActive: -1, //视频高亮
      rlen: history.length,
      personnelIndex: null //患者下标,

    }
  },
  mounted() {
    this.sdkAppId = sessionStorage.getItem('sdkAppId') * 1
    this.userSig = sessionStorage.getItem('userSig')
    this.userId = sessionStorage.getItem('userId')
    this.roomId = this.$route.query.roomId * 1
    this.doctorName = this.$route.query.doctorName
    const doctorData = {
      name: this.$route.query.doctorName,
      type: 2,
      userId: this.userId,
      mic: true,
      camera: true,
      height: { height: '0%' }
    }
    this.personnel.push(doctorData)
    this.createClient(this.userId)
  },
  beforeDestroy() {
    if (this.client) {
      this.leaveRoom()
    }
  },
  methods: {
    goBack() {
      if (this.isMeeting) {
        this.$router.push({
          path: '/doctor/index?Status=2'
        })
      } else {
        const len = this.rlen - history.length - 1 //-1是不进入iframe页面的下级页面直接退出的话，执行后退一步的操作
        console.log(this.rlen, history.length)
        this.$router.go(len)
      }

    },
    videoItem(idx) {
      var that = this
      for (let i = 0; i < that.remoteStream.length; i++) {
        that.$set(that.remoteStream[i], `type`, 2)
      }
      that.$set(that.remoteStream[idx], `type`, 1)
      this.$nextTick(() => {
        for (var i = 0; i < that.remoteStreams.length; i++) {
          that.remoteStreams[i].stop('remote_stream-' + that.remoteStreams[i].getId())
          that.remoteStreams[i].play('remote_stream-' + that.remoteStreams[i].getId())
        }
      })
    },

    // 清除所有定时器
    clearTimer() {
      var that = this
      for (let i = 0; i < that.timer.length; i++) {
        clearInterval(that.timer[i])
        that.timer = []
      }
    },
    //创建链接
    createClient(userId) {
      const sdkAppId = this.sdkAppId
      const userSig = this.userSig
      this.client = TRTC.createClient({
        mode: 'rtc',
        sdkAppId,
        userId,
        userSig
      })
      //注册远程监听，要放在加入房间前--这里用了发布订阅模式
      this.subscribeStream(this.client)
      //初始化后才能加入房间
      this.joinRoom(this.client, this.roomId)
      // 公共监听方法
      this.publicMonitor(this.client)
    },
    //加入房间
    joinRoom(client, roomId) {
      client.join({ roomId })
        .catch(error => {
          console.error('进房失败 ' + error)
        })
        .then(() => {
          console.log('进房成功')
          //创建本地流
          this.createStream(this.userId)
          //播放远端流
          this.playStream(this.client)

        })
    },
    //创建本地音视频流
    createStream(userId) {
      const localStream = TRTC.createStream({ userId, audio: true, video: true })
      this.localStream = localStream
      localStream
        .initialize()
        .catch(error => {
          console.error('初始化本地流失败 ' + error)
        })
        .then(() => {
          console.log('初始化本地流成功')
          localStream.play('local_stream')
          this.setVolumeInterval(localStream, userId)
          //创建好后才能发布
          this.publishStream(localStream, this.client)

        })
    },
    //发布本地音视频流
    publishStream(localStream, client) {
      client
        .publish(localStream)
        .catch(error => {
          console.error('本地流发布失败 ' + error)
        })
        .then(() => {
          console.log('本地流发布成功')
          this.ShowlocalStream = true
        })
    },
    //订阅远端流--加入房间之前
    subscribeStream(client) {
      client.on('stream-added', event => {
        const remoteStream = event.stream
        this.remoteStreams.push(remoteStream)
        //订阅远端流
        client.subscribe(remoteStream)
        this.setVolumeInterval(remoteStream, this.personnel[this.personnel.length - 1].userId)
      })
    },
    //公共监听方法
    publicMonitor(client) {
      var that = this
      //  顶下线
      client.on('client-banned', err => {
        this.$alert('视频通话已结束', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            this.leaveRoom()
          }
        })

      })
      // 新增人员
      client.on('peer-join', evt => {
        const userId = evt.userId
        var params = {}
        params.userId = userId
        var list = that.personnel.concat(params)
        that.personnel = list
        const index = that.personnel.findIndex((item) => item.userId === userId)
        this.getNames(userId).then(res => {
          res.userId = userId
          res.mic = true
          res.camera = true
          res.height = { height: '0%' }
          that.$set(that.personnel, index, res)
        }).catch(res => {
          console.log(res)
        })
      })
      // 删减人员
      client.on('peer-leave', evt => {
        const userId = evt.userId
        const index = that.personnel.findIndex((item) => item.userId === userId)
        that.personnel.splice(index, 1)
        clearInterval(this.timer[index])
        this.timer[index] = null
        this.timer.splice(index, 1)
        var peerIndex = that.remoteStreams.findIndex((item) => item.getUserId() === userId)
        var remoteIndex = that.remoteStream.findIndex((item) => item.userId === userId)
        that.remoteStream.splice(remoteIndex, 1)
        that.remoteStreams.splice(peerIndex, 1)
        this.$nextTick(() => {
          for (var i = 0; i < that.remoteStreams.length; i++) {
            that.remoteStreams[i].stop('remote_stream-' + that.remoteStreams[i].getId())
            that.remoteStreams[i].play('remote_stream-' + that.remoteStreams[i].getId())
          }
        })

      })
      // 推流方关闭音频
      client.on('mute-audio', evt => {
        const userId = evt.userId
        const index = that.personnel.findIndex((item) => item.userId === userId)
        that.$set(that.personnel[index], `mic`, false)
        // 关闭声音大小监听
        clearInterval(this.timer[index])
        this.timer[index] = null
      })
      // 推流方开启音频
      client.on('unmute-audio', evt => {
        const userId = evt.userId
        const index = that.personnel.findIndex((item) => item.userId === userId)
        var d = this.remoteStreams.find(stream => stream.getUserId() === userId)
        that.$set(that.personnel[index], `mic`, true)
        if (d) {
          this.setVolumeInterval(d, userId)
        }
      })
    },
    //播放远端流
    playStream(client) {
      client.on('stream-subscribed', async event => {
        const remoteStream = event.stream
        const base = `<view id="${'remote_stream-' + remoteStream.getId()}"  style="width:100%;height:100%"></view>`
        // 增加type用来渲染的时候识别医生还是患者
        var type
        await this.getNames(remoteStream.getUserId()).then(res => {
          type = res.type
        })
        var data = {
          base: base,
          userId: remoteStream.getUserId(),
          type: type
        }
        this.remoteStream.push(data)
        console.log(this.remoteStream)
        this.$nextTick(() => {
          remoteStream.play('remote_stream-' + remoteStream.getId())
        })
      })

    },
    //退出音视频
    leaveRoom() {
      var client = this.client
      client.leave().then(() => {
        // 停止本地流，关闭本地流内部的音视频播放器
        this.localStream.stop()
        // 关闭本地流，释放摄像头和麦克风访问权限
        this.localStream.close()
        this.localStream = null
        this.client = null
        this.clearTimer()
        this.goBack()
      })
        .catch(error => {
          console.error('退房失败 ' + error)
          // 错误不可恢复，需要刷新页面。
          // 停止本地流，关闭本地流内部的音视频播放器
          this.localStream.stop()
          // 关闭本地流，释放摄像头和麦克风访问权限
          this.localStream.close()
          this.localStream = null
          this.client = null
          this.clearTimer()
          this.goBack()
        })
    },
    // 关闭或开启摄像头
    muteVideo(e) {
      if (this.closeCamera) {
        this.localStream.muteVideo()
        this.$set(this.personnel[0], `camera`, false)
        this.closeCamera = false
      } else {
        this.localStream.unmuteVideo()
        this.$set(this.personnel[0], `camera`, true)
        this.closeCamera = true
      }
    },
    // 开始关闭麦克风
    muteMic(e) {
      var userId = this.localStream.getUserId()
      if (this.closeMic) {
        this.localStream.muteAudio()
        this.$set(this.personnel[0], `mic`, false)
        this.closeMic = false
        clearInterval(this.timer[0])
        this.timer[0] = null
      } else {
        this.localStream.unmuteAudio()
        this.$set(this.personnel[0], `mic`, true)
        this.closeMic = true
        this.setVolumeInterval(this.localStream, userId)
      }
    },
    // 监听声音大小
    setVolumeInterval(stream, userId) {
      var that = this
      const index = that.personnel.findIndex((item) => item.userId === userId)
      clearInterval(this.timer[index])
      that.timer[index] = setInterval(() => {
        const volume = stream.getAudioLevel()
        if (volume > 0.01) {
          that.$set(that.personnel[index], `height`, { height: `${volume * 100 * 4}%` })
        } else {
          that.$set(that.personnel[index], `height`, { height: `0%` })
        }
      }, 1000)
    },
    // 根据userID换用户信息
    getNames(id) {
      const params = {
        userId: id,
        roomId: this.roomId
      }
      return API.getThirdNames(params)
    }
  }
}
</script>
<style src="@/assets/css/index.css"  scoped></style>
<style src="@/assets/css/room.css"  scoped></style>
<style src="@/assets/css/bootstrap-material-design.min.css"  scoped></style>
<style  scoped>
  #member-list{
    display: block;
    padding-bottom: 30px;
  }
  #room-root{
    display: block;
    height: 77vh;
    min-height: 77vh;
    overflow-x: hidden;
  }
  .card {
      border: 0;
      box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%), 0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
      width: 100%; height: 65px; justify-content: space-between;
      display: flex;
      flex-direction: row;
  }
  .logo{
      height: 100%; width: 230px; justify-content: center;
      display: flex;
      align-items: center;
  }
  #header-roomId{
      width: 230px; justify-content: flex-end; padding-right: 20px; font-size: 14px; color: #888888;
      display: flex;
      align-items: center;
  }
  /* .local-stream {
    width:300px;
    height: 300px;
    float: left;
  }*/
  .distant-stream {
    width: 100%;
    height:100%;
  }
  .logoText{
    width: 86px; height: 23px; font-size: 18px; color: #333333
  }
  .logout{
    height: 100%;width: auto;
  }
  .logout img{
    width: 65px;
    height: 65px;
  }
  .roomContent{
    height: 100%; width: 100%; padding: 10px;background:#f0f0f0;align-items: flex-start;
  }
  .memberMain{
    width: 340px;
    padding: 10px;
  }
  .memberMain .box{
    width: 100%;height: 100%;display: block;
  }
  #member-list{
    width: 100%; justify-content: flex-start; flex: 1;min-height:400px
  }
  #member-me{
    width: 100%; padding-left: 20px;flex-direction: column;
  }
  #member-me .member{
    width: 100%; height: 50px; justify-content: space-between;
  }
  .memberIcon{
    width:100px; height: 26px; justify-content: center
  }
  .memberMic-on{
    width: 28px; height: 28px; display: inline-block; position: relative
  }
  .member-audio-btn{width: 100%; height: 100%;}
  .volume-level{position: absolute; bottom: 0; left: 0; width: 28px; overflow: hidden; transition: height .1s ease;}
  .volume-level img{
    position: absolute; bottom: 0;
  }
  #video-grid{
    height: 120%; flex: 1;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }
  #main-video{
    width: auto;
    height: auto;
    flex: 1;
    height: 450px;
  }
  .videoList{
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
  }
  #main-video-btns{width: 156px; position: absolute; z-index: 10; justify-content: center; align-self: flex-end;bottom:20px}
  #main-video-btns img{
    width: 68px;height: 68px;
  }
  #local_stream{
    width: 100%;height: 100%;
  }
  .videoMask{
    height: 100%; width: 100%;box-sizing:border-box;padding:10px;position: absolute; top: 0; left: 0;
  }
  .videoMask div{
    height: 100%; width: 100%; background-color: #D8D8D8;
  }
  #mask_main img{
    width: 63px; height: 69px; z-index: 10;
  }
  #mask_main .text{
    padding-top: 20px;
    z-index: 10;
  }
  .small-video{
    /* width: 50%; */
    height: 250px;
    box-sizing: border-box;
    padding: 5px;
    position: relative;
    flex: 1;
    max-width: 50%;
    min-width: 30%;
  }
</style>
