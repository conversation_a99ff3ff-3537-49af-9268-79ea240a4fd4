<template>
  <el-radio-group v-model="selectValue">
    <el-radio
      v-for="item in dictData"
      :key="item.code"
      :label="item.code"
      :value="item.code"
      @change="handleSelect"
    >{{ item.value }}</el-radio>
  </el-radio-group>
</template>

<script>
import { getDict } from '@/api/system/dict'
export default {
  name: 'DictRadio',
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: null
    },
    type: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      selectValue: null,
      dictData: []
    }
  },
  watch: {
    value: function(val) {
      this.selectValue = this.value
    }
  },
  mounted() {
    this.getDict()
    this.selectValue = this.value
  },
  methods: {
    // 获取数据
    getDict() {
      getDict(this.type).then(response => {
        this.dictData = response
      })
    },
    handleSelect() {
      if (this.selectValue === '') {
        this.$emit('change', null)
      } else {
        this.$emit('change', this.selectValue)
      }
    }
  }
}
</script>
