<template>
  <el-date-picker
    v-model="tempDate"
    :editable="false"
    type="daterange"
    range-separator="-"
    :start-placeholder="startPlaceholder"
    :end-placeholder="endPlaceholder"
    value-format="yyyy-MM-dd"
    :picker-options="pickerOptions"
  />
</template>

<script>
import { getFilterDate } from '@/utils/date'
import { pickerOptions } from '@/utils/'
export default {
  name: 'DatePicker',
  model: {
    event: 'change'
  },
  props: {
    gte: {
      type: String,
      default: 'dateGte'
    },
    lte: {
      type: String,
      default: 'dateLte'
    },
    startPlaceholder: {
      type: String,
      default: '开始日期'
    },
    endPlaceholder: {
      type: String,
      default: '结束日期'
    },
    queryModel: {
      type: Object,
      default: null
    },
    useDefault: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pickerOptions: pickerOptions,
      tempDate: this.useDefault ? getFilterDate() : null
    }
  },
  watch: {
    tempDate: function(val) {
      if (this.tempDate) {
        this.queryModel[this.gte] = this.tempDate[0]
        this.queryModel[this.lte] = this.tempDate[1]
      } else {
        this.queryModel[this.gte] = null
        this.queryModel[this.lte] = null
      }
      this.$emit('change', this.queryModel)
    }
  },
  mounted() {
    if (this.tempDate) {
      this.queryModel[this.gte] = this.tempDate[0]
      this.queryModel[this.lte] = this.tempDate[1]
    }
  }
}
</script>
