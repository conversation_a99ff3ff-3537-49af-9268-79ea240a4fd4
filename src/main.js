import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css
import '@/styles/iconfont/iconfont.css' // global css

import App from './App'
import router from './router'
import store from './store'
import './icons' // icon
import './permission' // permission control
import './utils/errorLog' // error log

import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
Vue.component('Pagination', Pagination)

import BackToTop from '@/components/BackToTop'
Vue.component('BackToTop', BackToTop)

import * as filters from './filters' // global filters

// import { mockXHR } from '../mock' // simulation data

import permission from '@/directive/permission/index.js' // 权限判断指令
Vue.use(permission)

// mock api in github pages site build

import Vant from 'vant'
import 'vant/lib/index.css'

Vue.use(Vant)

import Common from '@/utils/common.js'
Vue.prototype.Common = Common

Vue.use(Element, {
  size: Cookies.get('size') || 'mini'
})

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

var vm = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
global.vm = vm
