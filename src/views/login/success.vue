<template>
  <div class="app-container">
    <img src="@/assets/image/ok.png" />
    <div>成功</div>
  </div>
</template>

<style lang="less" scoped>
  .app-container {
    text-align: center;
    padding-top: 30%;
    & img {
      width: 100px;
      height: 100px;
      padding-bottom: 10px;
    }
  }
</style>
<script>
export default {
  name: 'Project',
  components: {
  },
  data() {
    return {
    }
  },
  computed: {},
  created() {
  },
  mounted() {
    window.setTimeout(() => {
      this.closeT()
    }, 3000)
  },
  methods: {
    closeT() {
    }
  }
}
</script>
