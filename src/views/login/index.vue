<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" label-position="left" label-width="0px" class="login-form">
      <h3 class="title">
        医生工作站
      </h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" placeholder="请输入用户名">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" placeholder="请输入密码">
          <i slot="prefix" class="el-icon-lock"></i>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="loginForm.code" placeholder="请输入验证码，不区分大小写" style="width: 63%" @keyup.enter.native="handleLogin">
          <i slot="prefix" class="login_verification"></i>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item>
      <el-form-item prop="smsCode">
        <el-input v-model="loginForm.smsCode" placeholder="请输入短信验证码" style="width: 63%" @keyup.enter.native="handleLogin">
          <i slot="prefix" class="login_verification"></i>
        </el-input>
        <div class="login-code">
          <el-button size="medium" :disabled="codetime>0" type="primary" @click.native.prevent="getMsCode">
            <span>{{ codetext }}</span>
          </el-button>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住我</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" :disabled="!isGetCode" size="medium" type="primary" style="width:100%;" @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
      <el-radio v-model="loginForm.agreement" style="margin:0px 0px 25px 0px;">
        <span @click="agreementClick">《互联网用户协议》</span>
        <span @click="privacyClick">《隐私政策》</span>
      </el-radio>
    </el-form>
    <!--  底部  -->
    <div v-if="showFooter" id="el-login-footer">
      <!-- eslint-disable-next-line vue/no-v-html -->
      <span v-html="footerTxt" />
      <span>⋅</span>
      <a href="http://www.baidu.com/" target="_blank">{{ caseNumber }}</a>
    </div>
    <div class="textfix">{{ company }}互联网医院</div>

    <el-dialog title="《互联网用户协议》" :visible.sync="agreementDialog" width="70%" top="5vh">
      <div v-if="detail">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div v-html="detail"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="agreementDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="《隐私政策》" :visible.sync="privacyDialog" width="70%" top="5vh">
      <div v-if="detail">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div v-html="detail"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="privacyDialog = false">关 闭</el-button>
      </span>
    </el-dialog>

    <el-dialog title="人脸识别" :visible.sync="faceDialogVisible" width="70%" top="20vh" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <div class="tc">
        <div>请使用微信或其他软件扫码二维码，并依照提示完成人脸识别</div>
        <canvas id="canvas" ref="canvas"></canvas>
        <div>我同意采集人脸照片用于认证服务</div>
        <div v-if="expired" class="line pt_10" @click="closeQr">二维码已过期，点击重新登录！</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves' // Waves directive
import Settings from '@/settings'
import Cookies from 'js-cookie'
import { encrypt } from '@/utils/rsaEncrypt'
import API from '@/api/doctor/index'
import qrcode from 'qrcode'
export default {
  name: 'Login',
  directives: { waves },
  data() {
    return {
      // title: Settings.title + '医生端',
      title: '医生工作站',
      showFooter: Settings.showFooter,
      footerTxt: Settings.footerTxt,
      caseNumber: Settings.caseNumber,
      codeUrl: '',
      cookiePass: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        smsCode: '',
        uuid: '',
        agreement: true
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '用户名不能为空'
          }
        ],
        password: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '密码不能为空'
          }
        ],
        code: [{ required: true, trigger: 'change', message: '验证码不能为空' }],
        smsCode: [{ required: true, trigger: 'change', message: '短信验证码不能为空' }]
      },
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      codetime: 0, //验证码倒计时
      codetext: '获取验证码',
      isGetCode: false,
      agreementDialog: false,
      privacyDialog: false,
      detail: '',
      company: process.env.VUE_APP_BASE_COMPANY,
      faceDialogVisible: false,
      timer: null,
      expired: false
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
    // token 过期提示
    this.point()
    // this.getPrivacy()
  },
  mounted() {
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    getCode() {
      API.getCodeImg().then(res => {
        this.codeUrl = res.img
        this.loginForm.uuid = res.uuid
      })
    },
    getCookie() {
      const username = Cookies.get('username')
      let password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      // 保存cookie里面的加密后的密码
      this.cookiePass = password === undefined ? '' : password
      password = password === undefined ? this.loginForm.password : password
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        code: ''
      }
    },
    point() {
      const point = Cookies.get('point') !== undefined
      if (point) {
        this.$notify({
          title: '提示',
          message: '当前登录状态已过期，请重新登录！',
          type: 'warning',
          duration: 5000
        })
        Cookies.remove('point')
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          code: this.loginForm.code,
          smsCode: this.loginForm.smsCode,
          uuid: this.loginForm.uuid
        }
        if (user.password !== this.cookiePass) {
          user.password = encrypt(user.password)
        }
        if (valid) {
          this.loading = true
          if (user.rememberMe) {
            Cookies.set('username', user.username, {
              expires: Settings.passCookieExpires
            })
            Cookies.set('password', user.password, {
              expires: Settings.passCookieExpires
            })
            Cookies.set('rememberMe', user.rememberMe, {
              expires: Settings.passCookieExpires
            })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('rememberMe')
          }
          this.$store
            .dispatch('system/user/login', user)
            .then(res => {
              this.loading = false
              if (res.faceIdVO) {
                this.drawQrcode(res.faceIdVO)
              } else {
                this.$router.push({ path: '/' })
              }
              // window.location.href = res.h5faceUrl
            })
            .catch(() => {
              this.loading = false
              this.getCode()
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getMsCode() {
      var fieldList = ['username', 'password', 'code']
      Promise.all(fieldList.map(item => {
        return new Promise((resolve, reject) => {
          this.$refs.loginForm.validateField(item, (valid) => {
            console.log('getMsCode', valid)
            resolve(valid)
          })
        })
      })).then(result => {
        console.log(result, 'result')
        const valid = result.every(str => {
          return str === ''
        })
        if (valid) {
          const params = {
            username: this.loginForm.username,
            password: this.loginForm.password,
            code: this.loginForm.code,
            uuid: this.loginForm.uuid
          }
          if (params.password !== this.cookiePass) {
            params.password = encrypt(params.password)
          }
          API.getVerification(params).then(res => {
            console.log('获取验证码成功')
            this.$message.success(res.sendResult)
            this.isGetCode = true
            this.codetime = 59
            this.codetext = this.codetime + 's'
            const t = setInterval(() => {
              this.codetime--
              this.codetext = this.codetime + 's'
              if (this.codetime <= 0) {
                this.codetime = 0
                this.codetext = '重新获取'
                clearInterval(t)
              }
            }, 1000)
          }).catch(error => {
            this.getCode()
            // this.$message.error(error)
          })
        }
      })

      // this.loginForm

    },
    agreementClick() {
      this.getAgreement()
      this.agreementDialog = true
    },
    privacyClick() {
      this.getPrivacy()
      this.privacyDialog = true
    },
    getAgreement() {
      API.getAgreement().then(res => {
        this.detail = res.content
      })
    },
    getPrivacy() {
      API.getPrivacy().then(res => {
        this.detail = res.content
      })
    },
    //画二维码
    drawQrcode(params) {
      this.expired = false
      this.faceDialogVisible = true
      // const canvas = document.getElementById('canvas')
      this.$nextTick(() => {
        const canvas = this.$refs['canvas']
        qrcode.toCanvas(canvas, params.h5faceUrl, { width: 200, height: 200 }, error => {
          if (error) {
          // this.$message.error('加载失败')
          }
        })
      })

      const data = {
        faceId: params.h5faceId,
        userName: this.loginForm.username
      }
      //延迟5秒查询结果
      window.setTimeout(() => {
        this.timer = setInterval(() => {
          this.$store.dispatch('system/user/checkLogin', data) .then(res => {
            if (res.token) {
              //清除定时器
              clearInterval(this.timer)
              // 因为演示经常随便切换，权限，所以默认都跳转首页
              this.$router.push({ path: '/' })
              // this.$router.push({ path: this.redirect || '/' })
            } else if (res.expired) {
              this.expired = true
              // this.$message.error('二维码已过期，请重新登录！')
              clearInterval(this.timer)
              // this.faceDialogVisible = false
            }
          })
        }, 2000)
      }, 5000)
    },
    closeQr() {
      this.faceDialogVisible = false
      this.getCode()
    }
  }
}
</script>
<style lang="less">
  @import "../../assets/css/common.less";
</style>
<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url(/static/images/bg-1.jpg);
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  /deep/ {
    .el-input__prefix {
      display: flex;
      align-items: center;
      font-size: 16px;
      .login_verification {
        background-image: url(../../assets/image/login_verification.png);
        width: 20px;
        height: 20px;
        background-size: contain;
      }
    }
  }

  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  display: inline-block;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.textfix{
  position: absolute;
  left: 50px; top: 50px;
  font-size: 30px;
  color: #fff;
  font-weight: 600;
}
</style>
