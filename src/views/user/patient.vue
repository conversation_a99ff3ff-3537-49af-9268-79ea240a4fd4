<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.patientName"
        clearable
        placeholder="患者姓名"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>

    <el-table :key="tableKey" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="ID" prop="id" width="40px" align="center" />
      <el-table-column label="头像" prop="patientIcon" width="80px" align="center">
        <template slot-scope="{row}">
          <el-image :src="row.patientIcon">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" width="100px" align="center" />
      <el-table-column label="性别" prop="genderDescribe" width="60px" align="center" />
      <el-table-column label="年龄" prop="age" width="100px" align="center" />
      <el-table-column label="城市" prop="cityName" width="100px" align="center" />
      <el-table-column label="分组" prop="myGroup" width="170px" align="center">
        <template slot-scope="{row}">
          <el-tag v-for="(item, index) in row.myGroup" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所在项目" prop="projects" align="center">
        <template slot-scope="{row}">
          <el-tag v-for="(item, index) in row.projects" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="createdAt" width="155px" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="goMedicalRecord(scope.row.id)"
          >病历</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getList } from '@/api/user/patient'
import waves from '@/directive/waves' // Waves directive
export default {
  name: 'PatientsTable',
  directives: { waves },
  filters: {},
  components: {
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc'
      },
      textMap: {
        update: '更新',
        create: '新增'
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    goMedicalRecord(id) {
      this.$router.push({
        path: '/medicalRecord/list/' + id
      })
    }
  }
}
</script>
