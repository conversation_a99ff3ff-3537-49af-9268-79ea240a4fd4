<template>
  <div>
    <el-dialog title="患者详情" :visible.sync="dialogInfoVisible" width="80%" top="2vh">
      <el-tabs style="min-height: 300px;" type="card">
        <el-tab-pane label="基本信息">
          <el-form ref="dataForm" :model="patient" label-position="right" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="ID" prop="id">
                  <el-input v-model="patient.baseInfo.id" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="patient.baseInfo.name" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="头像" prop="headUrl">
                  <el-image :src="headUrl">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="昵称" prop="nickName">
                  <el-input v-model="patient.baseInfo.nickName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="城市" prop="cityId">
                  <el-input v-model="patient.baseInfo.cityName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <el-input v-model="patient.baseInfo.typeDescribe" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="手机" prop="type">
                  <el-input v-model="patient.baseInfo.phone" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-input v-model="patient.baseInfo.genderDescribe" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生ID" prop="recomDoctor">
                  <el-input v-model="patient.baseInfo.recomDoctor" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生姓名" prop="recomDoctorName">
                  <el-input v-model="patient.baseInfo.recomDoctorName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="认证状态" prop="status">
                  <el-input v-model="patient.baseInfo.statusDescribe" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="关注医生">
          <el-table :data="patient.focusOnDoctors" fit style="width: 100%">
            <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
            <el-table-column label="姓名" prop="name" width="100px" align="center" />
            <el-table-column label="头像" prop="headUrl" width="80px" align="center">
              <template slot-scope="{row}">
                <el-image :src="row.headUrl">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="typeDescribe" width="80px" align="center" />
            <el-table-column label="手机" prop="phone" width="110px" align="center" />
            <el-table-column label="执业医院" prop="hospitalName" align="center" />
            <el-table-column label="科室" prop="departmentName" width="130px" align="center" />
            <el-table-column label="医院所在地" prop="hospitalCityName" width="105px" align="center" />
            <el-table-column label="账户状态" prop="accountStatusDescribe" width="80px" align="center" />
            <el-table-column label="认证状态" prop="statusDescribe" align="center" width="110px" />
            <el-table-column label="备案状态" prop="recordStatusDescribe" align="center" width="100px" />
            <el-table-column label="注册时间" prop="createdAt" width="140px" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="历史订单">
          <el-table :data="patient.orderList" highlight-current-row fit style="width: 100%">
            <el-table-column label="订单号" fixed prop="orderSn" width="180px" align="center" />
            <el-table-column label="商品总价" prop="totalAmount" width="80px" align="center" />
            <el-table-column label="邮费" prop="freight" width="80px" align="center" />
            <el-table-column label="优惠券" prop="couponPay" width="80px" align="center" />
            <el-table-column label="实付" prop="realPay" width="80px" align="center" />
            <el-table-column label="订单状态" prop="orderStatusDescribe" width="80px" align="center" />
            <el-table-column label="支付状态" prop="payStatusDescribe" width="80px" align="center" />
            <el-table-column label="订单时间" prop="createdAt" width="140px" align="center" />
            <el-table-column label="支付时间" prop="payTime" width="140px" align="center" />
            <el-table-column label="发货时间" prop="sendTime" width="140px" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="咨询记录">
          <el-table
            :data="patient.consultSessionList"
            highlight-current-row
            fit
            style="width: 100%"
          >
            <el-table-column align="center" label="医生ID" prop="doctorId" />
            <el-table-column align="center" label="医生名称" prop="doctorName" />
            <el-table-column align="center" label="会话ID" width="250px" prop="sessionId" />
            <el-table-column align="center" label="开始时间" width="155px" prop="startTime" />
            <el-table-column align="center" label="结束时间" width="155px" prop="endTime" />
            <el-table-column align="center" label="金额" width="80px" prop="cost" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="病历档案">
          <el-table :data="caseList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="医生姓名" width="100px" prop="doctorName" />
            <el-table-column align="center" label="就诊医院" width="180px" prop="hospitalName" />
            <el-table-column align="center" label="就诊科室" width="100px" prop="departmentName" />
            <el-table-column
              align="center"
              label="主诉"
              width="300px"
              prop="doctorMedicalRecord.mainComplaint"
            />
            <el-table-column
              align="center"
              label="现病史"
              width="200px"
              prop="doctorMedicalRecord.presentDisease"
            />
            <el-table-column
              align="center"
              label="既往史"
              width="200px"
              prop="doctorMedicalRecord.pastHistory"
            />
            <el-table-column align="center" label="检查指标" width="250px" prop="sessionId" />
            <el-table-column
              align="center"
              label="诊断"
              width="200px"
              prop="doctorMedicalRecord.diagnosis"
            />
            <el-table-column
              align="center"
              label="医嘱小结"
              width="300px"
              prop="doctorMedicalRecord.doctorOrder"
            />
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="getCaseList"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-link
      v-waves
      type="primary"
      :underline="false"
      @click="handlePatientDetailsInfo()"
    >{{ patientName || patientNickName }}</el-link>
  </div>
</template>
<style>
.el-dialog {
  text-align: left;
}
/* .el-form-item__content .el-input__inner {
  width: 250px;
}
.el-form-item__content .el-textarea__inner {
  width: 250px;
}
.el-form-item__content .el-image {
  width: 250px;
  height: 250px;
} */
</style>
<script>
import { get, getCaseList } from '@/api/user/patient'
import waves from '@/directive/waves' // Waves directive
export default {
  name: 'PatientDetails',
  directives: { waves },
  props: {
    patientId: {
      type: String,
      required: false,
      default: ''
    },
    patientName: {
      type: String,
      required: false,
      default: ''
    },
    patientNickName: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      dialogInfoVisible: false,
      patient: {
        baseInfo: {},
        focusOnDoctors: [],
        orderList: []
      },
      headUrl: '',
      caseList: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  methods: {
    resetTemp() {
      this.$nextTick(() => {
        this.patient = {
          baseInfo: {},
          focusOnDoctors: [],
          consultList: [],
          orderList: []
        }
        this.headUrl = ''
      })
    },
    handlePatientDetailsInfo() {
      this.resetTemp()
      this.dialogInfoVisible = true
      get(this.patientId).then(response => {
        this.patient = response
        this.headUrl = response.baseInfo.headUrl
      })
      this.getCaseList()
    },
    getCaseList() {
      this.listQuery.patientId = this.patientId
      getCaseList(this.listQuery).then(response => {
        this.caseList = response[0].result
        this.total = response[0].totalCount
      })
    }
  }
}
</script>
