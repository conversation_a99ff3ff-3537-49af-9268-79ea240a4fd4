<template>
  <div style="background:#f0f0f0;height:100%">
    <deviceTesting v-if="type===1" @changeType="changeType"></deviceTesting>
    <template v-if="type===2">
      <videoRoom class="videoRoom" :is-meeting="isMeeting"></videoRoom>
      <tim class="tim"></tim>
      <el-button v-if="flag" id="closeBtn" type="primary" style="position:fixed;bottom:50px;right:50%;z-index:1001;" @click="closeBtn">结束问诊</el-button>
      <el-link id="offLineRecord" type="primary" style="position:fixed;bottom:100px;right:50px;z-index:1001;" @click="offLineRecordBtn">>>{{ company }}就诊记录</el-link>
      <el-link id="onLineRecord" type="primary" style="position:fixed;bottom:50px;right:50px;z-index:1001;" @click="onLineRecordBtn">>>互联网医院就诊历史记录</el-link>
      <deital v-if="detailShow"></deital>
    </template>
    <el-dialog title="请对本次问诊开具临床诊断" :visible.sync="diagnoseVisible" width="500px">
      <div class="dialog-content">
        <el-select v-model="diagnoseData" multiple filterable remote reserve-keyword placeholder="请输入关键词" :loading="searchLoading" :remote-method="remoteMethod" style="width:300px;">
          <el-option
            v-for="item in diagnoseOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="diagnoseVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmClose()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="互联网医院就诊历史记录" :visible.sync="onLineRecordVisible" width="600px" top="5vh">
      <iframe :src="onlineMedicalRecordUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
    </el-dialog>
    <el-dialog title="线下就诊记录" :visible.sync="offLineRecordVisible" width="600px" top="5vh">
      <iframe :src="offlineMedicalRecordUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
    </el-dialog>
  </div>
</template>
<script>
import videoRoom from '@/components/videoSpecial/videoRoom.vue'//视频房间
import deviceTesting from '@/components/videoSpecial/deviceTesting.vue'//设备监听
import tim from '@/components/videoSpecial/tim.vue'//tim聊天
import deital from '@/components/videoSpecial/detail.vue'//病情病例
import API from '@/api/doctor/index'
// import { getToken } from '@/utils/auth'
export default {
  name: 'VideoSpecial',
  components: {
    deviceTesting, videoRoom, tim, deital
  },
  data() {
    return {
      userId: '', //用户id --可更改
      roomId: '', //房间号--加入相同房间才能聊
      client: '', //客户端服务
      remoteStream: '', //远方播放流
      localStream: '', //本地流
      sdkAppId: '',
      userSig: '',
      type: 1,
      diagnoseVisible: false,
      diagnoseData: [],
      diagnoseOptions: [],
      searchLoading: false,
      onlineMedicalRecordUrl: '',
      offlineMedicalRecordUrl: '',
      offLineRecordVisible: false,
      onLineRecordVisible: false,
      detailShow: false,
      diagnosis: '',
      rlen: history.length,
      company: process.env.VUE_APP_BASE_COMPANY,
      isMeeting: 0,
      flag: true
    }
  },
  mounted() {
    this.sdkAppId = sessionStorage.getItem('sdkAppId') * 1
    this.userSig = sessionStorage.getItem('userSig')
    this.userId = sessionStorage.getItem('userId')
    this.roomId = this.$route.query.roomId
    this.isMeeting = +this.$route.query.isMeeting
    console.log(this.isMeeting, 81)
    // 区分被邀与发起医生 flase被邀 true发起
    this.flag = this.isMeeting ? this.$route.query.flag : true

    console.log(this.flag, 85)
    this.offlineMedicalRecordUrl = sessionStorage.getItem('offlineMedicalRecordUrl')
    this.onlineMedicalRecordUrl = sessionStorage.getItem('onlineMedicalRecordUrl')
  },
  methods: {
    //创建链接
    changeType(params) {
      this.type = params
    },
    closeBtn() {
      if (this.isMeeting) {
        this.meetingHangUp()
      } else {
        this.diagnoseVisible = true
      }
    },
    // 诊断搜索
    remoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        const params = {
          doctorId: this.doctorId,
          key: query
        }
        API.getDiagnoseData(params).then(response => {
          this.searchLoading = false
          this.optionsArr = response.result.map(item => {
            return { value: `${item.name}`, label: `${item.name}` }
          })
          this.diagnoseOptions = this.optionsArr.filter(item => {
            return item.label.toLowerCase()
              .indexOf(query.toLowerCase()) > -1
          })
        })
      } else {
        this.diagnoseOptions = []
      }
    },
    // 问诊结束
    confirmClose() {
      const param = {
        roomId: this.roomId * 1,
        diagnosisList: this.diagnoseData,
        // 诊断类型 1西医诊断；2中医诊断
        diagnosisType: 1
      }
      API.hangUp(param).then(response => {
        this.$message.success('问诊已结束')
        // this.$router.go(-1)
        // window.history.back()
      })
    },
    onLineRecordBtn() {
      this.onlineMedicalRecordUrl = sessionStorage.getItem('onlineMedicalRecordUrl')
      if (this.onlineMedicalRecordUrl === 'null') {
        this.$message('暂无互联网医院就诊历史记录！')
      } else {
        this.onLineRecordVisible = true
      }
    },
    offLineRecordBtn() {
      console.log(this.offlineMedicalRecordUrl, 677)
      if (this.offlineMedicalRecordUrl === 'null') {
        this.$message('暂无' + process.env.VUE_APP_BASE_COMPANY + '就诊记录')
      } else {
        this.offLineRecordVisible = true
      }
    },
    backBtn(type) {
      const timeTtamp = Date.parse(new Date())
      const iframe_url = sessionStorage.getItem(type)
      this[type] = iframe_url + '&timeTtamp=' + timeTtamp
    },
    meetingHangUp() {
      console.log('===========会诊结束问诊接口传参==========')
      API.meetingHangUp(this.roomId).then(response => {
        // this.$message.success('问诊已结束')
        // this.$router.go(-1)
        // this.$router.push({
        //   path: '/doctor/index?Status=2'
        // })
      })
    }
  }

}
</script>
<style  scoped>
  .app-main{
    overflow: scroll;
  }
</style>
