<template>
  <div class="app-container">
    <el-container class="main">
      <el-aside style="width: 100%;" class="left scroll">
        <ul class="tabUl">
          <li v-for="(item, index) in tabs" :key="index" :class="nowIndex == index ? 'active' : ''" class="tabli" @click="toggleTabs(index)">{{ item.tabName }}</li>
        </ul>
        <el-button class="icon-refresh-w" icon="el-icon-refresh" circle @click="getListData"></el-button>
        <div>
          <ul v-if="listShow" class="listUl">
            <li v-for="(item, index) in list" :key="index" class="listLi">
              <div>
                <div>就诊人信息：</div>
                <div class="row">
                  <span class="span">姓名：{{ item.inquirerInfo.name }} </span>
                  <span class="span">性别：{{ item.inquirerInfo.gender == 0 ? "女" : "男" }} </span>
                  <span class="span">年龄：{{ item.inquirerInfo.age }} </span>
                  <span class="span">联系电话：{{ item.inquirerInfo.ageYear >= 18 ? item.inquirerInfo.phone : "无" }} </span>
                  <template v-if="nowIndex === 1">
                    <span class="span">科室：{{ item.departmentName }} </span>
                  </template>
                </div>
                <div class="row wid">既往史：{{ item.inquirerInfo.pastHistory ? item.inquirerInfo.pastHistory : "无" }}</div>
                <div class="row wid">过敏史：{{ item.inquirerInfo.allergy ? item.inquirerInfo.allergy : "无" }}</div>
                <div v-if="nowIndex === 0 || nowIndex === 2" class="row wid">线下问诊诊断：{{ item.offlineDiagnosis }}</div>
                <div v-if="nowIndex === 0 || nowIndex === 2" class="row wid">病情描述：{{ item.conditionDesc }}</div>
                <template v-if="nowIndex === 1">
                  <div class="row">发起方：{{ item.sourceHospitalName }} | {{ item.departmentName }} | {{ item.doctorName }}</div>
                  <div class="row">受邀方：{{ item.targetHospitalName }} | {{ item.departmentName }} | {{ item.targetDoctorName }} （联系电话：{{ item.targetDoctorPhone }}）</div>
                  <div class="row">会诊原因：{{ item.groupConsultReason }}</div>
                </template>
                <div class="flex">
                  <el-link class="row" type="primary" @click="offlineMedicalRecordUrl(item.offlineMedicalRecordUrl)">>>{{ company }}就诊记录</el-link>
                  <el-link v-if="item.groupConsultStatus === 3 || item.groupConsultStatus === 4" class="row pl_10" type="primary" @click="getMeetingInfo(item.videoConsultId)">>>远程会诊资料</el-link>
                </div>
              </div>
              <div class="flex_none">
                <template v-if="nowIndex === 1">
                  <div style="margin-top:30px">
                    <span>申请会诊时间：{{ item.groupConsultDate }} {{ item.groupConsultTimeStart }} - {{ item.groupConsultTimeEnd }} </span><el-button v-if="item.groupConsultStatus === 1 && doctorId === item.doctorId" icon="el-icon-edit" circle @click="editMeeting(item)"></el-button>
                  </div>
                  <div class="row tr">
                    <el-button v-if="item.groupConsultStatus == 1" type="primary" @click="acceptsMeetingJiezhen(item, 2, doctorId == item.doctorId)">进入会诊</el-button>
                    <!-- <el-button v-if="item.groupConsultStatus==3 && item.groupConsultStatus==4" type="primary" @click="getMeetingInfo(item.videoConsultId)">查看会诊意见</el-button> -->
                    <el-button v-if="item.groupConsultStatus == 2 || item.groupConsultStatus == 3" :disabled="!(item.groupConsultStatus == 2 && doctorId != item.doctorId) && !(item.groupConsultStatus == 3 && doctorId == item.doctorId)" type="primary" @click="suggestDialog(item)">会诊意见</el-button>
                  </div>
                  <div v-if="item.groupConsultStatus == 2 && doctorId == item.doctorId" class="tr active-color" style="margin-top:10px"><span>等待会诊专家完成会诊意见</span></div>
                  <div v-else-if="(item.groupConsultStatus == 2 && doctorId != item.doctorId) || (item.groupConsultStatus == 3 && doctorId == item.doctorId)" class="tr active-color" style="margin-top:10px"><span>请完成会诊意见</span></div>
                </template>
                <template v-else>
                  <div v-if="item.status == 1 || item.status == 2 || item.status == 3" style="margin-top:30px">
                    <span>提交预约时间：{{ item.createdAt }} </span>
                  </div>
                  <div v-if="item.status == 4 || item.status == 5" style="margin-top:30px">
                    <span>结束时间：{{ item.changedAt }} </span>
                  </div>
                  <div v-if="item.status !== 1 && item.refusalReason !== null" class="row">
                    <span>拒绝原因：{{ item.refusalReason }} </span>
                  </div>
                  <div v-if="item.status == 1 || item.status == 2 || item.status == 3" class="row tr">
                    <el-button v-if="item.status == 1 && item.refusalReason == null" type="primary" @click="refusedJiezhen(item)">拒绝接诊</el-button>
                    <el-button v-if="item.status == 1" type="primary" @click="acceptsJiezhen(item, 1)">确认接诊</el-button>
                    <el-button v-if="item.status == 2 || (item.status == 3 && item.groupConsultStatus == 0)" type="danger" @click="acceptsJiezhen(item, 2)">通话中断，点击继续</el-button>
                    <el-button v-else-if="item.status == 3" type="primary" :disabled="true">医生会诊中</el-button>
                    <el-button v-if="item.status == 3 && item.groupConsultStatus === 0" type="primary" @click="changeMeeting(item, 2)">转会诊</el-button>
                  </div>
                  <div v-else class="row tr">
                    <el-button v-if="item.sendCaseFlg == 2 && item.refusalReason == null" type="primary" @click="perfectCase(item)">完善病历</el-button>
                    <el-button v-if="item.refusalReason == null" type="primary" @click="openPrescrip(item)">开具处方</el-button>
                    <el-button v-if="item.sendCaseFlg == 1 && item.refusalReason == null" type="primary" @click="lookCase(item)">查看病历</el-button>
                    <el-button v-if="item.sendRecomFlg == 1 && item.refusalReason == null" type="primary" @click="lookPrescrip(item)">查看处方</el-button>
                  </div>
                </template>
              </div>
            </li>
          </ul>
          <div v-else style="display:flex;justify-content:center;align-items:center;height:200px;">暂无数据</div>
        </div>
        <div v-if="listShow" style="display:flex;justify-content:center;align-items:center;height:70px;">
          <el-pagination layout="prev, pager, next" :current-page="currentPage" :total="total" @current-change="currentChange" @prev-click="prevClick" @next-click="nextClick"> </el-pagination>
        </div>
      </el-aside>
    </el-container>

    <el-dialog title="拒绝理由" :visible.sync="reasonDialogVisible" width="500px" class="dialog_reason">
      <div class="dialog-content">
        <el-radio-group v-model="radio" @change="radioChange">
          <p v-for="(item, index) in radioList" :key="index" style="margin-bottom:10px;">
            <el-radio :label="item.content">{{ item.content }}</el-radio>
          </p>
        </el-radio-group>
        <el-input v-if="reasonShow" v-model="reason" maxlength="50" type="textarea"></el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reasonDialogVisible = false">返 回</el-button>
        <el-button type="primary" @click="confirmRefuse()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="dialogTemplateTitle" :visible.sync="perfectCaseVisible" :close-on-click-modal="false" width="80%" top="5vh" @close="resetForm('caseForm')">
      <el-form v-if="perfectCaseVisible && settings.length" ref="caseForm" :model="caseForm" :rules="caseFormRules" label-width="120px">
        <el-form-item v-if="!isSetTpl" label="就诊人信息:">
          <div class="flex_m_c">
            <div>
              <span class="span">姓名：{{ baseInfo.name }}</span>
              <span class="span">性别：{{ baseInfo.gender == 0 ? "女" : "男" }}</span>
              <span class="span">年龄：{{ baseInfo.age }}</span>
            </div>
            <div>
              <el-link type="primary" class="mgr_20" @click="getTplList('show')">病历模板</el-link>
              <el-link type="primary" @click="handleSettings">病历设置</el-link>
            </div>
          </div>
          <!-- <span class="span">身份证号：{{ baseInfo.idCard }}</span>
          <span class="span">婚姻状况：{{ baseInfo.maritalStatus==0?'未婚':(baseInfo.maritalStatus==1?'已婚':'未知') }}</span> -->
        </el-form-item>
        <el-form-item v-if="isSetTpl" label="模版名称:" prop="name">
          <el-input v-model="caseForm.name" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item v-if="settings[0].enabled" label="主诉:" prop="mainComplaint">
          <el-input v-model="caseForm.mainComplaint" type="textarea" maxlength="1000" show-word-limit @input="caseForm.mainComplaint = $event"></el-input>
        </el-form-item>
        <el-form-item v-if="settings[9].enabled" label="个人史:" prop="personalHistory">
          <el-input v-model="caseForm.personalHistory" type="textarea" maxlength="1000" show-word-limit @input="caseForm.personalHistory = $event"></el-input>
        </el-form-item>
        <el-form-item v-if="settings[1].enabled" label="现病史:" prop="presentDisease">
          <el-input v-model="caseForm.presentDisease" type="textarea" maxlength="1000" show-word-limit @input="caseForm.presentDisease = $event"></el-input>
        </el-form-item>
        <el-form-item v-if="settings[2].enabled" label="既往史:" prop="pastHistory">
          <el-input v-model="caseForm.pastHistory" class="mgb_20" type="textarea" @input="caseForm.pastHistory = $event"></el-input>
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- <span>常用输入:</span> -->
              <el-button v-for="(item, index) in pastHistory" :key="index" class="mgb_10" type="primary" plain @click="pastHistoryClick(item)">{{ item }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="settings[3].enabled" label="过敏史:" prop="allergy">
          <el-input v-model="caseForm.allergy" class="mgb_20" type="textarea"></el-input>
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- <span>常用输入:</span> -->
              <el-button v-for="(item, index) in allergy" :key="index" class="mgb_10" type="primary" plain @click="allergyClick(item)">{{ item }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="settings[4].enabled" label="家族史:" prop="pastFamily">
          <el-input v-model="caseForm.pastFamily" :value="caseForm.pastFamily" class="mgb_20" type="textarea"></el-input>
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- <span>常用输入:</span> -->
              <el-button v-for="(item, index) in pastFamily" :key="index" class="mgb_10" type="primary" plain @click="pastFamilyClick(item)">{{ item }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="inquirerGender == 0 && settings[5].enabled" label="月经史:" prop="menstrual">
          <el-radio-group v-model="caseForm.status" @change="menstrualClick">
            <el-radio :label="0">未初潮</el-radio>
            <el-radio :label="1">已初潮</el-radio>
            <el-radio :label="2">已绝经</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row v-if="inquirerGender == 0 && settings[5].enabled && menstrualShow" :gutter="20">
          <el-col :span="5">
            <el-form-item label="初潮年龄:">
              <el-select v-model="caseForm.firstAge" style="width:150px" clearable placeholder="请选择">
                <el-option v-for="(item, index) in menstrualAge" :key="index" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="月经周期:">
              <el-select v-model="caseForm.cycle" style="width:150px" clearable placeholder="请选择">
                <el-option v-for="(item, index) in menstrualCycle" :key="index" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="行经天数:">
              <el-select v-model="caseForm.processDays" style="width:150px" clearable placeholder="请选择">
                <el-option v-for="(item, index) in menstrualDay" :key="index" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="是否痛经:">
              <el-select v-model="caseForm.dysmenorrhea" style="width:150px" placeholder="请选择是否痛经">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item v-if="settings[6].enabled" label="检查指标:">
          <el-row :gutter="20">
            <el-col :span="4">
              <el-form-item prop="temperature">
                <div class="flex_m">
                  <span class="mgr_5">体温</span>
                  <el-input v-model="caseForm.temperature" class="flex1"></el-input>
                  <span class="mgl_5">℃</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item prop="weight">
                <div class="flex_m">
                  <span class="mgr_5">体重</span>
                  <el-input v-model="caseForm.weight" class="flex1"></el-input>
                  <span class="mgl_5">kg</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item prop="heartrate">
                <div class="flex_m">
                  <span class="mgr_5">心率</span>
                  <el-input v-model="caseForm.heartRete" class="flex1"></el-input>
                  <span class="mgl_5">bpm</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item prop="systolicPressure">
                <div class="flex_m">
                  <span class="mgr_5">收缩压</span>
                  <el-input v-model="caseForm.systolic" class="flex1"></el-input>
                  <span class="mgl_5">mmHg</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item prop="diastolicPressure">
                <div class="flex_m">
                  <span class="mgr_5">舒张压</span>
                  <el-input v-model="caseForm.diastole" class="flex1"></el-input>
                  <span class="mgl_5">mmHg</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="settings[6].enabled" label="阳性体征:">
          <el-input v-model.trim="caseForm.positiveSigns" maxlength="1000" type="textarea" show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="settings[6].enabled" label="必要阴性体征:">
          <el-input v-model.trim="caseForm.negativeSigns" maxlength="1000" type="textarea" show-word-limit></el-input>
        </el-form-item> -->
        <el-form-item v-if="settings[6].enabled" label="辅助检查:">
          <el-input v-model.trim="caseForm.moreExamin" maxlength="1000" type="textarea" show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="settings[7].enabled" label="临床诊断:" prop="diagnosisList">
          <el-select v-model="caseForm.diagnosisList" multiple filterable remote reserve-keyword placeholder="请输入关键词" :loading="searchLoading" :remote-method="remoteMethod" style="width:300px;">
            <el-option v-for="(item, index) in diagnoseOptions" :key="index" :label="item.label" :value="item.value">
              <div class="flex_m_c">
                <span>{{ item.optionLabel }}</span>
                                &nbsp;&nbsp;
                <span>{{ item.optionCode }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="settings[8].enabled" label="治疗意见:" prop="treatmentOptions">
          <el-input v-model.trim="caseForm.treatmentOptions" class="mgb_20" type="textarea"></el-input>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button v-for="(item, index) in treatmentPresetOptions" :key="index" class="mgb_10" type="primary" plain @click="presetOptions(item)">{{ item }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isSetTpl" type="primary" @click="handleTemplateSave('caseForm')">保存为病历模版</el-button>
        <!-- <el-button type="primary" @click="handleSave('caseForm')">保存为病历模版</el-button> -->
        <el-button type="primary" @click="isSetTpl ? handleTemplate('caseForm', 'save') : submitCaseForm('caseForm')">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="开具处方" :visible.sync="prescripVisible" width="80%" top="5vh">
      <el-form ref="prescripForm" :model="prescripForm" :rules="prescripFormRules" label-width="100px">
        <el-form-item label="就诊人信息:">
          <span class="span">姓名：{{ baseInfo.name }}</span>
          <span class="span">性别：{{ baseInfo.gender == 0 ? "女" : "男" }}</span>
          <span class="span">年龄：{{ baseInfo.age }}</span>
          <!-- <span class="span">身份证号：{{ baseInfo.idCard }}</span>
          <span class="span">婚姻状况：{{ baseInfo.maritalStatus==0?'未婚':(baseInfo.maritalStatus==1?'已婚':'未知') }}</span> -->
        </el-form-item>
        <el-form-item label="临床诊断:" prop="diagnosis">
          <el-select v-model="prescripForm.diagnosis" multiple filterable remote reserve-keyword placeholder="请输入关键词" :loading="searchLoading" :remote-method="remoteMethod" style="width:300px;">
            <el-option v-for="(item, index) in diagnoseOptions" :key="index" :label="item.label" :value="item.value">
              <div class="flex_m_c">
                <span>{{ item.optionLabel }}</span>
                                &nbsp;&nbsp;
                <span>{{ item.optionCode }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="添加药品:">
          <el-button type="primary" @click="addDrug">添加药品</el-button>
        </el-form-item>
        <el-form-item label="药品列表:">
          <el-table :data="drugList" fit highlight-current-row style="width: 100%">
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="药品名称" prop="productName" align="center" />
            <el-table-column label="用法用量" prop="usageText" align="center" />
            <el-table-column label="用法周期" prop="usageText" align="center">
              <template slot-scope="{ row }"> {{ row.drugCycle }}{{ row.drugCycleUnit }} </template>
            </el-table-column>
            <el-table-column label="数量" prop="quantity" width="200px" align="center" />
            <el-table-column label="操作" width="200px" align="center">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click.native.prevent="editRow(scope.$index, drugList)">编辑 </el-button>
                <el-button type="danger" size="mini" @click.native.prevent="deleteRow(scope.$index, drugList)">删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPrescripForm('prescripForm')">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="drugVisible" width="50%" top="5vh" @close="resetForm('drugForm')">
      <el-form ref="drugForm" :model="drugForm" :rules="drugFormRules" label-width="100px">
        <el-form-item label="药品名称:" prop="productName">
          <el-select v-model="drugForm.productName" filterable remote reserve-keyword placeholder="请输入药品" :disabled="disabled" :loading="searchLoading" :remote-method="drugRemoteMethod" style="width:400px;" @change="drugChange">
            <el-option v-for="(item, index) in drugOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="药品用量:" prop="dosageStr">
          <el-select v-model="drugForm.dosageStr" placeholder="请选择药品用量" style="width:400px;" @change="dosageChange">
            <el-option v-for="(item, index) in dosageOptions" :key="index" :label="item.dosageStr" :value="index"> </el-option>
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="20" style="display:flex">
            <el-form-item label="单次剂量:" prop="eachDosageCount">
              <el-input v-model="drugForm.eachDosageCount" placeholder="请输入数量" style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item label="剂量单位:" prop="eachDoseUnit">
              <el-select v-model="drugForm.eachDoseUnit" placeholder="请选择单位" style="width:150px;">
                <el-option v-for="(item, index) in unitOptions" :key="index" :label="item" :value="item"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="药品用法:" prop="usageVal">
          <el-select v-model="drugForm.usageVal" placeholder="请选择药品用法" style="width:400px;" @change="usageChange">
            <el-option v-for="(item, index) in usageOptions" :key="index" :label="item" :value="index"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="药品周期:" prop="drugCycle">
          <el-row>
            <el-col>
              <el-input v-model="drugForm.drugCycle" placeholder="请输入用药周期" style="width:250px;" onkeyup="value=value.replace(/[^\d]/g,'')" @keyup.native="getQuantity"></el-input>
              <span>天</span>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="药品数量:" prop="quantity">
          <el-row>
            <el-col>
              <el-input v-model="drugForm.quantity" placeholder="请输入数量" style="width:250px;"></el-input>
              <span>{{ drugForm.quantityUnit }}</span>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="drugForm.backup" type="textarea" style="width:400px;"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSrueDurg('drugForm')">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="处方列表" :visible.sync="prescripListVisible" width="50%" top="5vh">
      <el-table :data="prescripList" fit highlight-current-row style="width: 100%">
        <el-table-column label="编号" prop="serialNumber" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <el-button type="primary" size="mini" @click.native.prevent="prescripPdfUrl(row.pdfUrl)">查看 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="prescripListVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="病历签署" :visible.sync="dialogSignedVisible" width="70%" :show-close="false">
      <div class="dialog-content">
        <iframe :src="qm" frameborder="0" width="100%" height="600px"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="!signBtnShow" type="primary" @click="closeQm">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="处方签署" :visible.sync="dialogCfSignedVisible" width="70%" :show-close="false">
      <div class="dialog-content">
        <iframe :src="qm" frameborder="0" width="100%" height="600px"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="!signBtnShow" type="primary" @click="closeQm">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="会诊意见签署" :visible.sync="dialogOpinionSignedVisible" width="70%" :show-close="false">
      <div class="dialog-content">
        <iframe :src="qm" frameborder="0" width="100%" height="600px"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="!signBtnShow" type="primary" @click="closeQm">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="四川CA" :visible.sync="dialogCAVisible" width="70%" :show-close="false">
      <el-form ref="CAForm" :model="CAForm" :rules="CARules" label-width="80px" class="demo-ruleForm">
        <el-form-item label="密码" prop="password">
          <el-input v-model="CAForm.password" type="password"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="CASumbit('CAForm')">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog title="保存为病历模板" :visible.sync="dialogVisible" width="30%">
      <el-input v-model.trim="caseForm.name" placeholder="请输入模版名称" maxlength="20" type="textarea" show-word-limit></el-input>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleTemplate('caseForm', 'add')">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="病历设置" :visible.sync="dialogSetingVisible" width="30%">
      <el-checkbox-group v-model="checkedCities">
        <el-checkbox v-for="item in settings" :key="item.type" :disabled="item.type == 1 || item.type == 8 || item.type == 9" :label="item.type">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer">
        <el-button @click="dialogSetingVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSeting">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="病历模板" :visible.sync="dialogTemplateVisible" width="50%" height="70vh">
      <div class="tpl">
        <div class="tpl-scroll-view">
          <div>
            <el-button type="primary" class="mgt_20" @click="handleAddtpl">新增模版</el-button>
            <div v-if="tplList.length > 0" class="scroll-view">
              <a v-for="(item, index) in tplList" :key="index" class="mgt_20" :class="current === index ? 'tpl_active' : ''" @click="handleSwatch(item.id, index)">{{ item.name }}</a>
            </div>
          </div>
          <pagination v-show="tplTotal > 0" small layout="prev, pager, next" :total="tplTotal" :page.sync="tplListQuery.page" :limit.sync="tplListQuery.num" @pagination="getTplList('loadmore')" />
        </div>
        <div v-if="tplList.length > 0" class="flex1">
          <div class="tpl-content">
            <div class="tpl-content-title p20 bb">
              <div class="flex1 title">{{ tplList[current].name }}</div>
              <el-button type="danger" @click="handleDeleteFn">删除</el-button>
              <el-button @click="handleEdittpl">编辑</el-button>
            </div>
            <div v-if="(setting && setting[0].enabled) || !setting" class="flex bb">
              <span class="label p20 br">主诉</span>
              <span class="flex1 p20 label-value">{{ tplInfo.mainComplaint }}</span>
            </div>
            <div v-if="(setting && setting[9].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 个人史</span>
              <span class="flex1 p20 label-value"> {{ tplInfo.personalHistory }}</span>
            </div>
            <div v-if="(setting && setting[1].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 现病史</span>
              <span class="flex1 p20 label-value"> {{ tplInfo.presentDisease }}</span>
            </div>
            <div v-if="(setting && setting[2].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 既往史</span>
              <span class="flex1 p20 label-value"> {{ tplInfo.pastHistory }}</span>
            </div>
            <div v-if="(setting && setting[3].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 过敏史</span>
              <span class="flex1 p20 label-value"> {{ tplInfo.allergy }}</span>
            </div>
            <div v-if="(setting && setting[4].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 家庭史</span>
              <span class="flex1 p20 label-value"> {{ tplInfo.pastFamily }}</span>
            </div>
            <div v-if="(setting && setting[5].enabled) || !setting" class="flex bb">
              <span class="label p20 br"> 月经史</span>
              <div class="flex1 p20 label-value">
                <span style="margin-bottom:6px;">月经情况：{{ tplInfo.status ? tplInfo.status : "无" }}</span>
                <span style="margin-bottom:6px;">初潮年龄：{{ tplInfo.firstAge ? tplInfo.firstAge + "岁" : "无" }}</span>
                <span style="margin-bottom:6px;">月经周期：{{ tplInfo.cycle ? tplInfo.cycle + "天" : "无" }}</span>
                <span style="margin-bottom:6px;">行经天数：{{ tplInfo.processDays ? tplInfo.processDays + "天" : "无" }}</span>
                <span style="margin-bottom:6px;">是否痛经：{{ tplInfo.dysmenorrhea == 1 ? "是" : "否" }}</span>
                <span v-if="tplInfo.dysmenorrhea == 1" style="margin-bottom:6px;">痛经部位：{{ tplInfo.part }}</span>
              </div>
            </div>
            <!-- <div v-if="setting && setting[6].enabled ||!setting" class="flex bb">
              <span class="label p20 br"> 检查指标</span>
              <span class="flex1 p20 label-value">体温：{{ tplInfo.temperature }} 体重：{{ tplInfo.weight }}kg  心率：{{ tplInfo.heartRete }}bpm  收缩压：{{ tplInfo.systolic }} 舒张压：{{ tplInfo.diastole }}</span>
            </div>
            <div v-if="setting && setting[6].enabled ||!setting" class="flex bb">
              <span class="label p20 br"> 阳性指标</span>
              <span class="flex1 p20 label-value">{{ tplInfo.positiveSigns }}</span>
            </div>
            <div v-if="setting && setting[6].enabled ||!setting" class="flex bb">
              <span class="label p20 br"> 必要的阴性指标</span>
              <span class="flex1 p20 label-value">{{ tplInfo.negativeSigns }}</span>
            </div> -->
            <div v-if="(setting && setting[6].enabled) || !setting" class="bb flex">
              <span class="label p20 br"> 辅助检查</span>
              <span class="flex1 p20 label-value">{{ tplInfo.moreExamin }}</span>
            </div>
            <div v-if="(setting && setting[7].enabled) || !setting" class="bb flex">
              <span class="label p20 br"> 诊断</span>
              <span class="flex1 p20 label-value">{{ tplInfo.diagnosisStr }}</span>
            </div>
            <div v-if="(setting && setting[8].enabled) || !setting" class="bb flex">
              <span class="label p20 br"> 治疗意见</span>
              <span class="flex1 p20 label-value">{{ tplInfo.treatmentOptions }}</span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="dialogTemplateVisible = false">取 消</el-button>
        <el-button v-if="tplList.length > 0" type="primary" @click="handleEmploy">使 用</el-button>
      </div>
    </el-dialog>
    <div class="mask" :class="isUrl ? 'maskTran' : ''">
      <h2>就诊记录 <i class="el-icon-close" @click="(isUrl = false), (url = null)"></i></h2>
      <iframe v-if="url" id="iframe_chat" ref="iframe_chat" :src="url" frameborder="0" class="iframe_chat"></iframe>
    </div>
    <el-dialog title="会诊申请" :visible.sync="applyMeetingShow" width="70%" :show-close="true">
      <el-form ref="meetingForm" :model="applyMeetingForm" :rules="applyMeetingFormRules" label-width="20px">
        <div>
          <div class="meet-item b">就诊人信息</div>
          <div class="meet-item">姓名：{{ applyMeetingForm.inquirerName }} 性别：{{ applyMeetingForm.inquirerGender == 0 ? "女" : "男" }} 年龄：{{ applyMeetingForm.inquirerAgeStr }} 科室：{{ applyMeetingForm.departmentName }}</div>
          <div class="meet-item b">会诊原由：（病案摘要及会诊目的）</div>
          <el-form-item class="meet-item" prop="groupConsultReason">
            <el-input v-model="applyMeetingForm.groupConsultReason" :disabled="isApplyMeetingEdit" type="textarea"></el-input>
          </el-form-item>
          <div class="meet-item b">
            会诊医师：
          </div>
          <!-- <div class="meet-item">医院：{{ applyMeetingForm.targetHospitalName }}</div>
          <div class="meet-item">科室：{{ applyMeetingForm.departmentName }}</div> -->

          <!-- 医院列表 -->
          <div class="meet-item flex_m">
            <div>医院：</div>
            <el-form-item label-width="10px" prop="hospitalId">
              <el-select
                v-model="applyMeetingForm.hospitalId"
                :disabled="isApplyMeetingEdit"
                filterable
                clearable
                placeholder="请选择医院"
                @clear="clearHospital"
                @change="hospitalChange"
              >
                <el-option v-for="(item, index) in hospitalList" :key="index" :label="item.hospitalName" :value="item.hospitalId"> </el-option>
              </el-select>
            </el-form-item>
            <!-- <div class="pl_10">科室：{{ applyMeetingForm.departmentName }}</div> -->
          </div>

          <div class="meet-item flex_m">
            <div>医生：</div>
            <el-form-item label-width="10px" prop="targetDoctorId">
              <el-select v-model="applyMeetingForm.targetDoctorId" :disabled="isApplyMeetingEdit" placeholder="请选择会诊专家">
                <el-option v-for="(item, index) in targetDoctorList" :key="index" :label="item.name + '-' + item.departmentName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <!-- <span class="red">（{{ applyMeetingForm.targetHospitalName }}可以根据患者病情调整专家）</span> -->
            <div class="pl_10">联系电话：{{ doctorPhone }}</div>
            <div v-if="isApplyMeetingEdit" class="pl_10">科室：{{ applyMeetingForm.targetDepartmentName }}</div>
            <div v-else class="pl_10">科室：{{ targetDepartmentName }}</div>
          </div>
          <div class="meet-item flex_m">
            <div class="b">会诊时间：</div>
            <el-date-picker v-model="applyMeetingForm.groupConsultDate" class="date-select" :clearable="false" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions"> </el-date-picker>
            <el-time-select
              v-model="applyMeetingForm.groupConsultTimeStart"
              class="time-select"
              :picker-options="{
                start: timeOptions.groupConsultTimeStart,
                step: timeOptions.timeStep,
                end: '23:30'
              }"
              :clearable="false"
              placeholder="选择开始时间"
              @change="groupConsultTimeStartChange"
            >
            </el-time-select>
            <el-time-select
              v-model="applyMeetingForm.groupConsultTimeEnd"
              :clearable="false"
              class="time-select"
              :picker-options="{
                start: timeOptions.groupConsultTimeEnd,
                step: timeOptions.timeStep,
                end: '24:00'
              }"
              placeholder="选择结束时间"
            >
            </el-time-select>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="meettingLoading" @click="submitMeeting">提交会诊申请</el-button>
      </div>
    </el-dialog>
    <!-- <el-dialog :title="company+'远程会诊资料'"  class="meeting-dialog" :visible.sync="meetingInfoShow" width="800px" :show-close="true">
      <div class="meeting-info">
        <h3>会诊信息：</h3>
        <div class="inner-item">
          <div>患者：{{ meetingInfo.inquirerInfo.name }} 性别：{{ meetingInfo.inquirerInfo.gender==0?'女':'男' }}    年龄：{{ meetingInfo.inquirerInfo.age }}</div>
          <div class="pt_10">科室：{{ meetingInfo.departmentName }}</div>
          <div class="pt_10">会诊原由：（病案摘要及会诊目的）</div>
          <div class="pt_10">
            {{ meetingInfo.groupConsultReason }}
          </div>
        </div>
        <h3 class="flex_none">受邀专家意见：</h3>
        <div class="inner-item flex">
          <div class="w_100">
            <div>{{ meetingInfo.targetOpinion }}</div>
            <div class="pt_10 tr"><div>专家签名：{{ meetingInfo.targetDoctorName }} </div><div class="pt_10">时间：{{ meetingInfo.targetOpinionAt }}</div></div>
          </div>
        </div>
        <h3 class="flex_none">发起医师意见：</h3>
        <div class="inner-item flex">
          <div class="w_100">
            <div>{{ meetingInfo.sourceOpinion }}</div>
            <div class="pt_10 tr"><div>医师签字：{{ meetingInfo.doctorName }} </div><div class="pt_10">时间：{{ meetingInfo.sourceOpinionAt }}</div></div>
          </div>
        </div>
        <h3 class="flex_none">临床诊断：</h3>
        <div class="inner-item flex">
          <div class="w_100">
            <div class="pt_10">{{ meetingInfo.diagnoseData }}</div>
          </div>
        </div>
      </div>
    </el-dialog> -->
    <!-- pdf预览 -->
    <el-dialog :title="company + '远程会诊资料'" :visible.sync="meetingInfoShow" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border:0;"></iframe>
      </div>
    </el-dialog>
    <el-dialog title="远程会诊意见" :visible.sync="suggestShow" width="70%" :show-close="true">
      <el-form ref="suggestForm" :model="suggestForm" :rules="suggestFormRules" label-width="120px">
        <template v-if="suggestForm.from == 2">
          <el-form-item label="受邀专家意见：">
            <span>{{ rowItem.targetOpinion }}</span>
          </el-form-item>
          <div class="tr pb_10">专家签名：{{ rowItem.targetDoctorName }} 时间：{{ rowItem.targetOpinionAt }}</div>
          <el-form-item label="发起医师意见：" prop="opinion">
            <el-input v-model="suggestForm.opinion" type="textarea" placeholder="请输入您的会诊意见"></el-input>
          </el-form-item>
          <el-form-item label="临床诊断：" prop="diagnoseData">
            <el-select v-model="suggestForm.diagnoseData" multiple filterable remote reserve-keyword placeholder="请输入关键词" :loading="searchLoading" :remote-method="remoteMethod" style="width:300px;">
              <el-option v-for="(item, index) in diagnoseOptions" :key="index" :label="item.label" :value="item.value">
                <div class="flex_m_c">
                  <span>{{ item.optionLabel }}</span>
                                    &nbsp;&nbsp;
                  <span>{{ item.optionCode }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="受邀专家意见：" prop="opinion">
            <el-input v-model="suggestForm.opinion" type="textarea" placeholder="请输入您的会诊意见"></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="suggestLoading" @click="submitSuggest">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
@import "../../assets/css/doctor";
@import "../../assets/css/webRTC";
.flex {
    display: flex;
}
.flex_m {
    display: flex !important;
    align-items: center !important;
}
.flex_m_c {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flex1 {
    flex: 1;
}
.bl {
    border-left: 1px solid #ebeef5;
}
.bb {
    border-bottom: 1px solid #ebeef5;
}
.bt {
    border-top: 1px solid #ebeef5;
}
.br {
    border-right: 1px solid #ebeef5;
}
.p20 {
    padding: 20px;
}
.mgl_5 {
    margin-left: 5px;
}
.mgr_5 {
    margin-right: 5px;
}
.mgl_10 {
    margin-left: 10px;
}
.mgr_10 {
    margin-right: 10px;
}
.mgt_20 {
    margin-top: 20px;
}
.header {
    display: flex;
    align-items: center;
}
.header .title {
    text-align: center;
}
.scroll-view {
    max-height: 500px;
    overflow: auto;
    display: flex;
    flex-direction: column;
}
.scroll-view > div {
    margin-bottom: 10px;
    font-size: 15px;
}
.label {
    width: 150px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
}
.label-value {
    color: #333333;
    font-size: 14px;
}
.tpl {
    height: 100%;
    display: flex;
}
.tpl-content {
    width: 100%;
    font-size: 16px;
    border: 1px solid #ebeef5;
    background: #f7f9fd;
    border-radius: 4px;
}
.tpl-scroll-view {
    width: 150px;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.tpl-content-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.tpl-content-title div {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
}
.scroll-view .tpl_active {
    color: #389aff;
}
</style>
<script>
// import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'
import API from '@/api/doctor/index'
import Settings from '@/settings'
import { Loading } from 'element-ui'
import { dateFormat } from '@/utils/date'
export default {
  name: 'Project',
  components: {},
  data() {
    var checkTemperature = (rule, value, callback) => {
      setTimeout(() => {
        if (value) {
          if (value < 35 || value > 45) {
            callback(new Error('体温范围为35-45℃'))
          } else {
            callback()
          }
        }
        callback()
      }, 100)
    }
    var checkWeight = (rule, value, callback) => {
      setTimeout(() => {
        if (value) {
          if (value < 30 || value > 299) {
            callback(new Error('体重范围为30-299kg'))
          } else {
            callback()
          }
        }
        callback()
      }, 100)
    }
    var checkHeartRate = (rule, value, callback) => {
      setTimeout(() => {
        var pattern = /[\.]/
        if (this.caseForm.heartRete) {
          if (pattern.test(this.caseForm.heartRete)) {
            callback(new Error('心率范围为20-249次'))
          }
          if (this.caseForm.heartRete < 20 || this.caseForm.heartRete > 249) {
            callback(new Error('心率范围为20-249次/分'))
          } else {
            callback()
          }
        }
        callback()
      }, 100)
    }
    var checksystolicPressure = (rule, value, callback) => {
      setTimeout(() => {
        var pattern = /[\.]/
        if (this.caseForm.systolic) {
          if (pattern.test(this.caseForm.systolic)) {
            callback(new Error('请输入20-300的整数'))
          }
          if (this.caseForm.systolic < 20 || this.caseForm.systolic > 300) {
            callback(new Error('收缩压范围为20-300mmHg'))
          } else {
            callback()
          }
        }
        callback()
      }, 100)
    }
    var checkdiastolicPressure = (rule, value, callback) => {
      setTimeout(() => {
        var pattern = /[\.]/
        if (this.caseForm.diastole) {
          if (pattern.test(this.caseForm.diastole)) {
            callback(new Error('请输入20-300的整数'))
          }
          if (this.caseForm.diastole < 20 || this.caseForm.diastole > 300) {
            callback(new Error('舒张压范围为20-300mmHg'))
          } else {
            callback()
          }
        }
        callback()
      }, 100)
    }
    var checkVal = (rule, value, callback) => {
      setTimeout(() => {
        var pattern = /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
        if (value < 1 && !pattern.test(value)) {
          callback(new Error('请输入大于0的整数'))
        }
        if (value) {
          if (!Number.isInteger(parseInt(value))) {
            callback(new Error('请输入数字值'))
          }
          if (Math.ceil(value) > 999) {
            callback(new Error('请输入有效数值'))
          }
          if (!pattern.test(value)) {
            callback(new Error('只保留两位小数'))
          }
          console.log(this.eachDoseUnit, this.drugForm.eachDoseUnit)
          if (this.drugForm.medicineMinUnit === this.drugForm.eachDoseUnit) {
            if (this.drugForm.doseMax && rule.field === 'eachDosageCount' && value / 1 > this.drugForm.doseMax / 1) {
              callback(new Error(`不能超过该药品的安全用药范围：每次${this.drugForm.doseMin}-${this.drugForm.doseMax}${this.drugForm.medicineMinUnit}`))
            }
            if (this.drugForm.doseMin && rule.field === 'eachDosageCount' && value / 1 < this.drugForm.doseMin / 1) {
              callback(new Error(`不能超过该药品的安全用药范围：每次${this.drugForm.doseMin}-${this.drugForm.doseMax}${this.drugForm.medicineMinUnit}`))
            }
          }
          if (this.drugForm.medicationCycleMax && rule.field === 'drugCycle' && value / 1 > this.drugForm.medicationCycleMax / 1) {
            callback(new Error(`药品周期不能超过该药品安全用药周期：${this.drugForm.medicationCycleMax}天`))
          }
        }
        callback()
      }, 100)
    }
    // eslint-disable-next-line no-unused-vars
    var checkVal2 = (rule, value, callback) => {
      setTimeout(() => {
        var space = /\s+/
        if (!space.test(value)) {
          callback(new Error('输入框不能输入空格！'))
        }
        callback()
      }, 100)
    }
    return {
      formLabelWidth: '120px',
      title: Settings.title,
      currentIndex: -1,
      refuseDisabled: true,
      acceptsDisabled: true,
      listShow: true,
      videoConsultId: '',
      doctorId: null,
      doctorName: '',
      patientName: '',
      inquirerGender: '',
      userId: 'user_001',
      roomId: '13',
      value: '',
      ListQuery: {
        page: 1,
        status: 1
      },
      total: null,
      currentPage: null,
      list: [],
      radio: '',
      radioList: [],
      dialogVisible: false,
      dialogSetingVisible: false,
      dialogTemplateVisible: false,
      fullscreenLoading: false,
      reasonDialogVisible: false,
      dialogTemplateTitle: '完善病历',
      reason: '',
      reasonShow: false,
      tabs: [{ tabName: '待接诊' }, { tabName: '远程会诊' }, { tabName: '已完成' }],
      nowIndex: 0,
      perfectCaseVisible: false,
      caseForm: {
        doctorId: '',
        inquirerId: '',
        mainComplaint: '',
        personalHistory: '',
        presentDisease: '',
        pastHistory: '',
        allergy: '',
        pastFamily: '',
        status: '',
        firstAge: '',
        cycle: '',
        processDays: '',
        dysmenorrhea: '',
        diagnosisList: [],
        treatmentOptions: ''
      }, //完善病历所需参数

      caseFormRules: {
        name: [{ required: true, message: '请填模版名称', trigger: 'blur' }],
        mainComplaint: [{ required: true, message: '请填写主诉', trigger: 'blur' }],
        // personalHistory: [
        //   { required: true, message: '请填写个人史', trigger: 'blur' }
        // ],
        presentDisease: [{ required: true, message: '请填写现病史', trigger: 'blur' }],
        pastHistory: [{ required: true, message: '请填写既往史', trigger: 'blur' }],
        allergy: [{ required: true, message: '请填写过敏史', trigger: 'blur' }],
        pastFamily: [{ required: true, message: '请填写家庭史', trigger: 'blur' }],
        temperature: [{ required: true, validator: checkTemperature, trigger: 'blur' }],
        weight: [{ required: true, validator: checkWeight, trigger: 'blur' }],
        heartrate: [{ required: true, validator: checkHeartRate, trigger: 'blur' }],
        systolicPressure: [{ required: true, validator: checksystolicPressure, trigger: 'blur' }],
        diastolicPressure: [{ required: true, validator: checkdiastolicPressure, trigger: 'blur' }],
        treatmentOptions: [{ required: true, message: '请填写治疗意见', trigger: 'blur' }],
        diagnosisList: [{ required: true, message: '请选择临床诊断', trigger: 'blur' }]
      },
      baseInfo: {},
      treatmentPresetOptions: [], //诊断选项
      pastHistory: [], //既往史
      allergy: [], //过敏史
      pastFamily: [], //家庭史
      menstrualAge: [], //月经-初潮年龄
      menstrualCycle: [], //月经-月经周期
      menstrualDay: [], //月经-行经天数
      searchLoading: false,
      diagnoseData: [],
      diagnoseOptions: [],
      drugOptions: [],
      optionsArr: [],
      menstrualShow: false,
      prescripVisible: false,
      prescripForm: {
        //开具处方所需参数
        doctorId: '',
        inquirerId: '',
        diagnosis: [],
        items: [],
        type: 1,
        timestamp: ''
      },
      prescripFormRules: {
        diagnosis: [{ required: true, message: '请选择临床诊断', trigger: 'blur' }]
      },
      drugVisible: false,
      selDisabled: false,
      textMap: {
        create: '添加药品',
        update: '编辑药品'
      },
      dialogStatus: '',
      drugForm: {
        productName: '',
        dosageStr: '',
        eachDosageCount: '',
        eachDoseUnit: '',
        usageVal: '',
        usageTime: '',
        usageMethod: '',
        quantity: '',
        quantityUnit: '盒',
        drugCycle: '',
        drugCycleUnit: '天',
        backup: ''
      },
      drugFormRules: {
        productName: [{ required: true, message: '请选择药品', trigger: 'change' }],
        dosageStr: [{ required: true, message: '请选择药品用量', trigger: 'change' }],
        eachDosageCount: [{ required: true, validator: checkVal, trigger: 'blur' }],
        eachDoseUnit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        drugCycle: [{ required: true, validator: checkVal, trigger: 'blur' }],
        usageVal: [{ required: true, message: '请选择药品用法', trigger: 'change' }],
        quantity: [{ required: true, validator: checkVal, trigger: 'blur' }]
      },
      dosageOptions: [], //药品用量
      unitOptions: [], //单位
      usageOptionsArr: [],
      usageOptions: [], //药品用法
      drugList: [],
      prescripListVisible: false,
      prescripList: [],
      dialogSignedVisible: false,
      qm: '',
      uniqueId: '',
      recordId: '', //病历recordId
      recomId: '', //处方recomId
      dialogCfSignedVisible: false,
      disabled: false,
      defaultUsage: null,
      searchObj: {},
      pIndex: '',
      noSecret: null, //是否免密
      seviceName: null, //签名服务供应商：2（E签宝）,3四川ca
      dialogCAVisible: false,
      CAForm: {
        password: ''
      },
      CARules: {
        password: [{ required: true, message: '请填写密码', trigger: 'blur' }]
      },
      isSetTpl: false, //是否添加模板
      current: 0,
      tplListQuery: {
        page: 1,
        status: 1,
        num: 20
      },
      tplList: [],
      tplTotal: 0,
      checkedCities: [],
      settings: [],
      templateId: '',
      tplInfo: {},
      company: process.env.VUE_APP_BASE_COMPANY,
      setting: null,
      isAdd: false,
      isUrl: false,
      url: '',
      applyMeetingShow: false,
      applyMeetingForm: {
        //申请会诊所需参数
        inquirerInfo: {},
        targetDoctor: '',
        targetDoctorId: '',
        groupConsultReason: '',
        groupConsultDate: '',
        groupConsultTimeStart: '00:00',
        groupConsultTimeEnd: '00:30',
        hospitalId: '' //医院id
      },
      hospitalList: [], // 医院列表
      targetDoctorList: [], // 医生列表
      isApplyMeetingEdit: false,
      applyMeetingFormRules: {
        groupConsultReason: [{ required: true, message: '请填写原因', trigger: 'change' }],
        targetDoctorId: [{ required: true, message: '请选择医生', trigger: 'change' }],
        hospitalId: [{ required: true, message: '请选择医院', trigger: 'change' }]
      },
      // meetingRadio: true,
      doctorList: ['张三'],
      timeOptions: {
        timeStep: '00:30',
        groupConsultTimeStart: '00:00',
        groupConsultTimeEnd: '00:30'
      },
      meettingLoading: false,
      meetingInfoShow: false,
      meetingInfo: {
        inquirerInfo: {}
      },
      rowItem: {},
      suggestShow: false,
      suggestForm: {
        //申请会诊所需参数
        opinion: '',
        from: 1,
        roomId: 0,
        diagnosisList: []
      },
      suggestFormRules: {
        opinion: [{ required: true, message: '请填写会诊意见', trigger: 'change' }],
        diagnosisList: [{ type: 'array', required: true, message: '请至少选择诊断', trigger: 'change' }]
      },
      suggestLoading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      dialogOpinionSignedVisible: false,
      pdfsrc: '',
      signBtnShow: true
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    doctorPhone() {
      if (!this.applyMeetingForm.targetDoctorId || !this.targetDoctorList) {
        return ''
      } else {
        const doctorItem = this.targetDoctorList.find(item => {
          return item.id === this.applyMeetingForm.targetDoctorId
        })
        return doctorItem && doctorItem.phone
      }
    },
    targetDepartmentName() {
      if (!this.applyMeetingForm.targetDoctorId || !this.targetDoctorList) {
        return ''
      } else {
        const doctorItem = this.targetDoctorList.find(item => {
          return item.id === this.applyMeetingForm.targetDoctorId
        })
        return doctorItem && doctorItem.departmentName
      }
    }
  },
  watch: {},
  created() {
    const status = +this.$route.query.Status
    if (status && status === 1) {
      this.nowIndex = 2
      this.ListQuery.status = 5
    } else if (status && status === 2) {
      this.nowIndex = 1
      this.ListQuery.status = 5
    } else {
      this.nowIndex = 0
    }
    this.getKey()
    this.getUsage()
    this.getListData()

    this.doctorId = this.userInfo.id
    this.ListQuery.doctorId = this.userInfo.id
    this.caseForm.doctorId = this.userInfo.id
    this.prescripForm.doctorId = this.userInfo.id
    this.doctorName = this.userInfo.name
    console.log('created', this.userInfo)
    console.log(this.$parent.$parent.$children[0].$children[0].$children[1])
    this.getNoSecret()
    this.fetchHospitalList()
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    // 医院列表
    fetchHospitalList() {
      API.getHospitalList().then(res => {
        this.hospitalList = res
      })
    },
    // 获取医生列表
    fetchDoctorList(hospitalId) {
      API.getHospDoctorList(hospitalId).then(res => {
        this.applyMeetingForm.targetDoctorId = ''
        this.targetDoctorList = res.length > 0 ? res : []
      })
    },
    // 选择医院
    hospitalChange(val) {
      if (val) {
        this.fetchDoctorList(val)
        const hospitalItem = this.hospitalList.find(item => {
          return item.hospitalId === val
        })
        this.applyMeetingForm.targetHospitalId = val
        this.applyMeetingForm.targetHospitalName = hospitalItem && hospitalItem.hospitalName
      }
    },
    // 清空医院
    clearHospital() {
      this.applyMeetingForm.targetHospitalId = ''
      this.applyMeetingForm.targetHospitalName = ''
      this.applyMeetingForm.targetDoctorId = ''
      this.targetDoctorList = []
    },
    initTime() {
      const currentDate = new Date()
      const minutes = currentDate.getMinutes()
      if (minutes > 30) {
        this.applyMeetingForm.groupConsultTimeStart = currentDate.getHours() + 1 + ':00'
        this.applyMeetingForm.groupConsultTimeEnd = currentDate.getHours() + 1 + ':30'
      } else {
        this.applyMeetingForm.groupConsultTimeStart = currentDate.getHours() + ':30'
        this.applyMeetingForm.groupConsultTimeEnd = currentDate.getHours() + 1 + ':00'
      }
      this.applyMeetingForm.groupConsultDate = dateFormat(currentDate, 'yyyy-MM-dd')
      this.timeOptions.groupConsultTimeStart = this.applyMeetingForm.groupConsultTimeStart
      this.timeOptions.groupConsultTimeEnd = this.applyMeetingForm.groupConsultTimeEnd
    },
    // 是否免签
    getNoSecret() {
      const params = {
        doctorId: this.doctorId
      }
      API.getNoSecret(params).then(response => {
        this.noSecret = response.noSecret
      })
    },

    // 带接诊/已完成
    toggleTabs(index) {
      this.nowIndex = index
      if (index === 0) {
        this.ListQuery.page = 1
        this.ListQuery.status = 1
      } else if (index === 1) {
        this.ListQuery.page = 1
        this.ListQuery.status = 1 //会诊 ，新接口没有会诊状态，所以暂时用1代替
      } else {
        this.ListQuery.page = 1
        this.ListQuery.status = 5
      }
      this.getListData()
    },
    offlineMedicalRecordUrl(url) {
      if (!url) {
        this.$message('暂无' + process.env.VUE_APP_BASE_COMPANY + '就诊记录！')
        return
      }
      this.url = url
      this.isUrl = true
      // alert('弹出框')
      // window.open(url)
    },

    // 诊断搜索
    remoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        const params = {
          doctorId: this.doctorId,
          key: query
        }
        API.getDiagnoseData(params).then(response => {
          this.searchLoading = false
          this.optionsArr = response.result.map(item => {
            return { value: `${item.name} ${item.mainCode}`, label: `${item.name} ${item.mainCode}`, optionCode: `${item.mainCode}`, optionLabel: `${item.name}` }
          })
          this.diagnoseOptions = this.optionsArr.filter(item => {
            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
          })
        })
      } else {
        this.diagnoseOptions = []
      }
    },

    // 药品搜索
    drugRemoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        const productSearch = {
          doctorId: this.doctorId,
          key: query
        }
        API.getDrugData(productSearch).then(response => {
          sessionStorage.setItem('searchArr', JSON.stringify(response.result))
          this.searchLoading = false
          this.optionsArr = response.result.map(item => {
            return { value: `${item.skuId}`, label: `${item.name}` }
          })
          this.drugOptions = this.optionsArr.filter(item => {
            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
          })
        })
      } else {
        this.drugOptions = []
      }
    },

    // 确认接诊
    acceptsCommon(type, isMeeting = 0) {
      this.patientName = this.rowItem.inquirerName
      this.roomId = this.rowItem.roomId
      this.videoConsultId = this.rowItem.videoConsultId
      this.inquirerGender = this.rowItem.inquirerGender === 0 ? '女' : '男'
      const caseId = this.rowItem.caseId ? this.rowItem.caseId : ''
      const diseaseId = this.rowItem.diseaseId ? this.rowItem.diseaseId : ''
      sessionStorage.setItem('doctorId', this.doctorId)
      sessionStorage.setItem('videoConsultId', this.rowItem.videoConsultId)
      sessionStorage.setItem('inquirerName', this.rowItem.inquirerName)
      sessionStorage.setItem('inquirerGender', this.inquirerGender)
      sessionStorage.setItem('inquirerAgeStr', this.rowItem.inquirerAgeStr)
      sessionStorage.setItem('caseId', caseId)
      sessionStorage.setItem('diseaseId', diseaseId)
      sessionStorage.setItem('offlineMedicalRecordUrl', this.rowItem.offlineMedicalRecordUrl)
      sessionStorage.setItem('onlineMedicalRecordUrl', this.rowItem.onlineMedicalRecordUrl)
      const params = {
        doctorId: this.doctorId,
        videoConsultId: this.rowItem.videoConsultId
      }
      if (type === 1) {
        this.$confirm('接诊后会直接进入视频诊室，您确认立即接诊吗？？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '稍后再接',
          type: 'warning'
        }).then(() => {
          API.accepts(params).then(response => {
            // this.$router.push({ path: '/doctor/chat', query: { doctorName: this.doctorName, patientName: this.patientName, userId: 'doctor-pc-' + this.doctorId, roomId: this.roomId }})
            this.$router.push({ path: '/doctor/VideoSpecial', query: { doctorName: this.doctorName, patientName: this.patientName, userId: 'doctor-pc-' + this.doctorId, roomId: this.roomId, isMeeting: isMeeting, flag: this.rowItem.flag }})
          })
        })
      } else {
        API.accepts(params).then(response => {
          // this.$router.push({ path: '/doctor/chat', query: { doctorName: this.doctorName, patientName: this.patientName, userId: 'doctor-pc-' + this.doctorId, roomId: this.roomId }})
          this.$router.push({ path: '/doctor/VideoSpecial', query: { doctorName: this.doctorName, patientName: this.patientName, userId: 'doctor-pc-' + this.doctorId, roomId: this.roomId, isMeeting: isMeeting, flag: this.rowItem.flag }})
        })
      }
    },
    acceptsMeetingJiezhen(item, type, flag) {
      item.flag = flag
      this.rowItem = item
      this.acceptsCommon(type, 1)
    },
    // 确认接诊
    acceptsJiezhen(item, type) {
      this.rowItem = item
      this.acceptsCommon(type, 0)
    },
    // 拒绝接诊
    refusedJiezhen(item) {
      this.reasonShow = false
      this.reason = ''
      this.radio = ''
      this.videoConsultId = item.videoConsultId
      this.reasonDialogVisible = true
      API.reasonList().then(response => {
        this.radioList = response
      })
    },
    radioChange(val) {
      console.log(val)
      if (val === '其他原因') {
        this.reasonShow = true
        this.reason = ''
      } else {
        this.reasonShow = false
        this.reason = val
        const radioList = this.radioList
        for (const i in radioList) {
          if (val === radioList[i].id) {
            this.reason = radioList[i].content
          }
        }
      }
    },
    confirmRefuse() {
      console.log(this.radio)
      if (this.radio === '') {
        this.$message.error('请选择拒绝原因！')
        return
      }
      if (this.radio === '其他原因' && this.reason === '') {
        this.$message.error('请填写其他原因！')
        return
      }
      const params = {
        videoConsultId: this.videoConsultId,
        // refusalReasonId: this.radio,
        refusalReason: this.reason
      }
      API.refuse(params).then(response => {
        this.reasonDialogVisible = false
        this.ListQuery.page = 1
        this.ListQuery.status = 1
        this.getListData()
        this.$message.success('拒绝成功')
      })
    },

    // 完善病例
    perfectCase(item) {
      console.log(item, 1146)
      this.resetCaseForm()
      this.perfectCaseVisible = true
      this.baseInfo = item.inquirerInfo
      this.baseInfo.idCard = item.inquirerInfo.idCard.replace(/^(.{4})(?:\d+)(.{4})$/, '$1 **** **** $2')
      this.caseForm.inquirerId = item.inquirerId
      this.caseForm.patientId = item.patientId
      this.caseForm.patientName = item.inquirerName
      this.caseForm.age = item.inquirerInfo.ageYear
      this.caseForm.ageStr = item.inquirerInfo.age
      this.caseForm.ageUnit = '岁'
      this.caseForm.patientGender = item.inquirerInfo.gender
      this.caseForm.templateId = '1'
      this.caseForm.templateType = '1'
      this.caseForm.revisitFalg = '2'
      this.caseForm.send = '1'
      this.caseForm.pastHistory = item.inquirerInfo.pastHistory || '无'
      this.caseForm.allergy = item.inquirerInfo.allergy || '无'
      this.videoConsultId = item.videoConsultId
      this.inquirerGender = item.inquirerInfo.gender
      // this.caseForm.diagnoseData = item.diagnosis ? item.diagnosis : []
      this.caseForm.diagnosisList = item.diagnosis ? item.diagnosis : []
      this.current = 0
      this.isSetTpl = false
      this.isAdd = false
      this.dialogTemplateTitle = '完善病历'
      this.dialogTemplateVisible = false
      this.getSetingList()
      const initInfoData = {
        doctorId: this.doctorId,
        inquirerId: this.caseForm.inquirerId,
        patientId: this.caseForm.patientId,
        sessionConsultId: this.videoConsultId
      }
      this.initCaseFn(initInfoData, 1)
    },
    initCaseFn(data, identify = 1) {
      // identify 前端标识，1为完善病历初始化临床诊断，2为开具处方初始化临床诊断
      API.initCaseInterface(data).then(res => {
        if (res.sessionDiagnosis.diagnosis && res.sessionDiagnosis.diagnosis.length) {
          if (identify === 1) {
            console.log('完善病历初始化临床诊断 ', res)
            this.caseForm.diagnosisList = res.sessionDiagnosis.diagnosis.map(item => {
              return `${item.name} ${item.code}`
            })
          } else if (identify === 2) {
            console.log('开具处方初始化临床诊断 ', res)
            this.prescripForm.diagnosis = res.sessionDiagnosis.diagnosis.map(item => {
              return `${item.name} ${item.code}`
            })
          }
        }
      })
    },
    pastHistoryClick(item) {
      // eslint-disable-next-line eqeqeq
      if (item == '无') {
        this.caseForm.pastHistory = item
      } else {
        this.caseForm.pastHistory = this.caseForm.pastHistory && this.caseForm.pastHistory !== '无' ? this.caseForm.pastHistory + '、' + item : item
        this.caseForm.pastHistory = this.caseForm.pastHistory.replace('无', '')
      }
      console.log(this.caseForm.pastHistory, 'pastHistory')
      this.$set(this.caseForm, 'pastHistory', this.caseForm.pastHistory)
    },
    allergyClick(item) {
      // eslint-disable-next-line eqeqeq
      if (item == '无') {
        this.caseForm.allergy = item
      } else {
        this.caseForm.allergy = this.caseForm.allergy && this.caseForm.allergy !== '无' ? this.caseForm.allergy + '、' + item : item
        this.caseForm.allergy = this.caseForm.allergy.replace('无', '')
      }
      console.log(this.caseForm.allergy, 'allergy')
      this.$set(this.caseForm, 'allergy', this.caseForm.allergy)
    },
    pastFamilyClick(item) {
      if (item === '无') {
        this.caseForm.pastFamily = item
      } else {
        this.caseForm.pastFamily = this.caseForm.pastFamily && this.caseForm.pastFamily !== '无' ? this.caseForm.pastFamily + '、' + item : item
        this.caseForm.pastFamily = this.caseForm.pastFamily.replace('无', '')
      }
      console.log(this.caseForm.pastFamily, 'pastFamily')
      this.$set(this.caseForm, 'pastFamily', this.caseForm.pastFamily)
    },
    presetOptions(item) {
      this.caseForm.treatmentOptions = this.caseForm.treatmentOptions ? this.caseForm.treatmentOptions + '、' + item : item
    },
    menstrualClick(val) {
      if (val === 1) {
        this.menstrualShow = true
      } else {
        this.menstrualShow = false
      }
    },
    handleSave(formName) {
      this.$refs[formName].validate(valid => {
        this.dialogVisible = true
      })
    },
    transferDiagnosisNameListFn(diagnosisList) {
      if (!diagnosisList || !Array.isArray(diagnosisList)) return []
      return diagnosisList.map(item => item.replace(/\s\S+$/, ''))
    },
    transferDiagnosisListToStrFn(diagnosisList) {
      if (!diagnosisList || !Array.isArray(diagnosisList)) return ''
      return diagnosisList.map(item => item.name + ' ' + item.code).join(',')
    },
    transferDiagnosisObjFn(diagnosisList) {
      if (!diagnosisList || !Array.isArray(diagnosisList)) return []
      return diagnosisList.map(item => {
        return {
          name: item.replace(/\s\S+$/, ''),
          code: item.split(' ').pop()
        }
      })
    },
    submitCaseForm(formName) {
      // eslint-disable-next-line eqeqeq
      console.log('=====提交病历=======', this.caseForm)
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log(this.caseForm, this.doctorId, 1219)
          const loadingInstance = Loading.service({ fullscreen: true })
          this.caseForm.doctorId = this.doctorId
          // 完善病历提交
          API.submitCaseForm(this.caseForm, { diagnosisList: this.transferDiagnosisNameListFn(this.caseForm.diagnosisList) })
            .then(response => {
              this.uniqueId = response.uniqueId
              this.recordId = response.recordId
              sessionStorage.setItem('Type', '1')
              if (this.noSecret) {
                this.sign()
              } else {
                // eslint-disable-next-line eqeqeq
                if (this.seviceName == 2) {
                  this.signBtnShow = true
                  this.dialogSignedVisible = true
                  this.qm = response.faceUrl
                } else {
                  this.CAForm.password = ''
                  this.dialogCAVisible = true
                }
              }
              loadingInstance.close()
            })
            .finally(function() {
              loadingInstance.close()
            })
        } else {
          this.$message.error('请填写完整病历信息！')
        }
      })
    },
    //病历e签宝签署
    sign() {
      const that = this
      const params = {}
      params.recordId = that.recordId
      params.doctorId = that.doctorId
      params.uniqueId = that.uniqueId
      API.sign(params)
        .then(function(data) {
          if (data === null) {
            that.perfectCaseVisible = false
            that.prescripVisible = false
            that.dialogSignedVisible = false
            that.confirmSign()
          }
        })
        .finally(function() {
          this.signBtnShow = true
        })
    },
    confirmSign() {
      const that = this
      const params = {}
      params.recordId = that.recordId
      params.doctorId = that.doctorId
      params.uniqueId = that.uniqueId
      params.consultId = that.videoConsultId
      params.consultType = 2
      params.send = true
      API.confirmSign(params)
        .then(function(data) {
          that.perfectCaseVisible = false
          that.prescripVisible = false
          that.dialogSignedVisible = false
          that.nowIndex = 1
          that.ListQuery.page = 1
          that.ListQuery.status = 5
          that.getListData()
        })
        .finally(function() {
          this.signBtnShow = true
        })
    },
    //关闭签署关闭按钮
    closeSignBtn() {
      this.signBtnShow = false
    },
    closeQm() {
      this.dialogSignedVisible = false
      this.dialogCfSignedVisible = false
      this.dialogOpinionSignedVisible = false
      this.getListData()
    },
    qmOnClose() {
      this.getListData()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    resetCaseForm() {
      this.caseForm = {
        doctorId: '',
        inquirerId: '',
        mainComplaint: '',
        presentDisease: '',
        pastHistory: '',
        allergy: '',
        pastFamily: '',
        status: '',
        firstAge: '',
        cycle: '',
        processDays: '',
        dysmenorrhea: '',
        diagnosisList: [],
        treatmentOptions: ''
      }
      this.menstrualShow = false
    },

    // 四川CA密码提交签署
    CASumbit(formName) {
      const that = this
      that.$refs[formName].validate(valid => {
        if (valid) {
          const params = {}
          params.recordId = that.recordId
          params.recomId = that.recomId
          params.doctorId = that.doctorId
          params.uniqueId = that.uniqueId
          params.pin = that.CAForm.password
          console.log(params, 958)
          if (that.perfectCaseVisible) {
            API.sign(params)
              .then(function(data) {
                if (data === null) {
                  that.perfectCaseVisible = false
                  that.prescripVisible = false
                  that.dialogSignedVisible = false
                  that.dialogCAVisible = false
                  that.confirmSign()
                }
              })
              .finally(function() {})
          } else {
            API.Cfsign(params, function(res) {
              if (res.code === 0) {
                that.perfectCaseVisible = false
                that.prescripVisible = false
                that.dialogCfSignedVisible = false
                that.dialogCAVisible = false
              } else {
                this.$message.error(res.msg)
              }
            })
              .then(function(data) {
                if (data === null) {
                  that.perfectCaseVisible = false
                  that.prescripVisible = false
                  that.dialogCfSignedVisible = false
                  that.dialogCAVisible = false
                  that.CfconfirmSign()
                }
              })
              .finally(function() {})
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 开具处方
    openPrescrip(item) {
      this.resetPrescripForm()
      this.baseInfo = item.inquirerInfo
      this.prescripForm.inquirerId = item.inquirerId
      this.prescripForm.patientId = item.patientId
      this.prescripForm.patientName = item.inquirerName
      this.prescripForm.patientAge = item.inquirerInfo.ageYear
      this.prescripForm.patientAgeStr = item.inquirerInfo.age
      this.prescripForm.patientGender = item.inquirerInfo.gender
      this.videoConsultId = item.videoConsultId
      this.prescripForm.patientAgeUnit = '岁'
      // this.diagnoseData = item.diagnosis ? item.diagnosis : []
      this.prescripForm.diagnosis = item.diagnosis ? item.diagnosis : []
      const params = { doctorId: this.doctorId, inquirerId: item.inquirerId }
      const initInfoData = {
        doctorId: this.doctorId,
        inquirerId: this.prescripForm.inquirerId,
        patientId: this.prescripForm.patientId,
        sessionConsultId: this.videoConsultId
      }
      // 校验处方发送条件
      API.send(params)
        .then(response => {
          this.initCaseFn(initInfoData, 2)
          this.prescripVisible = true
        })
        .catch(e => {
          this.prescripVisible = false
        })
    },
    submitPrescripForm(formName) {
      var date = new Date()
      this.prescripForm.timestamp = date.getTime()
      // this.prescripForm.diagnosis = this.diagnoseData
      console.log('=====提交处方=======', this.prescripForm)
      // eslint-disable-next-line eqeqeq
      if (this.prescripForm.items.length == 0) {
        this.$message.error('请添加药品！')
        return
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          // 开具处方提交
          API.submitPrescripForm({ ...this.prescripForm, diagnosis: this.transferDiagnosisNameListFn(this.prescripForm.diagnosis) }, this.doctorId).then(response => {
            this.uniqueId = response.esignSerial[0]
            this.recomId = response.recomId
            this.$message.success('成功')
            sessionStorage.setItem('Type', '2')
            if (this.noSecret) {
              this.Cfsign()
            } else {
              // eslint-disable-next-line eqeqeq
              if (this.seviceName == 2) {
                this.signBtnShow = true
                this.dialogCfSignedVisible = true
                this.qm = response.faceUrl
              } else {
                this.dialogCAVisible = true
                this.CAForm.password = ''
              }
            }
          })
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    //处方e签宝签署
    Cfsign() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      params.doctorId = that.doctorId
      params.uniqueId = that.uniqueId
      console.log(833, params)
      API.Cfsign(params, function(res) {
        if (res.code === 0) {
          that.perfectCaseVisible = false
          that.prescripVisible = false
          that.dialogCfSignedVisible = false
        } else {
          this.$message.error(res.msg)
        }
      })
        .then(function(data) {
          if (data === null) {
            that.perfectCaseVisible = false
            that.prescripVisible = false
            that.dialogCfSignedVisible = false
            that.CfconfirmSign()
          }
        })
        .finally(function() {})
    },
    CfconfirmSign() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      params.doctorId = that.doctorId
      params.uniqueId = that.uniqueId
      params.consultId = that.videoConsultId
      params.consultType = 2
      params.send = true
      API.CfconfirmSign(params)
        .then(function(data) {
          that.perfectCaseVisible = false
          that.prescripVisible = false
          that.dialogSignedVisible = false
          that.nowIndex = 1
          that.ListQuery.page = 1
          that.ListQuery.status = 5
          that.getListData()
        })
        .finally(function() {})
    },
    resetPrescripForm() {
      this.diagnoseData = []
      this.prescripForm.diagnosis = []
      this.prescripForm.items = []
      this.drugList = []
    },

    // 添加药品
    addDrug() {
      if (this.drugList.length >= 5) {
        this.$message.error('最多可添加5种药品！')
        return
      } else {
        this.drugOptions = []
        sessionStorage.removeItem('searchArr')
        this.resetDrug()
        this.drugVisible = true
        this.disabled = false
        this.dialogStatus = 'create'
        this.pIndex = ''
        this.selDisabled = false
      }
    },
    drugChange(val) {
      this.resetForm('drugForm')
      const searchArr = JSON.parse(sessionStorage.getItem('searchArr'))
      for (const i in searchArr) {
        // eslint-disable-next-line eqeqeq
        if (val == searchArr[i].skuId) {
          this.searchObj = searchArr[i]
          this.drugForm.productId = searchArr[i].productId
          this.drugForm.commonName = searchArr[i].company
          this.drugForm.productName = searchArr[i].name
          this.drugForm.skuId = searchArr[i].skuId
          this.drugForm.usage = searchArr[i].usage
          this.drugForm.quantity = searchArr[i].quantity
          this.drugForm.spec = searchArr[i].spec
          this.drugForm.stockNum = searchArr[i].stockNum
        }
        console.log(this.drugForm.usage, 1029)
      }
      const params = {
        doctorId: this.doctorId,
        skuIds: val
      }
      API.getDefaultUsage(params).then(response => {
        console.log(response)
        if (response.length > 0) {
          this.defaultUsage = response[0]
          this.drugForm.usageVal = response[0].usageMethod
          // this.drugForm.dosageCycle = response[0].dosageCycle
          // this.drugForm.dosageCycleUnit = response[0].dosageCycleUnit
          // this.drugForm.dosageCount = response[0].dosageCount
          // this.drugForm.dosageStr = response[0].dosageStr
          // this.drugForm.eachDosageCount = response[0].eachDosageCount
          this.drugForm.eachDoseUnit = response[0].eachDoseUnit
          this.drugForm.usageMethod = response[0].usageMethod
          this.drugForm.usageTime = response[0].usageTime
          this.drugForm.packagSpec = response[0].packagSpec
          this.drugForm.quantityUnit = response[0].quantityUnit
          this.drugForm.medicationCycleMax = response[0].medicationCycleMax
          this.drugForm.doseMax = response[0].doseMax
          this.drugForm.doseMin = response[0].doseMin
          this.eachDoseUnit = response[0].eachDoseUnit
          this.drugForm.quantity = ''
          this.drugForm.medicineMinUnit = response[0].medicineMinUnit
          // response[0].eachDosageCount ? this.selDisabled = false : this.selDisabled = false
          if (response[0].eachDosageCount) {
            if (response[0].doseMin && response[0].doseMax) {
              if (response[0].eachDosageCount >= response[0].doseMin && response[0].eachDosageCount <= response[0].doseMax) {
                this.drugForm.eachDosageCount = response[0].eachDosageCount
              } else if (response[0].eachDosageCount < response[0].doseMin) {
                this.drugForm.eachDosageCount = response[0].doseMin
              } else if (response[0].eachDosageCount > response[0].doseMax) {
                this.drugForm.eachDosageCount = response[0].doseMax
              }
            } else {
              this.drugForm.eachDosageCount = response[0].eachDosageCount
            }
          }
          const dayNumber = this.getDayNumber(response[0].dosageCount, response[0].dosageCycle, response[0].dosageCycleUnit)
          if (response[0].eachDosageCount) {
            if (response[0].frequencyMin && response[0].frequencyMax) {
              if (dayNumber >= response[0].frequencyMin && dayNumber <= response[0].frequencyMax) {
                this.drugForm.dosageCycle = response[0].dosageCycle
                this.drugForm.dosageCycleUnit = response[0].dosageCycleUnit
                this.drugForm.dosageCount = response[0].dosageCount
                this.drugForm.dosageStr = response[0].dosageStr
              } else if (dayNumber < response[0].frequencyMin) {
                this.drugForm.dosageCycle = '1'
                this.drugForm.dosageCycleUnit = '日'
                this.drugForm.dosageCount = response[0].frequencyMin
                this.drugForm.dosageStr = '每日' + response[0].frequencyMin + '次'
              } else if (dayNumber > response[0].frequencyMax) {
                this.drugForm.dosageCycle = '1'
                this.drugForm.dosageCycleUnit = '日'
                this.drugForm.dosageCount = response[0].frequencyMax
                this.drugForm.dosageStr = '每日' + response[0].frequencyMax + '次'
              }
            } else {
              this.drugForm.dosageCycle = response[0].dosageCycle
              this.drugForm.dosageCycleUnit = response[0].dosageCycleUnit
              this.drugForm.dosageCount = response[0].dosageCount
              this.drugForm.dosageStr = response[0].dosageStr
            }
          }
        }
      })
    },
    dosageChange(val) {
      const dosageOptions = this.dosageOptions
      this.drugForm.dosageCycle = dosageOptions[val].dateNumber
      this.drugForm.dosageCycleUnit = dosageOptions[val].dataUnit
      this.drugForm.dosageCount = dosageOptions[val].frequency
      this.getQuantity()
    },
    usageChange(val) {
      const usageOptionsArr = this.usageOptionsArr
      this.drugForm.usageMethod = usageOptionsArr[val].takeMethod
      this.drugForm.usageTime = usageOptionsArr[val].takeDate
    },
    addSrueDurg(formName) {
      for (const i in this.drugList) {
        // eslint-disable-next-line eqeqeq
        if (this.drugList[i].skuId == this.drugForm.skuId && this.dialogStatus == 'create') {
          this.$message.error('已经添加过该药品，不能重复添加！')
          return
        }
      }
      if (this.drugForm.quantity > this.drugForm.stockNum) {
        this.$message.error('库存不足！')
        return
      }
      this.drugForm.usageText = '每' + this.drugForm.dosageCycle + this.drugForm.dosageCycleUnit + this.drugForm.dosageCount + '次，每次' + this.drugForm.eachDosageCount + this.drugForm.eachDoseUnit + '，' + this.drugForm.usageMethod
      this.drugForm.usage = '每' + this.drugForm.dosageCycle + this.drugForm.dosageCycleUnit + this.drugForm.dosageCount + '次，每次' + this.drugForm.eachDosageCount + this.drugForm.eachDoseUnit + '，' + this.drugForm.usageMethod
      console.log('=====添加的药品=======', this.drugForm)
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.pIndex !== '') {
            const drugForm = Object.assign({}, this.drugForm)
            this.$set(this.drugList, this.pIndex, drugForm)
            this.prescripForm.items = this.drugList
            this.drugVisible = false
          } else {
            const drugForm = Object.assign({}, this.drugForm)
            this.drugList.push(drugForm)
            this.prescripForm.items = this.drugList
            this.drugVisible = false
          }
          console.log('=====药品列表=======', this.drugList)
        } else {
          this.drugVisible = true
          return false
        }
      })
    },
    resetDrug() {
      this.drugForm.skuId = ''
      this.drugForm.productName = ''
      this.drugForm.dosageStr = ''
      this.drugForm.eachDosageCount = ''
      this.drugForm.eachDoseUnit = ''
      this.drugForm.usageVal = ''
      this.drugForm.drugCycle = ''
      this.drugForm.quantity = ''
      this.drugForm.backup = ''
    },
    // 编辑药品
    editRow(index, rows) {
      this.pIndex = index
      console.log(index, rows)
      this.drugVisible = true
      this.disabled = true
      this.dialogStatus = 'update'
      this.drugForm.productName = rows[index].productName
      this.drugForm.dosageStr = '每' + rows[index].dosageCycle + rows[index].dosageCycleUnit + rows[index].dosageCount + '次'
      this.drugForm.eachDosageCount = rows[index].eachDosageCount
      this.drugForm.eachDoseUnit = rows[index].eachDoseUnit
      this.drugForm.usageVal = rows[index].usageTime + rows[index].usageMethod
      this.drugForm.quantity = rows[index].quantity
      this.drugForm.drugCycle = rows[index].drugCycle
      this.drugForm.backup = rows[index].backup
    },
    // 删除药品
    deleteRow(index, rows) {
      console.log(index, rows)
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rows.splice(index, 1)
        this.$message.success('删除成功')
      })
    },

    // 查看病例
    lookCase(item) {
      const params = {
        videoConsultId: item.videoConsultId
      }
      API.getLookCase(params).then(response => {
        window.open(response)
      })
    },
    // 查看处方
    lookPrescrip(item) {
      const params = {
        videoConsultId: item.videoConsultId
      }
      API.getLookPrescrip(params).then(response => {
        this.prescripList = response
        if (response.length > 1) {
          this.prescripListVisible = true
          this.prescripList = response
        } else {
          window.open(response[0].pdfUrl)
        }
      })
    },
    prescripPdfUrl(pdfUrl) {
      window.open(pdfUrl)
    },

    // 用法用量数据
    getUsage() {
      API.getUsage().then(response => {
        console.log(response, 1723)
        this.pastHistory = response.pastList[0].itemList //过敏史
        this.allergy = response.pastList[1].itemList //过敏史
        var pastFamily = response.pastList[2].itemList.concat(response.historyFamilyList)
        this.pastFamily = pastFamily //家庭史
        this.menstrualAge = response.menstrualFirstAgeList //月经-初潮年龄
        this.menstrualCycle = response.menstrualCycleList //月经-月经周期
        this.menstrualDay = response.menstrualProcessDayList //月经-行经天数
        this.dosageOptions = response.medicineDosageList //药品用量
        this.unitOptions = response.medicineUnitList //单位
        this.usageOptionsArr = response.medicineDateAndMethodList
        this.usageOptions = response.medicineDateAndMethodList.map(item => item.takeDate + item.takeMethod) //药品用法
        this.seviceName = response.eSignConfigVO.seviceName
        this.treatmentPresetOptions = response.treatmentPresetOptions
      })
    },

    // 列表
    getListData() {
      this.ListQuery.doctorId = this.doctorId
      const getList = this.nowIndex === 1 ? API.getMeetingList : API.getList
      getList(this.ListQuery).then(response => {
        if (response.result.length === 0) {
          this.listShow = false
        } else {
          this.listShow = true
          this.list = response.result
          this.total = parseInt(response.totalPages * 10)
        }
      })
    },
    //分页（上一页 下一页 当前页）
    prevClick(val) {
      this.currentIndex = -1
      this.ListQuery.page = val
      this.getListData()
    },
    nextClick(val) {
      this.currentIndex = -1
      this.ListQuery.page = val
      this.getListData()
    },
    currentChange(val) {
      this.currentIndex = -1
      this.ListQuery.page = val
      this.getListData()
    },

    // 获取key和appId
    getKey() {
      const params = {
        userId: 'doctor-pc-' + this.userInfo.id
      }
      API.getKey(params).then(res => {
        const key = res.key
        const sdkAppId = res.appId
        const userSig = res.userSig
        const userId = res.userId
        sessionStorage.setItem('key', key)
        sessionStorage.setItem('sdkAppId', sdkAppId)
        sessionStorage.setItem('userSig', userSig)
        sessionStorage.setItem('userId', userId)
      })
    },
    // 药品一天需要吃的的次数
    getDayNumber(dosageCount, dosageCycle, dosageCycleUnit) {
      let val
      switch (dosageCycleUnit) {
        case '小时':
          val = (24 / dosageCycle) * dosageCount
          break
        case '日':
          val = dosageCount / dosageCycle
          break
        case '隔日':
          val = dosageCount / dosageCycle / 2
          break
        case '周':
          val = dosageCount / dosageCycle / 7
          break
        case '月':
          val = dosageCount / dosageCycle / 30
          break
        default:
          break
      }
      return val
    },
    // 自动计算用药合数
    getQuantity() {
      const drugCycle = this.drugForm.drugCycle //药品周期
      const eachDosageCount = this.drugForm.eachDosageCount //单次剂量
      const packagSpec = this.drugForm.packagSpec //最小规格包装数
      const dosageCount = this.drugForm.dosageCount //次数
      const dosageCycle = this.drugForm.dosageCycle //小时、日、周、月
      const dosageCycleUnit = this.drugForm.dosageCycleUnit //单次剂量的单位
      const dayNumber = this.getDayNumber(dosageCount, dosageCycle, dosageCycleUnit)
      if (packagSpec !== null && this.drugForm.medicineMinUnit === this.drugForm.eachDoseUnit) {
        const quantity = (drugCycle * dayNumber * eachDosageCount) / packagSpec
        const quantityVal = Math.ceil(quantity)
        this.$set(this.drugForm, 'quantity', quantityVal)
      }
    },

    // 对象合并方法
    objMerge(obj1, obj2) {
      for (var key in obj2) {
        // eslint-disable-next-line no-prototype-builtins
        if (obj2.hasOwnProperty(key) === true) {
          obj1[key] = obj2[key]
        }
      }
      console.log('=====合并对象=======', obj1)
    },
    getTime() {
      setInterval(() => {
        this.nowtime = new Date().toLocaleString()
      }, 1000)
    },
    formatNum(e) {
      e.target.value = e.target.value.match(/^\d*/g)[0] || null
      this.productData.equivalent = e.target.value
    },
    formatTime(date, type = true) {
      return this.Common.formatChatTime(date, type)
    },
    formatInt(num) {
      if (num && !isNaN(num)) {
        num = parseInt(num)
      } else {
        num = ''
      }
      return num
    },
    handleTemplate(formName, type) {
      if (type === 'add' && !this.caseForm.name) {
        this.$message({
          message: '请填写模板名称',
          type: 'warning'
        })
        return
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          const loadingInstance = Loading.service({ fullscreen: true })
          console.log(this.caseForm, 1909)
          if (this.isAdd) {
            this.caseForm.medicalRecordSettingList = this.settings
          }
          // 保存病历模版
          API.saveTemplate(this.doctorId, { ...this.caseForm, diagnosis: this.transferDiagnosisObjFn(this.caseForm.diagnosisList) })
            .then(response => {
              if (type === 'save') {
                this.$alert('保存成功', '提示', {
                  confirmButtonText: '确定',
                  callback: action => {
                    if (this.isSetTpl) {
                      this.isSetTpl = false
                      this.dialogTemplateTitle = '完善病历'
                      this.dialogTemplateVisible = true
                      this.getSetingList()
                      this.getTplList()
                    }
                  }
                })
              } else {
                this.$message.success('保存成功')
                this.getTplList()
                this.dialogVisible = false
              }
              this.isAdd = false
              loadingInstance.close()
            })
            .finally(function() {
              loadingInstance.close()
            })
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    handleSeting() {
      API.saveSettingList({ typeStr: this.checkedCities.join(','), doctorId: this.doctorId }).then(response => {
        this.$message.success('保存成功')
        this.dialogSetingVisible = false
        this.getSetingList()
      })
    },
    handleSettings() {
      this.checkedCities = []
      this.getSetingList()
      this.dialogSetingVisible = true
    },
    // 新增病例模版
    handleAddtpl() {
      this.resetTemp()
      this.isSetTpl = true
      this.isAdd = true
      this.dialogTemplateTitle = '新增模版'
      this.dialogTemplateVisible = false
      this.$set(this.caseForm, 'medicalRecordSettingList', this.settings)
      console.log(this.caseForm.medicalRecordSettingList, 1957)
    },
    // 编辑病例模版
    handleEdittpl() {
      console.log('编辑模版')
      this.isSetTpl = true
      this.dialogTemplateTitle = '编辑模版'
      this.dialogTemplateVisible = false
      this.caseForm = this.tplInfo
      if (this.setting) {
        this.settings = this.setting
      }
    },
    // 删除病例模版
    handleDeleteFn() {
      console.log('删除病例模版')
      this.$confirm('确认删除该病历模板？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        const deleteTemData = {
          doctorId: this.doctorId,
          id: this.templateId
        }
        API.deleteTemplate(deleteTemData).then(response => {
          console.log('删除病例模版', response)
          this.getTplList('show')
        })
      })
    },
    // 切换模版
    handleSwatch(id, index) {
      this.templateId = id
      this.current = index
      this.getTemplateDetail()
    },
    // 使用模版
    handleEmploy() {
      this.isSetTpl = false
      this.dialogTemplateTitle = '完善病历'
      this.dialogTemplateVisible = false
      this.tplInfo.inquirerId = this.baseInfo.inquirerId
      this.tplInfo.patientId = this.baseInfo.patientId
      this.tplInfo.patientName = this.baseInfo.name
      this.tplInfo.age = this.baseInfo.ageYear
      this.tplInfo.ageStr = this.baseInfo.age
      this.tplInfo.ageUnit = '岁'
      this.tplInfo.patientGender = this.baseInfo.gender
      this.tplInfo.templateId = '1'
      this.tplInfo.templateType = '1'
      this.tplInfo.revisitFalg = '2'
      this.tplInfo.send = '1'
      this.caseForm = this.tplInfo
      // this.settings = this.setting
      this.menstrualShow = this.caseForm.status === 1 ? true : false
    },
    // 获取病历列表
    getTplList(type) {
      if (type === 'show') {
        // this.current = 0
        this.dialogTemplateVisible = true
      }
      this.tplListQuery.doctorId = this.doctorId
      API.getTemplate(this.tplListQuery).then(response => {
        console.log('病历列表', response)
        this.tplList = response.result
        if (this.tplList.length) {
          this.templateId = response.result[this.current].id
          this.tplTotal = response.totalCount
          this.getTemplateDetail()
        }
      })
    },
    // 获取病历设置列表
    getSetingList() {
      API.getSettingList({ doctorId: this.doctorId }).then(response => {
        this.settings = response
        this.settingsData = response
        response.forEach(item => {
          if (item.enabled === 1) {
            this.checkedCities.push(item.type)
          }
        })
      })
    },
    // 病历模版详情
    getTemplateDetail() {
      console.log('=====templateId====', this.templateId, this.doctorId)
      API.getTemplateDetail({ doctorId: this.doctorId, id: this.templateId }).then(response => {
        response.diagnosisStr = response.diagnosis ? this.transferDiagnosisListToStrFn(response.diagnosis) : ''
        this.tplInfo = response
        this.setting = response.medicalRecordSettingList

        console.log(response.medicalRecordSettingList, 2026)
      })
    },
    handleTemplateSave(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.isAdd = true
          this.dialogVisible = true
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.caseForm = {
          doctorId: '',
          inquirerId: '',
          mainComplaint: '',
          presentDisease: '',
          pastHistory: '',
          allergy: '',
          pastFamily: '',
          status: '',
          firstAge: '',
          cycle: '',
          processDays: '',
          dysmenorrhea: '',
          diagnosisList: [],
          treatmentOptions: ''
        }
        this.$refs['caseForm'].clearValidate()
      })
    },
    changeMeeting(item) {
      this.isApplyMeetingEdit = false
      this.applyMeetingShow = true

      // this.rowItem = item
      this.getMeetingApplyInfo(item.videoConsultId, true)
    },
    editMeeting(item) {
      this.isApplyMeetingEdit = true
      this.getMeetingApplyInfo(item.videoConsultId)
      this.applyMeetingShow = true
    },
    submitMeeting() {
      this.$refs.meetingForm.validate(valid => {
        if (valid) {
          this.meettingLoading = true
          console.log('this.applyMeetingForm', this.applyMeetingForm)
          API.submitMeeting(this.applyMeetingForm)
            .then(response => {
              this.applyMeetingShow = false
              // this.acceptsCommon(2, 1)
              this.toggleTabs(1)
            })
            .finally(() => {
              this.meettingLoading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getMeetingInfo(id) {
      this.meetingInfoShow = true
      this.pdfsrc = ''
      API.getMeetingInfo(id).then(response => {
        // this.meetingInfo = response
        this.pdfsrc = '../../plugin/pdf/web/viewer.html?file=' + encodeURIComponent(response.pdfPath)
      })
    },
    getMeetingApplyInfo(id, isInit = false) {
      const params = {}
      params.videoConsultId = id
      API.getMeetingApplyInfo(params).then(response => {
        this.applyMeetingForm = response
        this.$nextTick(() => {
          this.$refs.meetingForm.clearValidate()
        })
        if (isInit) {
          this.initTime()
        } else {
          this.hospitalChange(response.targetHospitalId)
          this.applyMeetingForm.targetDoctorId = response.targetDoctorId
          this.applyMeetingForm.hospitalId = response.targetHospitalId
        }
      })
    },
    getDoctorList() {
      API.getDoctorList({}).then(response => {
        this.doctorList = response
      })
    },
    submitSuggest() {
      this.$refs.suggestForm.validate(valid => {
        if (valid) {
          this.suggestLoading = true
          // 申请意见发起签署
          API.submitSuggest({ ...this.suggestForm, diagnoseData: this.transferDiagnosisNameListFn(this.suggestForm.diagnoseData) })
            .then(response => {
              this.suggestShow = false
              sessionStorage.setItem('Type', '3')
              this.qm = response.faceUrl
              this.signBtnShow = true
              this.dialogOpinionSignedVisible = true
              // this.getListData()
            })
            .finally(() => {
              this.suggestLoading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    opinionSign() {
      const params = {}
      params.from = this.suggestForm.from
      params.videoConsultId = this.suggestForm.videoConsultId
      API.opinionConfirm(params)
        .then(response => {
          this.dialogOpinionSignedVisible = false
          this.getListData()
        })
        .finally(() => {
          this.signBtnShow = true
        })
    },
    suggestDialog(item) {
      this.rowItem = item
      this.suggestForm.roomId = item.roomId
      this.suggestForm.videoConsultId = item.videoConsultId
      this.suggestForm.from = this.doctorId === item.doctorId ? 2 : 1
      this.suggestShow = true
    },
    groupConsultTimeStartChange(val) {
      // const times = val.split(':')
      // if (times[1] === '30') {
      //   this.timeOptions.groupConsultTimeEnd = +times[0] + ':00'
      // } else {
      //   this.timeOptions.groupConsultTimeEnd = times[0] + ':30'
      // }
      console.log(this.timeOptions, '=========this.timeOptions=========')
      // this.applyMeetingForm.groupConsultTimeStart  = val
    }
  }
}
</script>
