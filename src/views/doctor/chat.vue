<template>
  <div class="main">
    <el-button id="closeBtn" type="primary" style="position:fixed;bottom:50px;right:50%;z-index:999;" @click="closeBtn">结束问诊</el-button>
    <el-link id="offLineRecord" type="primary" style="position:fixed;bottom:100px;right:50px;z-index:999;" @click="offLineRecordBtn">>>{{ company }}就诊记录</el-link>
    <el-link id="onLineRecord" type="primary" style="position:fixed;bottom:50px;right:50px;z-index:999;" @click="onLineRecordBtn">>>互联网医院就诊历史记录</el-link>
    <el-button id="detailBtn" style="position:fixed;bottom:50px;right:50px;z-index:999;" @click="goDetail">查看病例/病情</el-button>
    <iframe id="iframe_chat" ref="iframe_chat" :src="url" frameborder="0" class="iframe_chat"></iframe>
    <!-- <el-button @click="getIframeChat">测试</el-button> -->
    <div id="uploadShow">
      <div style="height:70px;width:100%;overflow:hidden;">
        <div style="width:100%;display:flex;align-items:center;overflow-x:scroll;overflow-y:hidden;height:100%;">
          <div style="flex-direction:row;justify-content:center;align-item:center;">
            <el-upload
              class="avatar-uploader"
              :action="action"
              :headers="myHeaders"
              :data="updata"
              :show-file-list="false"
              :on-success="handleSuccess"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div style="width:50px;height:20px;"></div>
          </div>
          <!-- <div v-if="uploadImglist && uploadImglist.length">
            <div v-for="(item, index) in uploadImglist" :key="index" style="flex-direction:row;justify-content:center;align-item:center;margin-left:5px;">
              <el-image
                v-if="item.path"
                style="width:50px;height:50px;flex:none;"
                :src="item.path"
                fit
                @click="zoomIn(item.path)"
              />
              <div v-if="item.isDoctorSend" style="font-size:12px;width:50px;height:20px;text-align:center;line-height:20px;">{{ item.doctorName }}</div>
            </div>
          </div> -->
        </div>
      </div>
      <div style="height:70px;width:100%;overflow:hidden;margin-top:10px;">
        <div v-if="imglist && imglist.length" style="display:flex;align-items:center;overflow-x:scroll;overflow-y:hidden;height:100%;">
          <div v-for="(item, index) in imglist" :key="index" style="flex-direction:row;justify-content:center;align-item:center;margin-left:5px;">
            <el-image
              style="width:50px;height:50px;margin-right:5px;flex:none;"
              :src="item.path"
              fit
              @click="zoomIn(item.path)"
            />
            <div v-if="item.isDoctorSend" style="font-size:12px;width:50px;height:20px;text-align:center;line-height:20px;">{{ item.doctorName }}</div>
            <div v-else style="font-size:12px;width:50px;height:20px;text-align:center;line-height:20px;">{{ item.patientName }}</div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog :visible.sync="detailsVisible" :title="title" width="70%">
      <!-- 病情 -->
      <div v-show="typeShow1">
        <div>
          <h4>基本信息</h4>
          <div style="width:100%;background:#F9F9F9;padding:10px 0 10px 20px;">
            <p>姓名：{{ detail.patientName }}</p>
            <p>性别：{{ detail.patientGender == 0 ? '女' : '男' }}</p>
            <p>年龄：{{ detail.patientAgeStr }}</p>
            <p>就诊医院：{{ detail.offlineHospital }}</p>
            <p>就诊科室：{{ detail.offlineDepartment }}</p>
            <p>就诊医生：{{ detail.offlineDoctor }}</p>
            <p>就诊诊断：{{ detail.offlineDiagnosis }}</p>
          </div>
        </div>
        <div>
          <h4>线下病历/处方</h4>
          <div v-if="offlineERImgs !== null" style="display:flex;">
            <div v-for="(item, index) in offlineERImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>PDF文件</h4>
          <div v-if="offlineERPdfs !== null">
            <div v-for="(item, index) in offlineERPdfs" :key="index">
              <!-- <div @click="opendPdf(item)" style="margin-bottom:5px;cursor:pointer">{{item.name}}</div> -->
              <el-link :href="item.filepath" target="_blank">{{ item.name }}</el-link>
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>检查图片</h4>
          <div v-if="offlineDiagnosisImgs !== null" style="display:flex;">
            <div v-for="(item, index) in offlineDiagnosisImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>病情主诉</h4>
          <el-form>
            <el-form-item label="">{{ detail.description ? detail.description : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>持续时长</h4>
          <el-form>
            <el-form-item label="">{{ detail.diseaseCycle ? detail.diseaseCycle : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>患处图片</h4>
          <div v-if="descriptionImgs !== null" style="display:flex;">
            <div v-for="(item, index) in descriptionImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>服用药物</h4>
          <el-form>
            <el-form-item label="">{{ detail.drugs ? detail.drugs : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>药盒照片</h4>
          <div v-if="drugsImgs !== null" style="display:flex;">
            <div v-for="(item, index) in drugsImgs" :key="index">
              <img :src="item" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>过敏史</h4>
          <el-form>
            <el-form-item label="">{{ detail.allergy ? detail.allergy : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>既往史</h4>
          <el-form>
            <el-form-item label="">{{ detail.pastHistory ? detail.pastHistory : '无' }}</el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 病历 -->
      <div v-show="typeShow2">
        <div>
          <h4>基本信息</h4>
          <div style="display:flex;justify-content:space-between;background:#F9F9F9;">
            <div style="background:#F9F9F9;padding:10px 0 10px 20px;">
              <p>姓名：{{ casedetail.name }}</p>
              <p>性别：{{ casedetail.gender == 0 ? '女' : '男' }}</p>
              <p>年龄：{{ casedetail.ageStr }}</p>
              <p>科室：{{ casedetail.department }}</p>
              <p>时间：{{ signInfo.signTime }}</p>
            </div>
            <img src="/logo/ic_seal.png" style="width:150px;height:150px;" />
          </div>
        </div>
        <div>
          <h4>主诉</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.mainComplaint ? casedetail.mainComplaint : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>现病史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.presentDisease ? casedetail.presentDisease : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>既往史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.pastHistory ? casedetail.pastHistory : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>过敏史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.allergy ? casedetail.allergy : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>家庭史</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.pastFamily ? casedetail.pastFamily : '无' }}</el-form-item>
          </el-form>
        </div>
        <div v-if="casedetail.gender == 0">
          <h4>月经史</h4>
          <el-form>
            <div style="margin-bottom:6px;">月经情况：{{ menstrual.status ? menstrual.status : '无' }}</div>
            <div style="margin-bottom:6px;">初潮年龄：{{ menstrual.firstAge ? menstrual.firstAge+'岁' : '无' }}</div>
            <div style="margin-bottom:6px;">月经周期：{{ menstrual.cycle ? menstrual.cycle+'天' : '无' }}</div>
            <div style="margin-bottom:6px;">行经天数：{{ menstrual.processDays ? menstrual.processDays+'天' : '无' }}</div>
            <div style="margin-bottom:6px;">是否痛经：{{ menstrual.dysmenorrhea == 1 ? '是' : '否' }}</div>
            <div v-if="menstrual.dysmenorrhea == 1" style="margin-bottom:6px;">痛经部位：{{ menstrual.part }}</div>
          </el-form>
        </div>
        <div>
          <h4>检查指标</h4>
          <el-form>
            <div style="margin-bottom:6px;">体温：{{ casedetail.temperature ? casedetail.temperature+'度' : '无' }}</div>
            <div style="margin-bottom:6px;">体重：{{ casedetail.weight ? casedetail.weight+'kg' : '无' }}</div>
            <div style="margin-bottom:6px;">心率：{{ casedetail.heartRete ? casedetail.heartRete+'bpm' : '无' }}</div>
            <div style="margin-bottom:6px;">收缩压：{{ casedetail.systolic ? casedetail.systolic+'mmHg' : '无' }}</div>
            <div style="margin-bottom:6px;">舒张压：{{ casedetail.diastole ? casedetail.diastole+'mmHg' : '无' }}</div>
            <div style="margin-bottom:6px;">阳性体征：{{ casedetail.positiveSigns ? casedetail.positiveSigns : '无' }}</div>
            <div style="margin-bottom:6px;">必要的阴性体征：{{ casedetail.negativeSigns ? casedetail.negativeSigns : '无' }}</div>
            <div style="margin-bottom:6px;">更多检查结果：{{ casedetail.moreExamin ? casedetail.moreExamin : '无' }}</div>
          </el-form>
        </div>
        <div>
          <h4>诊断</h4>
          <div v-for="(item, index) in casedetail.diagnosisList" :key="index">
            <div style="margin-bottom:5px;">{{ item }}</div>
          </div>
        </div>
        <div>
          <h4>治疗意见</h4>
          <el-form>
            <el-form-item label="">{{ casedetail.treatmentOptions ? casedetail.treatmentOptions : '无' }}</el-form-item>
          </el-form>
        </div>
        <div>
          <h4>其他</h4>
          <div v-if="casedetailImgs !== null" style="display:flex;">
            <div v-for="(item, index) in casedetailImgs" :key="index">
              <img :src="item.imgUrl" style="width:100px;height:100px;margin-right:10px" @click="zoomIn(item.imgUrl)" />
            </div>
          </div>
          <div v-else>无</div>
        </div>
        <div>
          <h4>医生</h4>
          <el-form>
            <el-form-item label="">{{ signInfo.doctorName ? signInfo.doctorName : '无' }}</el-form-item>
            <img :src="signInfo.sealImage" style="width:100px;" />
          </el-form>
        </div>
        <div>
          <h4>医院</h4>
          <el-form>
            <el-form-item label="">{{ signInfo.hospitalName ? signInfo.hospitalName : '无' }}</el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="互联网医院就诊历史记录" :visible.sync="onLineRecordVisible" width="80%" top="5vh">
      <el-button type="primary" @click="backBtn">刷新</el-button>
      <iframe id="iframe_url" ref="iframe_url" :src="onlineMedicalRecordUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
    </el-dialog>
    <el-dialog title="线下就诊记录" :visible.sync="offLineRecordVisible" width="80%" top="5vh">
      <iframe :src="offlineMedicalRecordUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
    </el-dialog>
    <el-dialog title="请对本次问诊开具临床诊断" :visible.sync="diagnoseVisible" width="500px">
      <div class="dialog-content">
        <el-select v-model="diagnoseData" multiple filterable remote reserve-keyword placeholder="请输入关键词" :loading="searchLoading" :remote-method="remoteMethod" style="width:300px;">
          <el-option
            v-for="item in diagnoseOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="diagnoseVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmClose()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
  .main {
    & .iframe_chat {
      width: 100%;
      height: 100vh;
    }
  }
  #detailBtn{
    display: none;
  }
  #uploadShow{
    display: none;
    width: 100%;
    padding: 10px 20px;
    background: #eee;
    position:fixed;left:0;bottom:0;
  }
  #uploadShow .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  #uploadShow .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  #uploadShow .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border: 1px dashed #d9d9d9;
  }
  #uploadShow .avatar-uploader-icon:hover {
    border-color: #409EFF;
  }
  #uploadShow .avatar {
    width: 70px;
    height: 70px;
    display: block;
  }
  #closeBtn{display: none;}
  #onLineRecord{display: none;}
  #offLineRecord{display: none;}
</style>

<script>
import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'
import { getToken } from '@/utils/auth'
import API from '@/api/doctor/index'
import { mapGetters } from 'vuex'
export default {
  name: 'Project',
  components: {
  },
  data() {
    return {
      roomType: null, //进入房间的方式
      userId: '',
      key: '',
      sdkAppId: '',
      userSig: '',
      // imglist: [],
      uploadImglist: [],
      num: 0,
      url: '/TRTC/index.html',
      action: process.env.VUE_APP_BASE_API + '/vc/video/consult/sendGroupMsg',
      myHeaders: { Authorization: getToken(), _o: '6', _p: '2', _w: '1' },
      updata: '',
      dialogImageUrl: '',
      dialogVisible: false,
      fullscreenLoading: false,
      doctorName: '',
      patientName: '',
      roomId: 888888, //房间号--加入相同房间才能聊
      client: '', //客户端服务
      remoteStream: '', //远方播放流
      localStream: '', //本地流
      tim: null,
      inquirerName: '',
      inquirerGender: '',
      inquirerAgeStr: '',
      detailsVisible: false,
      typeShow1: false,
      typeShow2: false,
      caseId: null,
      diseaseId: null,
      title: '病历详情',
      detail: {},
      casedetail: {},
      menstrual: {},
      signInfo: {},
      offlineERImgs: null,
      offlineERPdfs: null,
      offlineDiagnosisImgs: null,
      descriptionImgs: null,
      drugsImgs: null,
      casedetailImgs: null,
      onLineRecordVisible: false,
      offLineRecordVisible: false,
      diagnoseVisible: false,
      searchLoading: false,
      diagnoseData: [],
      diagnoseOptions: [],
      optionsArr: [],
      diagnosis: '',
      onlineMedicalRecordUrl: '',
      offlineMedicalRecordUrl: '',
      doctorId: '',
      videoConsultId: '',
      rlen: history.length,
      company: process.env.VUE_APP_BASE_COMPANY,
      isUrl: false,
      iframeUrl: ''
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapGetters({ imglist: 'messages' })
  },
  watch: {
    messages(oldval, newval) {
      console.log(oldval, newval, 356)
    }
  },
  created() {
    console.log(this.$store.state, '===this.$store.state===')
    this.doctorId = sessionStorage.getItem('doctorId')
    this.videoConsultId = sessionStorage.getItem('videoConsultId')
    this.inquirerName = sessionStorage.getItem('inquirerName')
    this.inquirerGender = sessionStorage.getItem('inquirerGender')
    this.inquirerAgeStr = sessionStorage.getItem('inquirerAgeStr')
    this.caseId = sessionStorage.getItem('caseId')
    this.diseaseId = sessionStorage.getItem('diseaseId')
    this.doctorName = this.getUrlParam('doctorName')
    this.patientName = this.getUrlParam('patientName')
    this.userId = this.getUrlParam('userId')
    this.roomId = this.getUrlParam('roomId')
    this.roomType = this.getUrlParam('roomType')
    this.offlineMedicalRecordUrl = sessionStorage.getItem('offlineMedicalRecordUrl')
    // this.onlineMedicalRecordUrl = sessionStorage.getItem('onlineMedicalRecordUrl')
    this.updata = {
      roomId: this.roomId,
      isDoctorSend: 1,
      userId: this.userId
    }
    this.getImgRecord()

    //处理通过中断再接诊进入的接收图片记录
    // const acceptsData = JSON.parse(sessionStorage.getItem('acceptsData'))
    // console.log(acceptsData, this.messages, 377)
    // const imglist = this.$store.state.mqtt.messages
    // if (imglist.length > 0) {
    //   this.imglist = imglist.concat(acceptsData)
    // } else {
    //   this.imglist = acceptsData
    // }

    // 处理通过第三方输入房间号的接收图片记录
    // const receiveImg = JSON.parse(sessionStorage.getItem('receiveImg'))
    // const receiveImgArr = []
    // for (const i in receiveImg) {
    //   if (receiveImg[i].type == 2) {
    //     const obj = JSON.parse(receiveImg[i].content)
    //     receiveImgArr.push(obj)
    //   }
    // }
    // for (const i in receiveImgArr) {
    //   if (receiveImgArr[i].isDoctorSend) {
    //     this.uploadImglist.push(receiveImgArr[i])
    //   } else {
    //     this.imglist.push(receiveImgArr[i])
    //   }
    // }
  },
  mounted() {
    this.Init()
  },
  beforeDestroy() {
    this.logout()
  },
  methods: {
    goBack() {
      const len = this.rlen - history.length - 1 //-1是不进入iframe页面的下级页面直接退出的话，执行后退一步的操作
      this.$router.go(len)
    },
    //获取图片消息记录
    getImgRecord() {
      const params = {
        doctorId: this.doctorId,
        videoConsultId: this.videoConsultId
      }
      console.log(params, 661)
      API.accepts(params).then(response => {
        console.log(response, 660)
        const imgRecordArr = []
        for (const i in response) {
          if (response[i].type === 2) {
            const obj = JSON.parse(response[i].content)
            imgRecordArr.push(obj)
          }
        }
        sessionStorage.setItem('acceptsData', JSON.stringify(imgRecordArr))
      })
    },
    getUrlParam(name) {
      var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
      var r = window.location.search.substr(1).match(reg)
      if (r != null) return decodeURI(r[2])
      return null
    },
    // 初始化方法tim
    Init() {
      this.key = sessionStorage.getItem('key')
      this.sdkAppId = sessionStorage.getItem('sdkAppId')
      this.userSig = sessionStorage.getItem('userSig')
      this.userId = sessionStorage.getItem('userId')
      const userId = this.userId.split('-')
      const param = {
        userId: userId[2],
        token: getToken()
      }
      API.getMqtt(param).then(response => {
        const params = {}
        const id = this.userInfo.id
        this.END_TIME = 30
        params.clientId = 'c_pc_' + id
        params.dnHost = response.dnHost
        params.cleanSession = response.cleanSession
        params.connectionTimeout = response.connectionTimeout
        params.privatePushTopicName = response.privatePushTopicName
        params.msgTopicName = response.msgTopicName
        params.willTopicName = response.willTopicName
        params.keepAliveInterval = response.keepAliveInterval
        params.sessionTimeout = response.sessionTimeout
        params.statusTopicName = response.statusTopicName
        console.log('initMqtt', params)
        this.$store.dispatch('mqtt/initMqtt', params)
        console.log('连接成功～')
      })

      const options = {
        SDKAppID: this.sdkAppId
      }
      const tim = TIM.create(options)
      this.tim = tim
      tim.setLogLevel(0)
      tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin })
      console.log(TIM.EVENT, '================TIM.EVENT===============', { userID: this.userId, userSig: this.userSig }, options)
      // tim.on(TIM.EVENT.SDK_READY, this._onIMReady)
      // tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.receiveMsg)
      tim.login({ userID: this.userId, userSig: this.userSig })
    },
    // 退出
    logout() {
      this.tim.logout()
    },
    // 接收和发送群组
    _onIMReady() {
      console.log('_onIMReady')
      this.tim.searchGroupByID(this.roomId).then(imResponse => {
        console.log(imResponse, '===========searchGroupByID============')
        this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
          console.log(imResponse, '===========joinGroup============')
        })
      }).catch(e => {
        console.log(e, '=========eeee==searchGroupByID============')
        this.tim.createGroup({
          groupID: this.roomId + '',
          name: this.roomId + '',
          type: TIM.TYPES.GRP_MEETING
        }).then(imResponse => {
          console.log(imResponse, '===========createGroup============')
          this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
            console.log(imResponse, '===========joinGroup============')
          })
        }).catch(e => {
          if (e.code === 10021) {
            console.log('群已存在，直接进群', e)
            this.tim.joinGroup({ groupID: this.roomId, type: TIM.TYPES.GRP_MEETING }).then(imResponse => {
              console.log(imResponse, '===========joinGroup============')
            })
          }
        })
      })
    },
    // IM接收消息
    receiveMsg(event) {
      // console.log('===receiveMsg========' + 206, event.data[0])
      // const payload = JSON.parse(event.data[0].payload.text)
      // const content = JSON.parse(payload.content)
      // if (content.isDoctorSend) {
      //   this.uploadImglist.unshift(content)
      // } else {
      //   this.imglist.unshift(content)
      // }
    },
    // 图片上传
    handleSuccess(res, file) {
      if (res.code === 0) {
        this.$message.success('发送成功!')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片预览
    zoomIn(item) {
      this.dialogImageUrl = item
      this.dialogVisible = true
    },
    // 视频挂断调用方法
    HangUp() {
      const diagnoseData = this.diagnoseData
      for (const i in diagnoseData) {
        this.diagnosis += diagnoseData[i] + ','
      }
      const param = {
        roomId: this.roomId,
        diagnosis: this.diagnosis,
        // 诊断类型 1西医诊断；2中医诊断
        diagnosisType: 1
      }
      console.log('===========结束问诊接口传参==========', param)
      API.hangUp(param).then(response => {
        this.tim.logout()
      })
    },
    // 存参userId、roomId
    storage() {
      const params = {
        doctorName: this.userInfo.name,
        patientName: this.patientName,
        userId: this.userId,
        roomId: this.roomId,
        inquirerName: this.inquirerName,
        inquirerGender: this.inquirerGender,
        inquirerAgeStr: this.inquirerAgeStr,
        roomType: this.roomType
      }
      return params
    },
    // 存参sdkAppId、userSig
    getKey() {
      return {
        sdkAppId: this.sdkAppId,
        userSig: this.userSig
      }
    },
    // 获取第三方名字
    getNames(id) {
      const params = {
        userId: id
      }
      return API.getThirdNames(params)
    },
    // 视频问诊页面查看详情
    goDetail() {
      this.detailsVisible = true
      if (this.diseaseId) {
        this.typeShow1 = true
        this.typeShow2 = false
        this.title = '病情详情'
        const params = {
          diseaseId: this.diseaseId
        }
        API.getDiseaseDetail(params).then(res => {
          this.detail = res
          if (res.offlineERImgs.length !== 0) {
            this.offlineERImgs = res.offlineERImgs
          } else {
            this.offlineERImgs = null
          }
          if (res.offlineDiagnosisImgs.length !== 0) {
            this.offlineDiagnosisImgs = res.offlineDiagnosisImgs
          } else {
            this.offlineDiagnosisImgs = null
          }
          if (res.descriptionImgs.length !== 0) {
            this.descriptionImgs = res.descriptionImgs
          } else {
            this.descriptionImgs = null
          }
          if (res.drugsImgs.length !== 0) {
            this.drugsImgs = res.drugsImgs
          } else {
            this.drugsImgs = null
          }
          if (res.offlineERPdfs.length !== 0) {
            this.offlineERPdfs = res.offlineERPdfs
          } else {
            this.offlineERPdfs = null
          }
        })
      }
      if (this.caseId) {
        this.typeShow1 = false
        this.typeShow2 = true
        this.title = '病历详情'
        const params = {
          recoreId: this.caseId
        }
        API.getCaseDetail(params).then(res => {
          // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 0) {
            res.drCaseVO.menstrual.status = '未初潮'
          }
          // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 1) {
            res.drCaseVO.menstrual.status = '已初潮'
          }
          // eslint-disable-next-line eqeqeq
          if (res.drCaseVO.menstrual.status == 2) {
            res.drCaseVO.menstrual.status = '已绝经'
          }
          this.casedetail = res.drCaseVO
          this.menstrual = res.drCaseVO.menstrual
          this.signInfo = res.signInfo
          if (res.drCaseVO.imgList.length !== 0) {
            this.casedetailImgs = res.drCaseVO.imgList
          } else {
            this.casedetailImgs = null
          }
        })
      }
    },
    onLineRecordBtn() {
      this.onlineMedicalRecordUrl = sessionStorage.getItem('onlineMedicalRecordUrl')
      if (this.onlineMedicalRecordUrl === 'null') {
        this.$message('暂无互联网医院就诊历史记录！')
      } else {
        this.onLineRecordVisible = true
      }
    },
    offLineRecordBtn() {
      console.log(this.offlineMedicalRecordUrl, 677)
      if (this.offlineMedicalRecordUrl === 'null') {
        this.$message('暂无' + process.env.VUE_APP_BASE_COMPANY + '就诊记录')
      } else {
        this.offLineRecordVisible = true
      }
    },
    backBtn() {
      const timeTtamp = Date.parse(new Date())
      const iframe_url = sessionStorage.getItem('onlineMedicalRecordUrl')
      this.onlineMedicalRecordUrl = iframe_url + '&timeTtamp=' + timeTtamp
    },
    // closeBtn() {
    //   this.$confirm('您确认结束问诊吗？', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     this.$refs.iframe_chat.contentWindow.inquiryClose()
    //     this.$message.success('问诊已结束')
    //   })
    // },
    closeBtn() {
      this.diagnoseVisible = true
    },
    confirmClose() {
      this.$refs.iframe_chat.contentWindow.inquiryClose()
      this.$message.success('问诊已结束')
      sessionStorage.removeItem('acceptsData')
      sessionStorage.removeItem('doctorId')
      sessionStorage.removeItem('videoConsultId')
      this.$router.push({
        path: '/doctor/index?Status=1'
      })
    },
    // 诊断搜索
    remoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        const params = {
          doctorId: this.doctorId,
          key: query
        }
        API.getDiagnoseData(params).then(response => {
          this.searchLoading = false
          this.optionsArr = response.result.map(item => {
            return { value: `${item.name}`, label: `${item.name}` }
          })
          this.diagnoseOptions = this.optionsArr.filter(item => {
            return item.label.toLowerCase()
              .indexOf(query.toLowerCase()) > -1
          })
        })
      } else {
        this.diagnoseOptions = []
      }
    },

    // 测试（vue页面吊一哦那个iframe页面中的方法）
    // getIframeChat() {
    //   const a = this.$refs.iframe_chat.contentWindow.exportIframe()
    //   const aa = this.$refs.iframe_chat.contentWindow.genTestUserSig('0011')
    // },
    getChatList() {
      if (!this.hasNext) {
        return false
      }
      this.queryStatus = true
      this.listQuery.patientId = this.user.patientId
      this.listQuery.patientUserId = this.user.id
      this.listQuery.sessionId = this.user.sessionId
      // listQuery
      // let _chartTime = 0
      const height = this.$refs.chat_scoll.$el.scrollHeight
      console.log('scollBottom', this.$refs.chat_scoll.$el.scrollHeight, this.$refs.chat_scoll.$el.offsetHeight)
      this.$store.dispatch('mqtt/getMessageList', this.listQuery).then(response => {
        this.hasNext = response.hasNext
        this.baseUrl = response.baseUrl
        this.chartTime = 0
        if (!this.listQuery.sendTime) {
          // this.totalPage = response.totalPage
          // this.consultList = response.list
          this.scollBottom()
        } else {
          this.scollBottom(height)
          // this.consultList = response.list.concat(this.consultList)
        }
      }).catch(e => {
      }).finally(() => {
        this.queryStatus = false
      })
    }
  }
}
</script>
