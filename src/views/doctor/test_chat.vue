<template>
  <div class="center-page">
    <div
      :class="remoteStream?'distant-stream':''"
      v-html="remoteStream"
    >
    </div>
    <div
      id="local_stream"
      class="local-stream"
    >
    </div>
  </div>
</template>

<style lang="less" scoped>
  @import "../../assets/css/doctor";
</style>
<script>
import { mapGetters } from 'vuex'
import API from '@/api/doctor/index'
import { getToken, getTokenName } from '@/utils/auth'
import { RtcClient } from '@/utils/trtc/rtc-client'
import TRTC_CLI from '@/utils/trtc/index'
import { ShareClient } from '@/utils/trtc/share-client'
import { genTestUserSig } from '@/utils/trtc/GenerateTestUserSig'
import TRTC from 'trtc-js-sdk'
export default {
  name: 'Project',
  components: {
  },
  data() {
    return {
      fullscreenLoading: false,
      userId: 'user_' + parseInt(Math.random() * 100000000), //用户id --可更改
      roomId: 888888, //房间号--加入相同房间才能聊
      client: '', //客户端服务
      remoteStream: '', //远方播放流
      localStream: '' //本地流
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'patient', 'messageId', 'unreadMsgCount', 'webRTCStatus', 'webRTCCurrentSession', 'webRTCCurrentSessionStatus'])
  },
  watch: {
    user(newVal, oldVal) {
      console.log('user 改变', newVal, oldVal)
      this.changeUser()
    }
  },
  created() {
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    // this.$store.dispatch('mqtt/disconnect')
  },
  methods: {
    init() {
      TRTC_CLI.init()
      const options = {}
      options.userId = '11111'
      const config = genTestUserSig(options.userId)
      options.sdkAppId = config.sdkAppId
      options.userSig = config.userSig
      options.roomId = 1111
      this.rtc = new RtcClient(options)
      this.join()
      this.shareUserId = options.userId
      this.share = new ShareClient(options)
    },
    getList() {
      const params = {}

    },
    join() {
      this.rtc.join()
    },

    leave() {
      this.rtc.leave()
      this.share.leave()
    }
  }
}
</script>
