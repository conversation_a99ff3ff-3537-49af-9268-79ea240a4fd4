const getters = {
  sidebar: state => state.app.sidebar,
  language: state => state.app.language,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  tokenName: state => state['system/user'].tokenName,
  token: state => state['system/user'].token,
  user: state => state['system/user'].user,
  userInfo: state => state['system/user'].userInfo,
  avatar: state => state['system/user'].avatar,
  roles: state => state['system/user'].roles,
  permissions: state => state['system/user'].permissions,
  permission_routes: state => state.permission.routes,
  addRoutes: state => state.permission.addRoutes,
  errorLogs: state => state.errorLog.logs,
  // messages: state => state.mqtt.messages
  messages: state => {
    const messagesList = state.mqtt.messages
    console.log(messagesList, 999)
    if (messagesList && messagesList.length) {
      const imgArr = []
      const imgList = []
      for (const j in messagesList) {
        if (JSON.parse(messagesList[j].i) !== 1) {
          const obj = JSON.parse(messagesList[j].i)
          // imgArr.push(obj)
          imgArr.unshift(obj)
        }
      }
      console.log(imgArr, 888)
      for (const i in imgArr) {
        if (JSON.parse(imgArr[i].content)) {
          const obj = JSON.parse(imgArr[i].content)
          imgList.push(obj)
        }
      }
      if (sessionStorage.getItem('acceptsData') && sessionStorage.getItem('acceptsData').length) {
        console.log(sessionStorage.getItem('acceptsData'), 777)
        const acceptsData = JSON.parse(sessionStorage.getItem('acceptsData'))
        const imgUpload = imgList.concat(acceptsData)
        const arr = [] //数组中的对象去重
        for (const val of imgUpload) {
          if (!arr.some(item => item.id === val.id)) {
            arr.push(val)
          }
        }
        console.log(arr, '====图片消息记录（包括mqtt）=====')
        return arr
      }
    } else {
      if (sessionStorage.getItem('acceptsData') && sessionStorage.getItem('acceptsData').length) {
        console.log(sessionStorage.getItem('acceptsData'), 4444)
        const acceptsData = JSON.parse(sessionStorage.getItem('acceptsData'))
        console.log(acceptsData, '====图片消息记录（包括mqtt）2=====')
        return acceptsData
      }
    }
  }
}
export default getters
