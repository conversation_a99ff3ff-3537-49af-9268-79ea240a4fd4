import { logout } from '@/api/system/user'
import { login, checkLogin } from '@/api/system/login'
import {
  getToken,
  setToken,
  removeToken,
  getTokenName,
  setTokenName,
  removeTokenName,
  getUserInfo,
  setUserInfo
} from '@/utils/auth'
import { resetRouter, router } from '@/router'
//import WarterMark from '@/utils/warterMark'

const state = {
  tokenName: getTokenName(),
  token: getToken(),
  user: null,
  userInfo: getUserInfo(),
  avatar: '/static/images/user.jpg',
  roles: [],
  permissions: []
}

const mutations = {
  SET_TOKENNAME: (state, tokenName) => {
    state.tokenName = tokenName
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USER: (state, user) => {
    state.user = user
    console.log(user)
    // WarterMark.set(user.name)
  },
  SET_USERINFO: (state, userinfo) => {
    state.userInfo = userinfo
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo)
        .then(response => {
          console.log('login', response)
          if (response.token) {
            commit('SET_TOKENNAME', response.headerName)
            commit('SET_TOKEN', response.token)
            commit('SET_USERINFO', response.user)
            setTokenName(response.headerName)
            setToken(response.token)
            setUserInfo(response.user)
          }
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  checkLogin({ commit }, params) {
    return new Promise((resolve, reject) => {
      checkLogin(params)
        .then(response => {
          console.log('login', response)
          if (response.token) {
            commit('SET_TOKENNAME', response.headerName)
            commit('SET_TOKEN', response.token)
            commit('SET_USERINFO', response.user)
            setTokenName(response.headerName)
            setToken(response.token)
            setUserInfo(response.user)
          }
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      //权限 暂时不从后台获取，
      const response = { roles: [{}], permissions: [] }
      const { roles, permissions } = response

      // roles must be a non-empty array
      if (!roles || roles.length <= 0) {
        reject('getInfo: roles must be a non-null array!')
      }
      commit('SET_ROLES', roles)
      commit('SET_PERMISSIONS', permissions)
      commit('SET_USER', response)
      resolve(response)
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          removeTokenName()
          removeToken()
          sessionStorage.removeItem('userRecommed')
          resetRouter()
          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeTokenName()
      removeToken()
      resolve()
    })
  },

  // Dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const { roles } = await dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch('permission/generateRoutes', roles, {
        root: true
      })

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      resolve()
    })
  },
  // 更新用户数据
  setUserInfo({ commit }, userInfo) {
    commit('SET_USERINFO', userInfo)
    setUserInfo(userInfo)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
