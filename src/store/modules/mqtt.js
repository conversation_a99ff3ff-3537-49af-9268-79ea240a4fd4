import API from '@/api/doctor/index'
import <PERSON>ho from 'paho-mqtt'
const state = {
  messages: [],
  client: null,
  mqtt: {
    isconnect: false,
    limit: 0,
    _LIMIT: 3,
    oldLimit: 0
  },
  connectOptions: {},
  params: {},
  messageId: 0,
  baseUrl: '',
  connctionTime: {
    start: 0,
    end: 0
  }
}

const mutations = {
  SET_MESSAGE: (state, messages) => {
    state.messages = messages
  },
  PUT_MESSAGE: (state, messages) => {
    state.messages = messages.concat(state.messages)
    console.log(state.messages)
  },
  ADD_MESSAGE: (state, message) => {
    console.log(message, 31)
    const len = state.messages.length
    let flag = false
    //message 去重 向前推10条没有重复的就加入
    if (message.id) {
      for (let index = len - 1; index >= 0; index--) {
        if ((len - index) > 10) {
          break
        }
        const item = state.messages[index]
        if (item.id === message.id) {
          flag = true
          break
        }
      }
    }
    if (!flag) {
      state.messages.push(message)
    }
    console.log(state.messages, 49)
  }
}

const actions = {
  getMessageList({ state, commit }, params) {
    if (!params.sendTime) {
      commit('PUT_MESSAGE', [])
    }
    return new Promise((resolve, reject) => {
      API.getChatList(params)
        .then(response => {
          console.log('getMessageList', response)
          //拼接url
          if (response && response.result) {
            state.baseUrl = response.baseUrl
            response.result = response.result.reverse()
            response.result = response.result.map(item => {
              if (item.type === 2) {
                item.content.path = response.baseUrl + item.content.path
              } else if (item.type === 17) {
                item.content.illness.fileUrls = item.content.illness.fileUrls.map(url => {
                  if (url.indexOf('https://') === -1 && url.indexOf('http://') === -1) {
                    url = response.baseUrl + url
                  }
                  return url
                })
              }
              return item
            })
          }
          if (params.sendTime) {
            if (response && response.result) {
              commit('PUT_MESSAGE', response.result)
            }
          } else {
            if (response && response.result) {
              commit('SET_MESSAGE', response.result)
            } else {
              commit('SET_MESSAGE', [])
            }
          }
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  addMessage({ commit }, message) {
    commit('ADD_MESSAGE', message)
  },
  setUser({ commit }, user) {
    commit('SET_USER', user)
  },
  /**
   * add by sunlc 2020年9月21日 连接mqtt
   * @param {*} store
   * @param {*} 连接参数
   */
  initMqtt({ commit, state, dispatch }, params) {
    const t = {}
    t.i = params.msgTopicName
    t.c = 0
    t.t = 3
    t.p = 1
    const message = new Paho.Message(JSON.stringify(t))
    message.destinationName = params.willTopicName // willTopicName

    state.connectOptions.userName = process.env.VUE_APP_MQTT_USER//连接mqtt用户名
    state.connectOptions.password = process.env.VUE_APP_MQTT_PASS//连接mqtt密码
    console.log(process.env.VUE_APP_MQTT_PASS, 121)
    state.connectOptions.timeout = params.connectionTimeout // 默认30s
    state.connectOptions.willMessage = message //遗嘱消息
    state.connectOptions.keepAliveInterval = 10 //params.keepAliveInterval //this.globalData.connectParams.keepAliveInterval; //心跳保持时间
    state.connectOptions.cleanSession = params.cleanSession //断开连接时是否要清除session
    state.connectOptions.reconnect = false //设置如果连接丢失，客户端是否自动尝试重新连接到服务器
    //连接成功回调
    state.connectOptions.onSuccess = e => {
      console.log('mqtt连接成功 - onConnect')

      state.mqtt.isconnect = true
      state.mqtt.oldLimit = state.mqtt.limit
      state.mqtt.limit = 0
      state.connctionTime.start = new Date() - 0
      state.client.subscribe(params.privatePushTopicName, { qos: 2 }) //pt_1234
      //'上线';
      const t = {}
      t.i = params.msgTopicName
      t.c = 0
      t.t = 0
      t.p = 1
      const message = new Paho.Message(JSON.stringify(t))
      message.destinationName = params.statusTopicName // statusTopicName
      //发送上线消息
      state.client.send(message)
    }
    //连接失败回调
    state.connectOptions.onFailure = e => {
      state.mqtt.isconnect = false
      console.log('mqtt - disConnect', e)
      if (e.errorCode === 8) {
        console.log(e)
      }
      clearTimeout(state.mqtt.timer)
      if (state.mqtt.limit < state.mqtt._LIMIT) {
        state.mqtt.timer = setTimeout(() => {
          dispatch('reConnect')

          state.mqtt.limit++

        }, 3000 * state.mqtt.limit)
      }
    }
    console.log(process.env, 162)
    const url = 'wss://' + params.dnHost + '/mqtt'
    console.log(url, 164)
    state.params = params
    state.client = new Paho.Client(url, params.clientId)
    console.log(url, params.clientId)
    state.client.onConnectionLost = responseObject => {
      if ((new Date() - state.connctionTime.start) < 3000) {
        state.mqtt.limit = state.mqtt.oldLimit
      }
      // wx.closeSocket();
      state.mqtt.isconnect = false

      console.log('mqtt失去连接 - responseObject:', responseObject, state.client)
      if (responseObject.errorCode !== 0) {
        clearTimeout(state.mqtt.timer)
        if (state.mqtt.limit < state.mqtt._LIMIT) {
          state.mqtt.timer = setTimeout(() => {
            state.mqtt.limit++
            dispatch('reConnect')
          }, 3000 * state.mqtt.limit)
        }
        console.log('onConnectionLost:' + responseObject.errorMessage)
      }
      // 其他地方登陆，被踢掉
      if (responseObject.errorCode === 8) {
        // 重新登录
        // dispatch('system/user/resetToken', null, { root: true }).then(() => {
        //   location.reload() // 为了重新实例化vue-router对象 避免bug
        // })
      }
    }
    state.client.onMessageArrived = message => {
      console.log('mqtt消息到达 - onMessageArrived:' + message.payloadString, message, message.topic)
      const _message = JSON.parse(message.payloadString)
      commit('ADD_MESSAGE', _message)
      console.log(_message)

    }
    console.log(state.connectOptions, 'state.connectOptions')
    state.mqtt.isconnect = false
    state.client.connect(state.connectOptions)
  },
  reConnect({ state, dispatch }) {
    const params = state.params
    console.log('重新连接reConnect', state.client)
    if (state.client) {
      dispatch('initMqtt', params)
      // state.client.connect(state.connectOptions)
    } else {
      dispatch('initMqtt', params)
    }
  },
  disconnect({ state }) {
    const params = state.params
    console.log('disconnect', params)
    //'下线';
    const t = {}
    t.i = params.msgTopicName
    t.c = 0
    t.t = 1
    t.p = 1
    const message = new Paho.Message(JSON.stringify(t))
    message.destinationName = params.statusTopicName
    //发送离线消息
    state.client.send(message)
    //断开MQTT连接
    setTimeout(() => {
      state.client.disconnect()
      state.mqtt.isconnect = false
    }, 0)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
