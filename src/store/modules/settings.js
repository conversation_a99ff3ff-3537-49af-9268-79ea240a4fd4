import variables from '@/styles/element-variables.scss'
import defaultSettings from '@/settings'

const {
  tagsView,
  fixedHeader,
  sidebar<PERSON>ogo,
  showRightPanel,
  uniqueOpened,
  settingBtn
} = defaultSettings

const state = {
  theme: variables.theme,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  showRightPanel: showRightPanel,
  uniqueOpened: uniqueOpened,
  settingBtn: settingBtn
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (Object.prototype.hasOwnProperty.call(state, key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
