import { asyncRoutes } from './router'
import router from './router'
import store from './store'
// import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/login/success'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()
  if (to.meta.title) {
    document.title = getPageTitle(to.meta.title)
  }
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      console.log(store.getters.roles, 'store.getters.roles')
      if (hasRoles) {
        next()
      } else {
        const { roles } = await store.dispatch('system/user/getInfo')
        console.log(to.path, 'path')
        // generate accessible routes map based on roles
        const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
        console.log(accessRoutes, 'accessRoutes')
        // dynamically add accessible routes
        router.addRoutes(accessRoutes)
        next()
      }

    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  const hasToken = getToken()
  if (hasToken && asyncRoutes.length === 1) {
    NProgress.done()
  } else {
    NProgress.done()
  }
})
