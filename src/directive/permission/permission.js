
import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const roles = store.getters && store.getters.roles
    const hasAdmin = roles.some(role => {
      return 'admin'.includes(role)
    })
    if (hasAdmin) {
      // return true
    }
    const { value } = binding
    const userPermissions = store.getters && store.getters.permissions
    if (value && value instanceof Array && value.length > 0) {
      const permission = value

      const hasPermission = userPermissions.some(dbpermission => {
        return permission.includes(dbpermission)
      })

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`need roles! Like v-permission="['admin','editor']"`)
    }
  }
}
