import axios from 'axios'
import { Message } from 'element-ui'
import qs from 'qs'

// create an axios instance
const service = axios.create({
  baseURL: '', // 由于此处可能存在多个不同的域名，故不在此处统一设置
  withCredentials: true, // 跨域请求时发送 cookies
  timeout: 30000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // 可在此处注入鉴权信息
    // formdata提交数据
    const headers = config.headers
    if (headers && Object.values(headers).includes('application/x-www-form-urlencoded')) {
      config.data && (config.data = qs.stringify(config.data))
    }
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get information such as headers or status
   * Please return  response => response
   */
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 XMLHttpRequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  response => {
    const respData = response.data
    console.log('request-yf respData: ', respData)
    return respData
  },
  error => {
    console.error('err' + error) // for debug
    if (error.response) {
      const res = error.response.data
      if (res.code) {
        Message({
          message: res.msg,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      if (error.message.indexOf('timeout of') === 0) {
        Message({
          message: '请求超时，请检查网络或重试。',
          type: 'error',
          duration: 5 * 1000
        })
      } else if (error.message.indexOf('Network Error') === 0) {
        Message({
          message: '网络错误。',
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        Message({
          message: error.message,
          type: 'error',
          duration: 5 * 1000
        })
      }
    }
    return Promise.reject(error)
  }
)

export default service
