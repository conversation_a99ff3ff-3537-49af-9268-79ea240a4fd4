// import Cookies from 'js-cookie'

const TokenName = 'tokenName'
const TokenKey = 'tokenValue'
const UserInfo = 'userInfo'

export function getToken() {
  return sessionStorage.getItem(TokenKey)
  // return '6006aad20a22a4001b98efd4'
}

export function setToken(token) {
  return sessionStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return sessionStorage.removeItem(TokenKey)
}

export function removeTokenName() {
  return sessionStorage.removeItem(TokenName)
}

export function getTokenName() {
  return sessionStorage.getItem(TokenName)
  // return '6006aad20a22a4001b98efd4'
}

export function setTokenName(tokenName) {
  return sessionStorage.setItem(TokenName, tokenName)
}

export function getUserInfo() {
  return JSON.parse(sessionStorage.getItem(UserInfo) || '{}')
}

export function setUserInfo(userInfo) {
  return sessionStorage.setItem(UserInfo, JSON.stringify(userInfo))
}
