export default {
  /**
   * 对Date的扩展，将 Date 转化为指定格式的String
   * 月(M)、日(d)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q) 可以用 1-2 个占位符
   * 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
   * eg:
   * (new Date()).pattern('yyyy-MM-dd hh:mm:ss.S') ==> 2006-07-02 08:09:04.423
   * (new Date()).pattern('yyyy-MM-dd E HH:mm:ss') ==> 2009-03-10 二 20:09:04
   * (new Date()).pattern('yyyy-MM-dd EE hh:mm:ss') ==> 2009-03-10 周二 08:09:04
   * (new Date()).pattern('yyyy-MM-dd EEE hh:mm:ss') ==> 2009-03-10 星期二 08:09:04
   * (new Date()).pattern('yyyy-M-d h:m:s.S') ==> 2006-7-2 8:9:4.18
   */
  dateFormat: function(fmt, dates) {
    var date
    if (dates) {
      date = new Date(dates)
    } else {
      date = new Date()
    }
    var o = {
      'M+': date.getMonth() + 1, //月份
      'd+': date.getDate(), //日
      'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, //小时
      'H+': date.getHours(), //小时
      'm+': date.getMinutes(), //分
      's+': date.getSeconds(), //秒
      'q+': Math.floor((date.getMonth() + 3) / 3), //季度
      S: date.getMilliseconds() //毫秒
    }
    var week = {
      '0': '/u65e5',
      '1': '/u4e00',
      '2': '/u4e8c',
      '3': '/u4e09',
      '4': '/u56db',
      '5': '/u4e94',
      '6': '/u516d'
    }
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        (date.getFullYear() + '').substr(4 - RegExp.$1.length)
      )
    }
    if (/(E+)/.test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        (RegExp.$1.length > 1
          ? RegExp.$1.length > 2
            ? '/u661f/u671f'
            : '/u5468'
          : '') + week[date.getDay() + '']
      )
    }
    for (var k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1
            ? o[k]
            : ('00' + o[k]).substr(('' + o[k]).length)
        )
      }
    }
    return fmt
  },
  //克隆对象
  objClone: function(obj) {
    return JSON.parse(JSON.stringify(obj))
  },
  formatChatTime: function(receiverTime, type = true) {
    if (!receiverTime) {
      return ''
    }
    var systemTime = new Date().getTime()
    if (type) {
      var d = receiverTime.split('-')
      var y = d[2].split(' ')
      d[2] = y[0]
      d[3] = y[1]
      y = d[3].split(':')
      d[3] = y[0]
      d[4] = y[1]
      d[5] = y[2]
      receiverTime = new Date(d[0], d[1] - 1, d[2], d[3], d[4], d[5]).getTime()
    }
    var showTime = '刚刚'
    var time = (systemTime - receiverTime) / 1000
    if (time < 60 && time >= 0) {
      showTime = '刚刚'
    } else if (time >= 60 && time < 3600) {
      showTime = parseInt(time / 60) + '分钟前'
    } else if (time >= 3600 && time < 3600 * 24) {
      showTime = parseInt(time / 3600) + '小时前'
    } else if (time >= 3600 * 24 && time < 3600 * 24 * 2) {
      showTime = '昨天'
    } else if (time >= 3600 * 24 * 2 && time < 3600 * 24 * 30) {
      showTime = parseInt(time / 3600 / 24) + '天前'
    } else if (time >= 3600 * 24 * 30 && time < 3600 * 24 * 30 * 12) {
      showTime = parseInt(time / 3600 / 24 / 30) + '个月前'
    } else if (time >= 3600 * 24 * 30 * 12) {
      showTime = parseInt(time / 3600 / 24 / 30 / 12) + '年前'
    } else {
      showTime = '刚刚'
    }
    return showTime
  },
  getIdByIndex(id, list) {
    const index = list.findIndex(item => {
      if (item.sessionId === id) {
        return true
      } else {
        return false
      }
    })
    return index
  },
  isObj(x) {
    const type = typeof x
    return x !== null && (type === 'object' || type === 'function')
  },
  toObject(val) {
    if (val === null || val === undefined) {
      throw new TypeError('Cannot convert undefined or null to object')
    }
    return Object(val)
  },
  assignKey(to, from, key) {
    const val = from[key]
    if (val === undefined || val === null) {
      return
    }
    // if (Object.prototype.hasOwnProperty.call(to, key)) {
    //   if (to[key] === undefined || to[key] === null) {
    //     throw new TypeError('Cannot convert undefined or null to object (' + key + ')')
    //   }
    // }
    if (!Object.prototype.hasOwnProperty.call(to, key) || !this.isObj(val)) {
      to[key] = val
    } else {
      to[key] = this.assign(Object(to[key]), from[key])
    }
  },
  assign(to, from) {
    if (to === from) {
      return to
    }
    from = Object(from)
    for (const key in from) {
      if (Object.prototype.hasOwnProperty.call(from, key)) {
        this.assignKey(to, from, key)
      }
    }
    if (Object.getOwnPropertySymbols) {
      const symbols = Object.getOwnPropertySymbols(from)
      for (let i = 0; i < symbols.length; i++) {
        if (Object.prototype.propertyIsEnumerable.call(from, symbols[i])) {
          this.assignKey(to, from, symbols[i])
        }
      }
    }
    return to
  },
  deepAssign(target) {
    target = this.toObject(target)
    for (let s = 1; s < arguments.length; s++) {
      this.assign(target, arguments[s])
    }
    return target
  }
}
