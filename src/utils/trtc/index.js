/* eslint-disable no-global-assign */
/* global $ TRTC Presetting deviceTestingInit cameraId micId rtcDetection*/

// presetting of login card
import TRTC from 'trtc-js-sdk'
import { rtcDetection } from '@/utils/trtc/rtc-detection'
function init() {
  rtcDetection().then(detectionResult => {
    detectionResult && deviceTestingInit()
  })

  // setup logging stuffs
  TRTC.Logger.setLogLevel(TRTC.Logger.LogLevel.DEBUG)
  TRTC.Logger.enableUploadLog()

  TRTC.getDevices()
    .then(devices => {
      devices.forEach(item => {
        console.log('device: ' + item.kind + ' ' + item.label + ' ' + item.deviceId)
      })
    })
    .catch(error => console.error('getDevices error observed ' + error))

  // populate camera options
  TRTC.getCameras().then(devices => {
    devices.forEach(device => {
      console.log('cameraId', device.deviceId)
    })
  })

  // populate microphone options
  TRTC.getMicrophones().then(devices => {
    devices.forEach(device => {
      console.log('micId', device.deviceId)
    })
  })
}

export default {
  init
}
