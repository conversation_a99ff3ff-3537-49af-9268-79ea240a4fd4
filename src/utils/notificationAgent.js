/*
 * @Author: otto.wong
 * @Date: 2020-11-04 16:13:29
 * @Last Modified by: otto.wong
 * @Last Modified time: 2020-11-20 14:02:54
 */

const title = '医生'
const desc = '新消息来咯！请医生及时查看。'
const iconLogo = require('/logo/ic_company.png')
const iconTip = require('/logo/ic_company.png')
let notificationManager // = new NotificationManager()

/**
 * 判断当前环境是否支持通知
 */
const isNotificationSupported = () => {
  const supported = !!(window.Notification || window.mozNotification || window.webkitNotification)
  console.log('current browser support notification: ', supported)
  return supported
}

export const requestNotificationPermission = callback => {
  if (!isNotificationSupported()) {
    callback(false)
    return
  }
  if (!notificationManager) {
    notificationManager = new NotificationManager()
  }
  Notification.requestPermission(status => {
    if (status === 'granted') {
      callback(true)
    } else {
      callback(false)
    }
  })
}

class NotificationManager {
  init = false
  initTitle
  activeTitle = this.initTitle
  isWindowBackground = false
  isVisible = true
  msgCount = 0
  lastSendNotificationTime = 0
  intervalTime = 300
  tabRef
  tabInitIconUrl
  tabShineIconUrl = require('../assets/logo/yifeng-logo.gif')
  ignoreNotification = false // 是否忽略通知

  constructor() {
    this.initTitle = document.title
    const tabRef = document.getElementById('pageTabIcon')
    this.tabRef = tabRef
    this.tabInitIconUrl = tabRef.href

    this.bilingTitle()
    window.onblur = () => {
      console.log('onblur')
      this.isWindowBackground = true
    }
    window.onfocus = () => {
      console.log('onfocus')
      this.isWindowBackground = false
    }
    window.addEventListener('visibilitychange', function() {
      if (document.visibilityState === 'hidden') {
        this.isWindowBackground = true
        this.isVisible = false
        this.intervalTime = 200
        console.log('页面被隐藏')
      } else {
        this.isWindowBackground = false
        this.isVisible = true
        this.intervalTime = 300
        console.log('页面被展示')
      }
    })
  }

  /**
   * 标题闪烁
   */
  bilingTitle() {
    setInterval(() => {
      if (this.msgCount === 0) {
        document.title = this.initTitle
        this.tabRef.href = this.tabInitIconUrl
      } else {
        if (document.title === this.initTitle) {
          document.title = `您有${this.msgCount}条未读消息`
          this.tabRef.href = this.tabInitIconUrl
        } else {
          document.title = this.initTitle
          this.tabRef.href = this.tabShineIconUrl
        }
      }
    }, this.intervalTime)
  }

  sendNotification() {
    console.log('发送通知')
    console.log('isWindowBackground: ', this.isWindowBackground)
    console.log('isNotificationSupported: ', isNotificationSupported())
    if (!this.isWindowBackground || !isNotificationSupported()) {
      return
    }
    const currentTime = new Date().getTime()
    if (currentTime - this.lastSendNotificationTime < 30000) {
      return
    }
    this.lastSendNotificationTime = currentTime
    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body: desc,
        icon: iconTip, // 提醒图片 使用提示图标
        badge: iconLogo, // 角标小图标，使用logo
        tag: 'tag',
        renotify: true,
        requireInteraction: true
      })
      notification.onclick = () => {
        window.focus()
        notification.close()
      }
    } else {
      Notification.requestPermission(status => {
        if (status === 'granted') {
          const notification = new Notification(title, {
            body: desc,
            icon: iconTip, // 提醒图片 使用提示图标
            badge: iconLogo, // 角标小图标，使用logo
            tag: 'tag',
            renotify: true,
            requireInteraction: true
          })
          notification.onclick = () => {
            window.focus()
            notification.close()
          }
        } else {
          console.log('拒绝通知授权')
        }
      })
    }
  }

  updateUnreadMsgCount(msgCount) {
    console.log('updateUnreadMsgCount: ', msgCount)
    // 避免在窗口冻结后、定时器失效情况下不更新标题问题
    if (msgCount > 0) {
      document.title = `您有${msgCount}条未读消息`
    } else {
      //
    }
    this.msgCount = msgCount
    if (this.isWindowBackground) {
      console.log('窗口不可见或失去焦点')
    }
  }

  setIgnoreNotification(ignore) {
    console.log('setIgnoreNotification: ', ignore)
    this.ignoreNotification = ignore
  }
}

/**
 * 发送通知
 */
export const sendNotification = consultInfo => {

  if (!notificationManager) {
    notificationManager = new NotificationManager()
  }

  // 忽略通知，一般在医生主页焦点被抢情况下忽略
  if (notificationManager.ignoreNotification && notificationManager.isVisible) {
    return
  }

  console.log('consultInfo: ', consultInfo)
  if (!notificationManager) {
    notificationManager = new NotificationManager()
  }

  if (!isNaN(consultInfo)) {
    try {
      const msgCount = parseInt(consultInfo)
      notificationManager.updateUnreadMsgCount(msgCount)
      notificationManager.sendNotification()
    } catch (e) {
      //
    }
    return
  }

  if (!consultInfo) {
    notificationManager.sendNotification()
    return
  }

  const { consultUsers = [] } = consultInfo
  if (!consultUsers || consultUsers.length === 0) {
    notificationManager.updateUnreadMsgCount(0)
    return
  }

  let unreadMsgCount = 0 // 计算未读消息数量
  let hasNewUnHandleConsult = false // 是否有未处理的咨询
  consultUsers.forEach(item => {
    if (item.num !== undefined) {
      unreadMsgCount = unreadMsgCount + item.num
    } else {
      hasNewUnHandleConsult = true
    }
  })
  console.log('unreadMsgCount: ', unreadMsgCount)
  notificationManager.updateUnreadMsgCount(unreadMsgCount)
  if (unreadMsgCount > 0 || hasNewUnHandleConsult) {
    notificationManager.sendNotification()
  }
}

export const setIgnoreNotification = ignore => {
  if (!notificationManager) {
    notificationManager = new NotificationManager()
  }
  notificationManager.setIgnoreNotification(ignore)
}
