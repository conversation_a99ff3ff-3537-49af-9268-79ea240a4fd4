import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken, getTokenName, setToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // api 的 base_url
  withCredentials: true, // 跨域请求时发送 cookies
  timeout: 30000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // config.headers = {
    //   'Content-Type': config.headers['Content-Type'] || 'application/x-www-form-urlencoded',
    // }
    // Do something before request is sent
    if (store.getters.token) {
      // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
      config.headers[getTokenName()] = getToken()
      config.headers['_o'] = '6'
      config.headers['_p'] = '2'
      config.headers['_w'] = '1'
    }
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get information such as headers or status
   * Please return  response => response
   */
  /**
   * 下面的注释为通过在response里，自定义code来标示请求状态
   * 当code返回如下情况则说明权限有问题，登出并返回到登录页
   * 如想通过 XMLHttpRequest 来状态码标识 逻辑可写在下面error中
   * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
   */
  response => {
    const res = response.data
    // token 刷新机制
    if (res.token) {
      setToken(res.token)
    }
    if (res.code !== 0) {
      //强制修改密码
      if (res.code === 2) {
        MessageBox.alert(res.msg, '警告', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          console.info('需要修改密码')
        })
      } else if (
        res.code === 1 ||
        res.code === 50008 ||
        res.code === 50012 ||
        res.code === 50014
      ) {
        // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
        // 请自行在引入 MessageBox
        // import { Message, MessageBox } from 'element-ui'
        store.dispatch('system/user/resetToken').then(() => {
          location.reload() // 为了重新实例化vue-router对象 避免bug
        })
        // MessageBox.confirm(
        //   '你已被登出，可以取消继续留在该页面，或者重新登录',
        //   '确定登出',
        //   {
        //     confirmButtonText: '重新登录',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        //   }
        // ).then(() => {
        //   store.dispatch('system/user/resetToken').then(() => {
        //     location.reload() // 为了重新实例化vue-router对象 避免bug
        //   })
        // })
      } else {
        Message({
          message: res.msg || 'error',
          type: 'error',
          duration: 5 * 1000
        })
      }
      return Promise.reject(res.msg || 'error')
    } else {
      return res.data
    }
  },
  error => {
    console.error('err' + error) // for debug
    if (error.response) {
      const res = error.response.data
      if (res.code) {
        Message({
          message: res.msg,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      if (error.message.indexOf('timeout of') === 0) {
        Message({
          message: '请求超时，请检查网络或重试。',
          type: 'error',
          duration: 5 * 1000
        })
      } else if (error.message.indexOf('Network Error') === 0) {
        Message({
          message: '网络错误。',
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        Message({
          message: error.message,
          type: 'error',
          duration: 5 * 1000
        })
      }
    }
    return Promise.reject(error)
  }
)

export default service
