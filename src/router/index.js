import Vue from 'vue'
import Router from 'vue-router'

/* Layout */
import Layout from '@/layout'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/errorPage/401'),
    hidden: true
  },
  /*{
    path: '',
    component: Layout,
    redirect: 'dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {
          title: '首页',
          icon: 'dashboard',
          noCache: true,
          affix: true
        }
      }
    ]
  },*/
  {
    path: '/user/center',
    component: Layout,
    redirect: '/user/center',
    hidden: true,
    children: [
      {
        path: '/user/center',
        component: () => import('@/views/system/user/center'),
        name: '个人中心',
        meta: {
          title: '个人中心',
          icon: 'user',
          noCache: true,
          affix: false
        }
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/doctor/index',
    name: '医生问诊',
    // hidden: true,
    children: [
      {
        path: '/doctor/index',
        component: () => import('@/views/doctor/index'),
        name: '医生问诊',
        meta: { title: '医生问诊', icon: 'link', noCache: true, affix: false }
      }
    ]
  },
  // {
  //   path: '/doctor/index1',
  //   component: () => import('@/views/doctor/index1'),
  //   name: '医生问诊1',
  //   meta: { title: '医生问诊1', icon: 'link', noCache: true, affix: false },
  //   hidden: true
  // },
  {
    path: '/doctor/success',
    component: () => import('@/views/doctor/success'),
    name: '成功',
    meta: { title: '成功', icon: 'link', noCache: true, affix: false },
    hidden: true
  },
  {
    path: '/doctor/cfSuccess',
    component: () => import('@/views/doctor/cfSuccess'),
    name: '成功',
    meta: { title: '成功', icon: 'link', noCache: true, affix: false },
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/doctor/chat',
    name: '问诊',
    // hidden: true,
    children: [
      {
        path: '/doctor/chat',
        component: () => import('@/views/doctor/chat'),
        name: '问诊',
        meta: { title: '问诊', icon: 'link', noCache: true, affix: false }
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/doctor/videoSpecial',
    name: '视频问诊',
    // hidden: true,
    children: [
      {
        path: '/doctor/videoSpecial',
        component: () => import('@/views/doctor/videoSpecial'),
        name: '视频问诊',
        meta: { title: '视频问诊', icon: 'link', noCache: true, affix: false }
      }
    ]
  },
  {
    path: '/login/success',
    component: () => import('@/views/login/success'),
    name: '成功',
    meta: { title: '成功', icon: 'link', noCache: true, affix: false },
    hidden: true
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]
export function restAsyncRoutes() {
  asyncRoutes.splice(1, asyncRoutes.length)
}

const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  restAsyncRoutes()
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export const loadView = view => {
  // 路由懒加载
  return () => import(`@/views/${view}`)
}

export default router
