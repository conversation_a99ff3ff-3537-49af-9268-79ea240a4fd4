import request from '@/utils/request'
import qs from 'qs'
function getPatientList(data) {
  return request({
    url: '/consult/users',
    method: 'get'
  })
}
function getChatList(data) {
  return request({
    url: '/consult/historys',
    method: 'get',
    params: data
  })
}
function getPrescription(id) {
  return request({
    url: '/consult/orders/' + id,
    method: 'get'
  })
}

function sendPrescription(data) {
  return request({
    url: '/prescription/save',
    method: 'post',
    data
  })
}

function rejectPrescription(data) {
  return request({
    url: '/prescription/reject',
    method: 'post',
    data
  })
}
function searchDiagnosis(data) {
  return request({
    url: '/prescription/diagnosis',
    method: 'get',
    params: data
  })
}
function sendMsg(data) {
  return request({
    url: '/consult/send',
    method: 'post',
    data: qs.stringify(data)
  })
}
function uploadURL() {
  return process.env.VUE_APP_BASE_API + '/consult/media/upload'
}
function closeChat(data) {
  return request({
    url: '/consult/close',
    method: 'post',
    data: qs.stringify(data)
  })
}
function searchDrugs(data) {
  return request({
    url: '/prescription/product/search',
    method: 'get',
    params: data
  })
}
function getNewImg(id) {
  return request({
    url: '/prescription/img/' + id,
    method: 'get'
  })
}
// 验证码
function getCodeImg(data) {
  return request({
    url: '/pc/auth/code',
    method: 'get'
  })
}
// 短信验证码
function getVerification(data) {
  return request({
    url: '/pc/auth/smscode',
    method: 'post',
    data
  })
}
// cms登录
function loginCms(data) {
  return request({
    url: '/pc/auth/login',
    method: 'post',
    data
  })
}
// 获取主页面列表
function getList(data) {
  return request({
    url: '/vc/video/consult/doctor/consult/list',
    method: 'get',
    params: data
  })
}
// 获取主页面列表
function getMeetingList(data) {
  return request({
    url: '/vc/group/consult/info',
    method: 'get',
    params: data
  })
}
// 病情详情
function getDiseaseDetail(data) {
  return request({
    url: '/emr/patient/disease/detail',
    method: 'post',
    params: data
  })
}
// 病历详情
function getCaseDetail(data) {
  return request({
    url: '/emr/record/medicalRecord',
    method: 'post',
    params: data
  })
}
// 获取appId和key
function getKey(data) {
  return request({
    url: '/vc/video/consult/getAppIdAndKey',
    method: 'get',
    params: data
  })
}
// 接诊
function accepts(data) {
  return request({
    url: '/vc/video/consult/accept',
    method: 'post',
    params: data
  })
}
// 拒绝原因列表
function reasonList(data) {
  return request({
    url: '/vc/video/consult/refusal/reason/list',
    method: 'get',
    params: data
  })
}
// 拒绝
function refuse(data) {
  return request({
    url: '/vc/video/consult/refuse',
    method: 'post',
    params: data
  })
}
// 挂断trtc视频
function hangUp(data) {
  return request({
    url: '/vc/video/consult/finish',
    method: 'post',
    data
  })
}
// 第三方进入房间号
function acceptsThird(data) {
  return request({
    url: '/vc/video/consult/room/config',
    method: 'get',
    params: data
  })
}
// 获取第三方名字
function getThirdNames(data) {
  return request({
    url: '/vc/video/consult/user/info',
    method: 'get',
    params: data
  })
}
// 查看病历
function getLookCase(data) {
  return request({
    url: '/vc/video/consult/case/pdf',
    method: 'get',
    params: data
  })
}
// 查看处方列表
function getLookPrescrip(data) {
  return request({
    url: '/vc/video/consult/recom/list',
    method: 'get',
    params: data
  })
}
// 完善病历提交
function submitCaseForm(data, obj) {
  return request({
    url: '/emr/case/saveDrCase',
    method: 'post',
    // params: data
    data: qs.stringify({ ...data, ...obj })
  })
}
// 填写病历页初始化信息
function initCaseInterface(data) {
  return request({
    url: '/medical/emr/case/initCase',
    method: 'post',
    // params: data
    data: qs.stringify(data)
  })
}
function getNoSecret(data) {
  return request({
    url: 'ad/esign/user/getNoSecret',
    method: 'get',
    params: data
  })
}
// 病历签名
function sign(data) {
  return request({
    url: '/emr/case/sign',
    method: 'post',
    params: data
  })
}
// 病历确认签名
function confirmSign(data) {
  return request({
    url: '/emr/case/confirm',
    method: 'post',
    params: data
  })
}
// 开具处方提交
function submitPrescripForm(data, doctorId) {
  return request({
    url: '/recommend/recom/safe/save?doctorId=' + doctorId,
    method: 'post',
    data
  })
}
// 校验处方发送条件
function send(data, obj) {
  return request({
    url: '/recommend/recom/verify/send',
    method: 'get',
    params: data
  })
}
// 处方签名
function Cfsign(data) {
  return request({
    url: '/recommend/recom/sign',
    method: 'post',
    params: data
  })
}
// 处方确认签名
function CfconfirmSign(data) {
  return request({
    url: 'recommend/recom/confirm',
    method: 'post',
    params: data
  })
}
// 诊断搜索数据
function getDiagnoseData(data) {
  return request({
    url: '/search/diagnosis/findDiagnosisList',
    method: 'get',
    params: data
  })
}
// 药品搜索数据
function getDrugData(data) {
  return request({
    url: '/ad/product/search',
    method: 'POST',
    params: data
  })
}
// 药品的默认用法用量
function getDefaultUsage(data) {
  return request({
    url: '/ad/medication/getDefaultUsage',
    method: 'POST',
    params: data
  })
}
// 药品用法用量数据
function getUsage(data) {
  return request({
    url: '/ad/doctorInfo/globalConfig',
    method: 'get',
    params: data
  })
}
// mqtt
function getMqtt(data) {
  return request({
    url: '/im/connect/params',
    method: 'post',
    headers: { _v: '1.0.1' },
    params: data
  })
}
//互联网用户协议
function getAgreement(data) {
  return request({
    url: '/ad/agreement/1',
    method: 'get',
    params: data
  })
}
// 隐私政策
function getPrivacy(data) {
  return request({
    url: '/ad/agreement/3',
    method: 'get',
    params: data
  })
}
// 病历模板删除
function deleteTemplate(data) {
  return request({
    url: '/medical/emr/template/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
// 获取病历模版列表
function getTemplate(data) {
  return request({
    url: '/medical/emr/template/list',
    method: 'get',
    params: data
  })
}
// 获取病历模版列表
function getSettingList(data) {
  return request({
    url: '/medical/emr/setting/list',
    method: 'get',
    params: data
  })
}
// 保存病历设置
function saveSettingList(data) {
  return request({
    url: '/medical/emr/setting/edit',
    method: 'post',
    params: data
  })
}
// 保存病历模版
function saveTemplate(id, data) {
  return request({
    url: '/medical/emr/template/save?doctorId=' + id,
    method: 'post',
    data
  })
}
// 获取病历模版详情
function getTemplateDetail(data) {
  return request({
    url: '/medical/emr/template/detail',
    method: 'get',
    params: data
  })
}

function submitMeeting(data) {
  return request({
    url: '/vc/group/consult/apply',
    method: 'post',
    data
  })
}
// 获取会诊记录
function getMeetingInfo(data) {
  return request({
    url: '/vc/group/consult/info/' + data,
    method: 'get',
    params: {}
  })
}
// 获取会诊记录
function getMeetingApplyInfo(data) {
  return request({
    url: '/vc/group/consult/apply',
    method: 'get',
    params: data
  })
}
// 获取诊断列表
function getDoctorList(data) {
  return request({
    url: '/search/diagnosis/findDiagnosisList',
    method: 'get',
    params: data
  })
}
function submitSuggest(data) {
  return request({
    url: '/vc/group/consult/opinion',
    method: 'post',
    data

  })
}
// 挂断trtc视频
function meetingHangUp(data) {
  return request({
    url: '/vc/group/consult/finish/' + data,
    method: 'post',
    data: {}
  })
}

//申请意见签署确认
function opinionConfirm(data) {
  return request({
    url: '/vc/group/consult/opinion/confirm',
    method: 'post',
    data
  })
}

// 获取医院列表
function getHospitalList() {
  return request({
    url: '/vc/group/consult/partner/hospitals',
    method: 'get'
  })
}

// 获取医生列表
function getHospDoctorList(hospitalId) {
  return request({
    url: '/vc/group/consult/doctors?hospitalId=' + hospitalId,
    method: 'get'
  })
}
export default {
  getPatientList,
  getChatList,
  getPrescription,
  sendPrescription,
  rejectPrescription,
  searchDiagnosis,
  sendMsg,
  uploadURL,
  closeChat,
  searchDrugs,
  getNewImg,

  getCodeImg,
  getVerification,
  loginCms,
  getList,
  getDiseaseDetail,
  getCaseDetail,
  getKey,
  accepts,
  reasonList,
  refuse,
  hangUp,
  acceptsThird,
  getThirdNames,

  getLookCase,
  getLookPrescrip,
  submitCaseForm,
  getNoSecret,
  sign,
  confirmSign,
  submitPrescripForm,
  Cfsign,
  CfconfirmSign,
  getDiagnoseData,
  getDrugData,
  getDefaultUsage,
  getUsage,
  send,
  getMqtt,
  getAgreement,
  getPrivacy,
  getTemplate,
  getSettingList,
  saveSettingList,
  saveTemplate,
  getTemplateDetail,
  submitMeeting,
  getMeetingInfo,
  getDoctorList,
  submitSuggest,
  getMeetingList,
  getMeetingApplyInfo,
  meetingHangUp,
  opinionConfirm,
  initCaseInterface,
  deleteTemplate,
  getHospitalList,
  getHospDoctorList
}
