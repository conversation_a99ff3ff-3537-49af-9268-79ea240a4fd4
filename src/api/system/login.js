import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/pc/auth/login',
    method: 'post',
    data
  })
}

export function getCodeImg() {
  return request({
    url: '/pc/auth/code',
    method: 'get'
  })
}
export function getVerification(data) {
  return request({
    url: '/pc/auth/smscode',
    method: 'post',
    data
  })
}

export function checkLogin({ userName, faceId }) {
  return request({
    url: '/pc/auth/login/' + userName + '/' + faceId,
    method: 'post'
  })
}
