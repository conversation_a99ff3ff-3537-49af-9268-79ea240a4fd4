import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/patient/list',
    method: 'get',
    params: data
  })
}

export function get(patientId) {
  return request({
    url: '/user/patient/' + patientId,
    method: 'get'
  })
}

export function getCaseList(data) {
  return request({
    url: '/user/patient/case',
    method: 'get',
    params: data
  })
}
